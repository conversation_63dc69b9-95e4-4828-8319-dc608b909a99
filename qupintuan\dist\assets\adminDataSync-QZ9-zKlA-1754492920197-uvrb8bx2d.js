async function c(r){try{return{success:!0,mode:"production"}}catch(e){return console.error("❌ [AdminDataSync] 奖励领取数据同步失败:",e),{success:!1,error:e.message}}}async function s(r){try{return{success:!0,mode:"production"}}catch(e){return console.error("❌ [AdminDataSync] QPT数据同步失败:",e),{success:!1,error:e.message}}}async function t(r){try{return{success:!0,mode:"production"}}catch(e){return console.error("❌ [AdminDataSync] 房间状态数据同步失败:",e),{success:!1,error:e.message}}}export{s as triggerQPTSync,c as triggerRewardSync,t as triggerRoomSync};
