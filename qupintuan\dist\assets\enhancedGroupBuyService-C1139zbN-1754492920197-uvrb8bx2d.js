const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/groupBuyApi-BIzKylWd-1754492920197-uvrb8bx2d.js","assets/basicOperations-n8eaq0ha-1754492920197-uvrb8bx2d.js","assets/vendor-Caz4khA--1754492920197-uvrb8bx2d.js","assets/roomManagement-CI-AdwfD-1754492920197-uvrb8bx2d.js","assets/rewardOperations-B7ql8-Tq-1754492920197-uvrb8bx2d.js"])))=>i.map(i=>d[i]);
import{_ as f}from"./vendor-Caz4khA--1754492920197-uvrb8bx2d.js";import{C as s,A as u}from"./index-Bv97PTFG-1754492920197-bwlxnz5od.js";import{g as w,c as S,h as A,b as D}from"./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js";const R=6;function p(){return S({chain:D,transport:A()})}async function l(a,n){try{const r=p(),o=s[97].GroupBuyRoom,t=await r.readContract({address:o,abi:u.GroupBuyRoom,functionName:"getUserPaidAmount",args:[BigInt(a),n]});return w(t,R)}catch(r){return console.error("查询用户支付金额失败:",r),"0.00"}}async function T(a,n,r){try{const o=await l(a,n),t=parseFloat(o);return Math.abs(t-r)<=.01&&t>0}catch(o){return console.error("检查USDT支付记录失败:",o),!1}}async function _(a,n,r,o=null){try{const t=await T(a,r,n),e=await l(a,r),m=p(),d=s[97].GroupBuyRoom,c=await m.readContract({address:d,abi:u.GroupBuyRoom,functionName:"isParticipant",args:[BigInt(a),r]})&&parseFloat(e)===0;if(!t&&!c)throw new Error(`验证失败：未找到有效的USDT支付记录。期望金额: ${n} USDT，实际支付: ${e} USDT`);if(!o)return{success:!0,verified:!0,isHistoricalUser:c,hasPaymentRecord:t,paidAmount:e,message:"验证通过，但未执行交易（缺少签名者）"};const{claimReward:h}=await f(async()=>{const{claimReward:y}=await import("./groupBuyApi-BIzKylWd-1754492920197-uvrb8bx2d.js");return{claimReward:y}},__vite__mapDeps([0,1,2,3,4])),i=await h({chainId:97,roomId:a,signer:o});return{success:!0,receipt:i.receipt,hash:i.txHash,isHistoricalUser:c,hasPaymentRecord:t,paidAmount:e}}catch(t){throw console.error("增强退款失败:",t),t}}export{T as checkUSDTPaymentRecord,_ as claimWithUSDTValidation,l as getUserPaidAmount};
