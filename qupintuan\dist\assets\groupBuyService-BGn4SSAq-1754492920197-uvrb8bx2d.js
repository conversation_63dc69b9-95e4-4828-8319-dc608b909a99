import{c as t}from"./index-Bv97PTFG-1754492920197-bwlxnz5od.js";import{E as j,R as k,d as E,h as L,e as U,f as g,r as v}from"./index-Bv97PTFG-1754492920197-bwlxnz5od.js";import{createAndLock as r,fetchTotalRooms as e,fetchRoom as c,joinRoom as n,claimReward as m}from"./core-COSm1F4e-1754492920197-uvrb8bx2d.js";import"./vendor-Caz4khA--1754492920197-uvrb8bx2d.js";import"./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js";import"./basicOperations-n8eaq0ha-1754492920197-uvrb8bx2d.js";import"./roomManagement-CI-AdwfD-1754492920197-uvrb8bx2d.js";import"./rewardOperations-B7ql8-Tq-1754492920197-uvrb8bx2d.js";import"./transaction-DknE05b8-1754492920197-uvrb8bx2d.js";async function h(o,a){return r(o,a)}async function y(o){return e(o)}async function S(o,a){return c(o,a)}async function T(o){return n(o)}async function w(o){return m(o)}async function $(o){return t.fetchRooms(o)}export{j as ERROR_CODES,k as ROOM_STATUS,E as calculateRemainingTime,L as canUserClaimReward,U as canUserJoinRoom,w as claimReward,h as createAndLock,t as default,S as fetchRoom,$ as fetchRooms,y as fetchTotalRooms,g as formatRoomStatus,T as joinRoom,t as newGroupBuyService,v as recalculateRoomStatus};
