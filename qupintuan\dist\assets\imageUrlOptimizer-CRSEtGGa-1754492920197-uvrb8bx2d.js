const l={MAX_IMAGE_HASH_LEN:200,MAX_IMAGES:5,SAFE_LENGTH:180};function a(e){const t={valid:!0,totalImages:e.length,oversizedImages:[],maxLength:0,suggestions:[]};return e.forEach((s,o)=>{const n=s.length;t.maxLength=Math.max(t.maxLength,n),n>l.MAX_IMAGE_HASH_LEN&&(t.valid=!1,t.oversizedImages.push({index:o,url:s,length:n,excess:n-l.MAX_IMAGE_HASH_LEN}))}),t.valid||(t.suggestions.push("图片URL超过合约限制，需要优化"),t.suggestions.push("建议使用更短的IPFS网关或压缩图片")),t.maxLength>l.SAFE_LENGTH&&t.suggestions.push("建议将URL长度控制在180字符以内"),t}function c(e){if(!e.includes("ipfs"))return e;let t="";e.includes("/ipfs/")?t=e.split("/ipfs/")[1]:e.includes("ipfs://")?t=e.replace("ipfs://",""):e.includes(".ipfs.")&&(t=e.split(".ipfs.")[0].split("//")[1]),t=t.split("?")[0].split("#")[0];const s=`https://dweb.link/ipfs/${t}`;return console.log("🔧 [优化IPFS URL]"),console.log("  原始URL:",e,`(${e.length}字符)`),console.log("  优化URL:",s,`(${s.length}字符)`),console.log("  节省:",e.length-s.length,"字符"),s}function g(e){const t={original:e,optimized:[],savings:[],totalSavings:0,allValid:!0};return e.forEach((s,o)=>{const n=c(s),i=s.length-n.length;t.optimized.push(n),t.savings.push(i),t.totalSavings+=i,n.length>l.MAX_IMAGE_HASH_LEN&&(t.allValid=!1,console.warn(`⚠️ 图片${o+1}优化后仍超长: ${n.length}字符`))}),t}function u(e){let t=0;for(let o=0;o<e.length;o++){const n=e.charCodeAt(o);t=(t<<5)-t+n,t=t&t}const s=`short_${Math.abs(t).toString(16)}`;return console.log("🔧 [生成短哈希]"),console.log("  原始URL:",e,`(${e.length}字符)`),console.log("  短哈希:",s,`(${s.length}字符)`),s}function d(e){if(a(e).valid)return{success:!0,optimized:e,method:"no_optimization_needed",savings:0};const s=g(e);if(console.log("🔧 [IPFS优化]",s),s.allValid)return console.log("✅ IPFS优化成功，所有URL符合限制"),{success:!0,optimized:s.optimized,method:"ipfs_optimization",savings:s.totalSavings};console.log("⚠️ IPFS优化后仍有URL超长，使用短哈希方案");const o=e.map(i=>i.length>l.MAX_IMAGE_HASH_LEN?u(i):c(i));return{success:a(o).valid,optimized:o,method:"short_hash_fallback",savings:e.reduce((i,h,r)=>i+(h.length-o[r].length),0),warning:"使用了短哈希方案，可能影响图片显示"}}async function p(e){try{if(e.length===0)throw new Error("至少需要一张图片");if(e.length>l.MAX_IMAGES)throw new Error(`最多只能上传${l.MAX_IMAGES}张图片`);const t=d(e);if(!t.success)throw new Error("无法将图片URL优化到合约限制范围内");return{success:!0,optimizedUrls:t.optimized,method:t.method,savings:t.savings,warning:t.warning}}catch(t){return console.error("❌ [合约验证] 验证失败:",t.message),{success:!1,error:t.message,originalUrls:e}}}export{l as CONTRACT_LIMITS,a as checkImageUrlLengths,u as generateShortHash,g as optimizeImageUrls,c as optimizeIpfsUrl,d as smartOptimizeImageUrls,p as validateAndOptimizeForContract};
