const a={LOCAL_STORAGE:"lottery_",SESSION_STORAGE:"lottery_session_",INDEXED_DB:"lottery_db_",URL_HASH:"lottery_hash_",COOKIE:"lottery_cookie_"};async function _(t,o){const r={...o,savedAt:Date.now(),version:"1.0"},e={localStorage:!1,sessionStorage:!1,indexedDB:!1,urlHash:!1,cookie:!1};try{localStorage.setItem(`${a.LOCAL_STORAGE}${t}`,JSON.stringify(r)),e.localStorage=!0}catch{}try{sessionStorage.setItem(`${a.SESSION_STORAGE}${t}`,JSON.stringify(r)),e.sessionStorage=!0}catch{}try{await S(t,r),e.indexedDB=!0}catch{}try{h(t,r),e.urlHash=!0}catch{}try{w(t,r),e.cookie=!0}catch{}return e}async function p(t){const o=[{name:"localStorage",loader:()=>u(t)},{name:"sessionStorage",loader:()=>d(t)},{name:"indexedDB",loader:()=>y(t)},{name:"urlHash",loader:()=>O(t)},{name:"cookie",loader:()=>g(t)}];for(const r of o)try{const e=await r.loader();if(e&&e.txHash&&e.winner)return e}catch{}return null}async function $(t){const o=[()=>localStorage.removeItem(`${a.LOCAL_STORAGE}${t}`),()=>sessionStorage.removeItem(`${a.SESSION_STORAGE}${t}`),()=>f(t),()=>m(t),()=>D(t)];for(const r of o)try{await r()}catch{}}function u(t){const o=localStorage.getItem(`${a.LOCAL_STORAGE}${t}`);return o?JSON.parse(o):null}function d(t){const o=sessionStorage.getItem(`${a.SESSION_STORAGE}${t}`);return o?JSON.parse(o):null}async function S(t,o){return new Promise((r,e)=>{const n=indexedDB.open("LotteryDB",1);n.onerror=()=>e(n.error),n.onsuccess=()=>{const c=n.result.transaction(["lottery"],"readwrite").objectStore("lottery").put({roomId:t,data:o,timestamp:Date.now()});c.onsuccess=()=>r(),c.onerror=()=>e(c.error)},n.onupgradeneeded=()=>{const i=n.result;i.objectStoreNames.contains("lottery")||i.createObjectStore("lottery",{keyPath:"roomId"}).createIndex("timestamp","timestamp",{unique:!1})}})}async function y(t){return new Promise((o,r)=>{const e=indexedDB.open("LotteryDB",1);e.onerror=()=>r(e.error),e.onsuccess=()=>{const s=e.result.transaction(["lottery"],"readonly").objectStore("lottery").get(t);s.onsuccess=()=>{const c=s.result;o(c?c.data:null)},s.onerror=()=>r(s.error)}})}async function f(t){return new Promise((o,r)=>{const e=indexedDB.open("LotteryDB",1);e.onsuccess=()=>{const s=e.result.transaction(["lottery"],"readwrite").objectStore("lottery").delete(t);s.onsuccess=()=>o(),s.onerror=()=>r(s.error)}})}function h(t,o){const r=btoa(JSON.stringify({[t]:o})),e=window.location.hash;e.includes("lottery=")?e.replace(/lottery=[^&]*/,`lottery=${r}`):(`${e}`,`${r}`),window._lotteryHashData=window._lotteryHashData||{},window._lotteryHashData[t]=o}function O(t){return window._lotteryHashData?.[t]||null}function m(t){window._lotteryHashData&&delete window._lotteryHashData[t]}function w(t,o){const r={txHash:o.txHash,winner:o.winner,timestamp:o.timestamp,roomId:o.roomId},e=btoa(JSON.stringify(r)),n=new Date(Date.now()+10080*60*1e3);document.cookie=`${a.COOKIE}${t}=${e}; expires=${n.toUTCString()}; path=/`}function g(t){const o=document.cookie.split(";"),r=`${a.COOKIE}${t}=`;for(let e of o)if(e=e.trim(),e.startsWith(r))try{const n=e.substring(r.length);return JSON.parse(atob(n))}catch{}return null}function D(t){document.cookie=`${a.COOKIE}${t}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`}export{$ as clearLotteryInfo,p as loadLotteryInfo,_ as saveLotteryInfo};
