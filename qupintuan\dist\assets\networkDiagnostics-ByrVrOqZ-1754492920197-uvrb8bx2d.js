const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-Bv97PTFG-1754492920197-bwlxnz5od.js","assets/vendor-Caz4khA--1754492920197-uvrb8bx2d.js","assets/web3-BiUUTWDm-1754492920197-uvrb8bx2d.js","assets/index-DM4lriVt-1754492920197-5l2r91brv.css"])))=>i.map(i=>d[i]);
import{_ as a}from"./vendor-Caz4khA--1754492920197-uvrb8bx2d.js";async function u(){const e={timestamp:new Date().toISOString(),environment:"production",hostname:typeof window<"u"?window.location.hostname:"unknown",userAgent:typeof navigator<"u"?navigator.userAgent:"unknown",rpcTests:[],contractTests:[],summary:{totalRpcTested:0,availableRpc:0,contractCallSuccess:!1,recommendedAction:""}},o=["https://bsc-testnet.public.blastapi.io","https://data-seed-prebsc-1-s1.binance.org:8545","https://data-seed-prebsc-2-s1.binance.org:8545","https://bsc-testnet.blockpi.network/v1/rpc/public"];for(const t of o){const s=await m(t);e.rpcTests.push(s),e.summary.totalRpcTested++,s.success&&e.summary.availableRpc++}if(e.summary.availableRpc>0){const t=await p();e.contractTests.push(t),e.summary.contractCallSuccess=t.success}return e.summary.recommendedAction=d(e),e}async function m(e){const o=Date.now(),t={url:e,success:!1,responseTime:0,error:null,blockNumber:null};try{const s=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({jsonrpc:"2.0",method:"eth_blockNumber",params:[],id:1}),signal:AbortSignal.timeout(1e4)});if(t.responseTime=Date.now()-o,s.ok){const c=await s.json();c.result?(t.success=!0,t.blockNumber=parseInt(c.result,16)):t.error=c.error?.message||"Invalid response"}else t.error=`HTTP ${s.status}: ${s.statusText}`}catch(s){t.responseTime=Date.now()-o,t.error=s.message}return t}async function p(){const e={type:"contract_call",success:!1,error:null,responseTime:0},o=Date.now();try{const{getContractAddress:t}=await a(async()=>{const{getContractAddress:n}=await import("./index-Bv97PTFG-1754492920197-bwlxnz5od.js").then(r=>r.j);return{getContractAddress:n}},__vite__mapDeps([0,1,2,3])),{ABIS:s}=await a(async()=>{const{ABIS:n}=await import("./index-Bv97PTFG-1754492920197-bwlxnz5od.js").then(r=>r.k);return{ABIS:n}},__vite__mapDeps([0,1,2,3])),{config:c}=await a(async()=>{const{config:n}=await import("./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js").then(r=>r.D);return{config:n}},__vite__mapDeps([2,1])),{readContract:i}=await a(async()=>{const{readContract:n}=await import("./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js").then(r=>r.E);return{readContract:n}},__vite__mapDeps([2,1])),l=t(97,"AgentSystem");await i(c,{address:l,abi:s.AgentSystem,functionName:"systemAdmin"}),e.success=!0,e.responseTime=Date.now()-o}catch(t){e.responseTime=Date.now()-o,e.error=t.message}return e}function d(e){const{summary:o}=e;return o.availableRpc===0?"所有RPC节点都不可用，请检查网络连接或防火墙设置":o.availableRpc<o.totalRpcTested/2?"部分RPC节点不可用，建议检查网络稳定性":o.contractCallSuccess?"网络连接正常，所有测试通过":"RPC连接正常但合约调用失败，可能是合约地址或ABI问题"}function g(e){console.group("🔍 网络诊断报告"),console.log("📊 基本信息:"),console.log(`  环境: ${e.environment}`),console.log(`  主机: ${e.hostname}`),console.log(`  时间: ${e.timestamp}`),console.log(`
🌐 RPC节点测试:`),e.rpcTests.forEach(o=>{const t=o.success?"✅":"❌";console.log(`  ${t} ${o.url}`),console.log(`     响应时间: ${o.responseTime}ms`),o.blockNumber&&console.log(`     区块高度: ${o.blockNumber}`),o.error&&console.log(`     错误: ${o.error}`)}),console.log(`
📋 合约测试:`),e.contractTests.forEach(o=>{const t=o.success?"✅":"❌";console.log(`  ${t} 合约调用测试`),console.log(`     响应时间: ${o.responseTime}ms`),o.error&&console.log(`     错误: ${o.error}`)}),console.log(`
💡 建议:`),console.log(`  ${e.summary.recommendedAction}`),console.groupEnd()}async function y(){try{const e=await u();(e.summary.availableRpc===0||!e.summary.contractCallSuccess)&&g(e)}catch(e){console.error("❌ 网络诊断失败:",e)}}export{y as autoRunDiagnosticsOnError,u as diagnoseNetworkConnection,g as printDiagnosticReport};
