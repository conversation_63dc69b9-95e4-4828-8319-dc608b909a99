import{g as i,A as u}from"./index-Bv97PTFG-1754492920197-bwlxnz5od.js";import{c as m,h as d,b as f}from"./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js";import"./vendor-Caz4khA--1754492920197-uvrb8bx2d.js";const p=t=>!t||t===0?0:Number(t)||0,g=m({chain:f,transport:d("https://data-seed-prebsc-1-s1.binance.org:8545",{batch:!0,fetchOptions:{timeout:1e4},retryCount:2,retryDelay:1e3})}),n=new Map,s=3e4;function h(t,...e){return`${t}_${JSON.stringify(e)}`}function l(t){const e=n.get(t);return e&&Date.now()-e.timestamp<s?e.data:(n.delete(t),null)}function b(t,e){n.set(t,{data:e,timestamp:Date.now()})}function C(t){return p(t)}async function A(t){const e=h("productInfo",t),c=l(e);if(c)return c;try{const o=i(97,"ProductManagement"),r=await g.readContract({address:o,abi:u.ProductManagement,functionName:"products",args:[BigInt(t)]}),a={productId:Number(r[0]),merchant:r[1],name:r[2],description:r[3],price:C(r[4]),stock:Number(r[5]),isActive:r[6],sales:Number(r[7]),priceFixed:!0,originalPrice:r[4].toString()};return b(e,a),a}catch(o){throw console.error("🚨 [getOptimizedProductInfo] 获取商品信息失败:",o),new Error(`获取商品信息失败: ${o.message}`)}}function w(){const t=Date.now();for(const[e,c]of n.entries())t-c.timestamp>s&&n.delete(e)}setInterval(w,6e4);export{w as clearExpiredCache,A as getOptimizedProductInfo};
