const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/rpcManager-B8UK2u_q-1754492920197-uvrb8bx2d.js","assets/vendor-Caz4khA--1754492920197-uvrb8bx2d.js","assets/index-Bv97PTFG-1754492920197-bwlxnz5od.js","assets/web3-BiUUTWDm-1754492920197-uvrb8bx2d.js","assets/index-DM4lriVt-1754492920197-5l2r91brv.css"])))=>i.map(i=>d[i]);
import{_ as l}from"./vendor-Caz4khA--1754492920197-uvrb8bx2d.js";const r={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_ADDRESSMANAGEMENT_ADDRESS:"0x75c7e4afF2363E96ce5A584E58A4281f6ed5414f",VITE_ADMIN_ADDRESS:"******************************************",VITE_AGENTSYSTEM_ADDRESS:"******************************************",VITE_APP_DOMAIN:"https://test.qupintuan.fun",VITE_APP_ENV:"production",VITE_BSC_RPC_URL:"https://data-seed-prebsc-1-s1.binance.org:8545",VITE_BSC_TESTNET_RPC_URL:"https://data-seed-prebsc-1-s1.binance.org:8545",VITE_BUYBACK_ADMIN_ADDRESS:"******************************************",VITE_CHAIN_ID:"97",VITE_ENABLE_DEBUG_LOGS:"false",VITE_FEESPLITMANAGER_ADDRESS:"******************************************",VITE_GROUPBUY_ROOM_ADDRESS:"******************************************",VITE_INFURA_PROJECT_ID:"your_infura_project_id_here",VITE_INFURA_PROJECT_SECRET:"your_infura_project_secret_here",VITE_MERCHANT_MANAGEMENT_ADDRESS:"******************************************",VITE_MULTISIGWALLET_ADDRESS:"******************************************",VITE_MYTIMELOCK_ADDRESS:"******************************************",VITE_NETWORK:"testnet",VITE_NETWORK_NAME:"BSC Testnet",VITE_NODE_STAKING_ADDRESS:"******************************************",VITE_ORDER_MANAGEMENT_ADDRESS:"******************************************",VITE_PINATA_JWT:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tbzk-sVCNLLUhpD8cWLJCF0MW-7gB1mst7lgT_A7SQU",VITE_PINATA_JWT_1:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tbzk-sVCNLLUhpD8cWLJCF0MW-7gB1mst7lgT_A7SQU",VITE_PINATA_JWT_2:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CGP1iRop3JxdL82wpi2xwpjT9VvUZJgW6zywhMBs08Y",VITE_PINATA_JWT_3:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JuBxPIVqMSDWGaHQkTzbY_ReJGI06eeyxvAppxmTmGE",VITE_POINTS_MANAGEMENT_ADDRESS:"0x29bC33F518d741DC45c1857B6ee8f3F529E594cd",VITE_PRODUCT_MANAGEMENT_ADDRESS:"0xe91cc0ED2e94D8918FDe09585104824de824E756",VITE_QPTLOCKER_ADDRESS:"0xcca28B4d5005819107948fe279893435a2A1728e",VITE_QPT_BUYBACK_ADDRESS:"0x23449063C3Fc3cc6361ef93Af4b602E0fC420dCE",VITE_QPT_TOKEN_ADDRESS:"0x1Ed10648278333D71E2d9fd880998bB775Ec221A",VITE_QPT_TOKEN_ADDRESS_MAINNET:"0x…",VITE_QPT_TOKEN_ADDRESS_TESTNET:"0x1Ed10648278333D71E2d9fd880998bB775Ec221A",VITE_RPC_URL_MAINNET:"https://bsc-dataseed1.binance.org",VITE_RPC_URL_TESTNET:"https://data-seed-prebsc-1-s1.binance.org:8545",VITE_RPC_URL_TESTNET_BACKUP1:"https://data-seed-prebsc-2-s1.binance.org:8545",VITE_RPC_URL_TESTNET_BACKUP2:"https://data-seed-prebsc-1-s2.binance.org:8545",VITE_RPC_URL_TESTNET_BACKUP3:"https://bsc-testnet.blockpi.network/v1/rpc/public",VITE_SIMPLE_QUERY_ADDRESS:"******************************************",VITE_SYSTEM_ADMIN_ADDRESS:"******************************************",VITE_USDT_ADDRESS_MAINNET:"******************************************",VITE_USDT_ADDRESS_TESTNET:"******************************************",VITE_USDT_TOKEN_ADDRESS:"******************************************"};async function E(){const t={timestamp:new Date().toISOString(),environment:"production",checks:{rpcConnectivity:null,contractAccess:null,walletConnection:null,environmentConfig:null},overall:{status:"unknown",issues:[],recommendations:[]}};try{return t.checks.rpcConnectivity=await _(),t.checks.contractAccess=await T(),t.checks.environmentConfig=await N(),t.checks.walletConnection=await d(),t.overall=h(t.checks),t}catch(e){return console.error("❌ [productionHealthCheck] 健康检查失败:",e),t.overall.status="error",t.overall.issues.push(`健康检查执行失败: ${e.message}`),t}}async function _(){const t={status:"unknown",details:{},responseTime:0,error:null},e=Date.now();try{const{getRpcUrl:s}=await l(async()=>{const{getRpcUrl:a}=await import("./rpcManager-B8UK2u_q-1754492920197-uvrb8bx2d.js");return{getRpcUrl:a}},__vite__mapDeps([0,1])),n=await s(97),o=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({jsonrpc:"2.0",method:"eth_blockNumber",params:[],id:1}),signal:AbortSignal.timeout(1e4)});if(t.responseTime=Date.now()-e,o.ok){const a=await o.json();a.result?(t.status="healthy",t.details={rpcUrl:n,blockNumber:parseInt(a.result,16),responseTime:t.responseTime}):(t.status="unhealthy",t.error="Invalid RPC response")}else t.status="unhealthy",t.error=`HTTP ${o.status}`}catch(s){t.responseTime=Date.now()-e,t.status="unhealthy",t.error=s.message}return t}async function T(){const t={status:"unknown",details:{},error:null};try{const{getContractAddress:e}=await l(async()=>{const{getContractAddress:c}=await import("./index-Bv97PTFG-1754492920197-bwlxnz5od.js").then(i=>i.j);return{getContractAddress:c}},__vite__mapDeps([2,1,3,4])),{ABIS:s}=await l(async()=>{const{ABIS:c}=await import("./index-Bv97PTFG-1754492920197-bwlxnz5od.js").then(i=>i.k);return{ABIS:c}},__vite__mapDeps([2,1,3,4])),{config:n}=await l(async()=>{const{config:c}=await import("./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js").then(i=>i.D);return{config:c}},__vite__mapDeps([3,1])),{readContract:o}=await l(async()=>{const{readContract:c}=await import("./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js").then(i=>i.E);return{readContract:c}},__vite__mapDeps([3,1])),a=e(97,"AgentSystem"),I=await o(n,{address:a,abi:s.AgentSystem,functionName:"systemAdmin"});t.status="healthy",t.details={contractAddress:a,systemAdmin:I,contractType:"AgentSystem"}}catch(e){t.status="unhealthy",t.error=e.message}return t}async function N(){const t={status:"unknown",details:{},issues:[]};try{const e={mode:"production",chainId:"97",rpcUrl:"https://data-seed-prebsc-1-s1.binance.org:8545",agentSystemAddress:"******************************************"};t.details=e;const s=["VITE_CHAIN_ID","VITE_RPC_URL_TESTNET","VITE_AGENTSYSTEM_ADDRESS"];for(const n of s)r[n]||t.issues.push(`缺少环境变量: ${n}`);t.status=t.issues.length===0?"healthy":"unhealthy"}catch(e){t.status="unhealthy",t.error=e.message}return t}async function d(){const t={status:"unknown",details:{},error:null};try{typeof window<"u"&&window.ethereum?(t.status="available",t.details={provider:"detected",chainId:window.ethereum.chainId}):(t.status="unavailable",t.details={provider:"not_detected"})}catch(e){t.status="error",t.error=e.message}return t}function h(t){const e={status:"healthy",issues:[],recommendations:[]};return Object.entries(t).forEach(([s,n])=>{n&&n.status==="unhealthy"&&(e.status="unhealthy",e.issues.push(`${s}: ${n.error||"检查失败"}`))}),t.rpcConnectivity?.status==="unhealthy"&&e.recommendations.push("检查网络连接和RPC节点配置"),t.contractAccess?.status==="unhealthy"&&e.recommendations.push("检查合约地址和ABI配置"),t.environmentConfig?.status==="unhealthy"&&e.recommendations.push("检查环境变量配置"),e.issues.length===0&&(e.status="healthy"),e}function D(t){console.group("🏥 生产环境健康检查报告"),console.log(`📊 总体状态: ${t.overall.status==="healthy"?"✅ 健康":"❌ 异常"}`),console.log(`🕐 检查时间: ${t.timestamp}`),console.log(`🌍 运行环境: ${t.environment}`),console.log(`
📋 详细检查结果:`),Object.entries(t.checks).forEach(([e,s])=>{if(s){const n=s.status==="healthy"?"✅":s.status==="unhealthy"?"❌":"⚠️";console.log(`  ${n} ${e}: ${s.status}`),s.error&&console.log(`     错误: ${s.error}`),s.details&&Object.keys(s.details).length>0&&console.log("     详情:",s.details)}}),t.overall.issues.length>0&&(console.log(`
⚠️ 发现的问题:`),t.overall.issues.forEach(e=>{console.log(`  • ${e}`)})),t.overall.recommendations.length>0&&(console.log(`
💡 建议:`),t.overall.recommendations.forEach(e=>{console.log(`  • ${e}`)})),console.groupEnd()}async function C(){try{const t=await E();return t.overall.status!=="healthy"&&D(t),t}catch(t){return console.error("❌ 自动健康检查失败:",t),null}return null}export{C as autoRunHealthCheck,D as printHealthCheckReport,E as runProductionHealthCheck};
