class n{constructor(){this.operations=new Map,this.isInitialized=!1}initialize(){if(!this.isInitialized)try{const t=localStorage.getItem("qpt_operations");if(t){const o=JSON.parse(t);this.operations=new Map(o)}this.isInitialized=!0}catch(t){console.warn("⚠️ QPT操作跟踪器初始化失败:",t),this.operations=new Map,this.isInitialized=!0}}recordQPTLock(t,o,e,r={}){try{this.initialize();const i={id:`${t}_${o}_${Date.now()}`,type:"lock",category:t,userAddress:o.toLowerCase(),amount:e,timestamp:new Date().toISOString(),metadata:r};return this.operations.set(i.id,i),this._saveToStorage(),i.id}catch(i){return console.warn("⚠️ 记录QPT锁仓失败:",i),null}}recordQPTUnlock(t,o,e,r={}){try{this.initialize();const i={id:`${t}_${o}_${Date.now()}`,type:"unlock",category:t,userAddress:o.toLowerCase(),amount:e,timestamp:new Date().toISOString(),metadata:r};return this.operations.set(i.id,i),this._saveToStorage(),i.id}catch(i){return console.warn("⚠️ 记录QPT解锁失败:",i),null}}getUserOperations(t){this.initialize();const o=[];for(const[e,r]of this.operations)r.userAddress===t.toLowerCase()&&o.push(r);return o.sort((e,r)=>new Date(r.timestamp)-new Date(e.timestamp))}getStats(){this.initialize();let t=0,o=0,e=0,r=0;for(const[i,a]of this.operations)a.type==="lock"?(t++,e+=parseFloat(a.amount||0)):a.type==="unlock"&&(o++,r+=parseFloat(a.amount||0));return{totalOperations:this.operations.size,totalLocks:t,totalUnlocks:o,totalLockAmount:e.toFixed(2),totalUnlockAmount:r.toFixed(2),netLocked:(e-r).toFixed(2)}}clear(){this.operations.clear(),this._saveToStorage()}_saveToStorage(){try{const t=Array.from(this.operations.entries());localStorage.setItem("qpt_operations",JSON.stringify(t))}catch(t){console.warn("⚠️ 保存QPT操作记录失败:",t)}}}const c=new n,l=(s,t,o,e)=>c.recordQPTUnlock(s,t,o,e);export{n as QPTOperationTracker,c as qptTracker,l as recordQPTUnlock};
