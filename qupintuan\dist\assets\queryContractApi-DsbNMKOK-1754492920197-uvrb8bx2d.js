const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-Bv97PTFG-1754492920197-bwlxnz5od.js","assets/vendor-Caz4khA--1754492920197-uvrb8bx2d.js","assets/web3-BiUUTWDm-1754492920197-uvrb8bx2d.js","assets/index-DM4lriVt-1754492920197-5l2r91brv.css"])))=>i.map(i=>d[i]);
import{_ as u}from"./vendor-Caz4khA--1754492920197-uvrb8bx2d.js";import{r as T,a as b}from"./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js";const f="0x7713b0C625054643D73F294d101e742078fF69f6",h=[{inputs:[{internalType:"address",name:"user",type:"address"}],name:"getUserCompleteStatus",outputs:[{internalType:"address",name:"user",type:"address"},{internalType:"uint256",name:"totalInvested",type:"uint256"},{internalType:"uint256",name:"totalReturns",type:"uint256"},{internalType:"uint256",name:"currentNodes",type:"uint256"},{internalType:"uint256",name:"maxNodes",type:"uint256"},{internalType:"uint256",name:"nodeLevel",type:"uint256"},{internalType:"uint256",name:"totalReferrals",type:"uint256"},{internalType:"uint256",name:"totalReferralRewards",type:"uint256"},{internalType:"uint256",name:"availableRewards",type:"uint256"},{internalType:"uint256",name:"totalWithdrawn",type:"uint256"},{internalType:"uint256",name:"lastRewardTime",type:"uint256"},{internalType:"uint256",name:"nodeActivationTime",type:"uint256"},{internalType:"bool",name:"isActive",type:"bool"},{internalType:"uint256",name:"pendingRewards",type:"uint256"},{internalType:"uint256",name:"claimableRewards",type:"uint256"},{internalType:"uint256",name:"nextRewardTime",type:"uint256"},{internalType:"uint256",name:"rewardRate",type:"uint256"},{internalType:"uint256",name:"stakingPower",type:"uint256"},{internalType:"uint256",name:"totalStaked",type:"uint256"},{internalType:"uint256",name:"unlockTime",type:"uint256"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"uint256",name:"roomId",type:"uint256"},{internalType:"address",name:"user",type:"address"}],name:"getUserRoleInRoom",outputs:[{components:[{internalType:"address",name:"user",type:"address"},{internalType:"uint256",name:"roomId",type:"uint256"},{internalType:"bool",name:"hasJoined",type:"bool"},{internalType:"bool",name:"hasClaimed",type:"bool"},{internalType:"bool",name:"isCreator",type:"bool"},{internalType:"bool",name:"isParticipant",type:"bool"},{internalType:"bool",name:"isWinner",type:"bool"},{internalType:"bool",name:"canClaim",type:"bool"},{internalType:"uint256",name:"participantIndex",type:"uint256"},{internalType:"bool",name:"roomClosed",type:"bool"},{internalType:"bool",name:"roomSuccessful",type:"bool"},{internalType:"address",name:"winnerAddress",type:"address"}],internalType:"struct SimpleQueryContract.RoomRoleInfo",name:"roleInfo",type:"tuple"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"uint256",name:"roomId",type:"uint256"},{internalType:"address",name:"user",type:"address"}],name:"checkClaimEligibility",outputs:[{internalType:"bool",name:"canClaim",type:"bool"},{internalType:"string",name:"reason",type:"string"}],stateMutability:"view",type:"function"}];async function w(t){try{const n=await T(b,{address:f,abi:h,functionName:"getUserCompleteStatus",args:[t]}),[l,e,m,a,r,c,o,i,g,s,R,N,p,I,C,A,k,v,y,d]=n;return{user:l,agentInfo:{inviter:"0x0000000000000000000000000000000000000000",level:Number(c),totalPerformance:Number(e),referralsCount:Number(o),isRegistered:Number(o)>0||Number(e)>0,personalPerformance:Number(e),hasAdminRole:!1,hasUploaderRole:!1,isSystemAdmin:!1},pointsInfo:{groupBuyPoints:0,salesPoints:0,lastExchangeTime:0,canExchangeGroupBuy:!1,canExchangeSales:!1,canExchangeNow:!1,exchangeCooldownRemaining:0},nodeInfo:{isActive:p,lastClaimDate:Number(R),requiredStakeAmount:Number(y),hasClaimedToday:!1,canClaimToday:Number(C)>0,totalActiveNodes:Number(a),maxNodes:Number(r),canStakeNode:!p&&Number(a)<Number(r)},lockerInfo:{totalLocked:Number(y),totalUnlocked:0,pendingUnlock:0,nextUnlockTime:Number(d),canUnlock:Number(d)>0&&Date.now()/1e3>Number(d)},buybackInfo:{currentRound:0,hasParticipatedCurrentRound:!1}}}catch{return{user:t,agentInfo:{inviter:"0x0000000000000000000000000000000000000000",level:0,totalPerformance:0,referralsCount:0,isRegistered:!1,personalPerformance:0,hasAdminRole:!1,hasUploaderRole:!1,isSystemAdmin:!1},pointsInfo:{groupBuyPoints:0,salesPoints:0,lastExchangeTime:0,canExchangeGroupBuy:!1,canExchangeSales:!1,canExchangeNow:!1,exchangeCooldownRemaining:0},nodeInfo:{isActive:!1,lastClaimDate:0,requiredStakeAmount:0,hasClaimedToday:!1,canClaimToday:!1,totalActiveNodes:0,maxNodes:0,canStakeNode:!1},lockerInfo:{totalLocked:0,totalUnlocked:0,pendingUnlock:0,nextUnlockTime:0,canUnlock:!1},buybackInfo:{currentRound:0,hasParticipatedCurrentRound:!1}}}}async function P(t){try{const{getContractAddress:n}=await u(async()=>{const{getContractAddress:a}=await import("./index-Bv97PTFG-1754492920197-bwlxnz5od.js").then(r=>r.j);return{getContractAddress:a}},__vite__mapDeps([0,1,2,3])),{ABIS:l}=await u(async()=>{const{ABIS:a}=await import("./index-Bv97PTFG-1754492920197-bwlxnz5od.js").then(r=>r.k);return{ABIS:a}},__vite__mapDeps([0,1,2,3])),e=n(97,"AgentSystem");return(await T(b,{address:e,abi:l.AgentSystem,functionName:"getUserInfo",args:[t]}))[4]}catch(n){return console.error("❌ [QueryContract] 检查用户注册状态失败:",n),!1}}async function U(t){try{const{readContract:n}=await u(async()=>{const{readContract:o}=await import("./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js").then(i=>i.y);return{readContract:o}},__vite__mapDeps([2,1])),{config:l}=await u(async()=>{const{config:o}=await import("./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js").then(i=>i.D);return{config:o}},__vite__mapDeps([2,1]));let e;try{e=(await w(t)).agentInfo}catch{const i="0x9096769B22B53A464D40265420b9Ed5342b6ACb3";if(i){const s=await n(l,{address:i,abi:[{inputs:[{internalType:"address",name:"user",type:"address"}],name:"getUserInfo",outputs:[{internalType:"address",name:"inviter",type:"address"},{internalType:"uint8",name:"level",type:"uint8"},{internalType:"uint256",name:"totalPerformance",type:"uint256"},{internalType:"uint256",name:"referralsCount",type:"uint256"},{internalType:"bool",name:"isRegistered",type:"bool"},{internalType:"uint256",name:"personalPerformance",type:"uint256"}],stateMutability:"view",type:"function"}],functionName:"getUserInfo",args:[t]});e={inviter:s[0],level:Number(s[1]),totalPerformance:Number(s[2]),referralsCount:Number(s[3]),isRegistered:s[4],personalPerformance:Number(s[5]),hasAdminRole:!1,hasUploaderRole:!1,isSystemAdmin:!1}}}const m={groupBuyPoints:0,salesPoints:0,lastExchangeTime:0,canExchangeGroupBuy:!1,canExchangeSales:!1,canExchangeNow:!1,exchangeCooldownRemaining:0},a={isActive:!1,lastClaimDate:0,requiredStakeAmount:0,hasClaimedToday:!1,canClaimToday:!1,totalActiveNodes:0,maxNodes:0,canStakeNode:!1},r={totalLocked:0,totalUnlocked:0,pendingUnlock:0,nextUnlockTime:0,canUnlock:!1},c={currentRound:0,hasParticipatedCurrentRound:!1};return{user:t,inviter:e.inviter,level:e.level,totalPerformance:e.totalPerformance,referralsCount:e.referralsCount,isRegistered:e.isRegistered,personalPerformance:e.personalPerformance,hasAdminRole:e.hasAdminRole,hasUploaderRole:e.hasUploaderRole,isSystemAdmin:e.isSystemAdmin,pointsInfo:m,nodeInfo:a,lockerInfo:r,buybackInfo:c}}catch(n){throw console.error("❌ [QueryContract] 获取用户信息失败:",n),n}}export{P as checkUserRegistered,w as getUserCompleteStatus,U as getUserInfo};
