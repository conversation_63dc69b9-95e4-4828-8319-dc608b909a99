const r={TIMEOUT:"timeout",HTTP_400:"http_400",HTTP_403:"http_403",HTTP_429:"http_429",HTTP_500:"http_500",NETWORK_ERROR:"network_error",CORS_ERROR:"cors_error",UNKNOWN:"unknown"};function R(s){if(!s)return r.UNKNOWN;const t=s.message||"",e=s.status||s.code;return e===400||t.includes("400")?r.HTTP_400:e===403||t.includes("403")?r.HTTP_403:e===429||t.includes("429")||t.includes("rate limit")?r.HTTP_429:e>=500||t.includes("500")||t.includes("Internal Server Error")?r.HTTP_500:t.includes("timeout")||t.includes("TIMEOUT")?r.TIMEOUT:t.includes("Failed to fetch")||t.includes("Network Error")?r.NETWORK_ERROR:t.includes("CORS")||t.includes("cross-origin")?r.CORS_ERROR:r.UNKNOWN}function _(s,t=""){const e=t?` (节点: ${t.split("/")[2]})`:"";switch(s){case r.HTTP_400:return`RPC节点请求格式错误${e}，正在尝试其他节点...`;case r.HTTP_403:return`RPC节点访问被拒绝${e}，可能是IP限制，正在尝试其他节点...`;case r.HTTP_429:return`RPC节点请求频率过高${e}，正在尝试其他节点...`;case r.HTTP_500:return`RPC节点服务器错误${e}，正在尝试其他节点...`;case r.TIMEOUT:return`RPC节点响应超时${e}，正在尝试其他节点...`;case r.NETWORK_ERROR:return`网络连接错误${e}，请检查网络连接`;case r.CORS_ERROR:return`跨域请求被阻止${e}，这可能是浏览器安全策略导致的`;default:return`RPC连接出现未知错误${e}，正在尝试其他节点...`}}function E(s){switch(s){case r.TIMEOUT:case r.HTTP_429:case r.HTTP_500:case r.NETWORK_ERROR:return!0;case r.HTTP_400:case r.HTTP_403:case r.CORS_ERROR:return!1;default:return!0}}function P(s,t){switch(s){case r.HTTP_429:return 1e3*Math.pow(2,t)*2;case r.TIMEOUT:return 1e3*t;case r.HTTP_500:return 1e3*Math.pow(2,t);default:return 1e3*t}}async function h(s,t={}){const{maxRetries:e=3,onError:n=null,onRetry:a=null,context:f="RPC调用"}=t;let i=null;for(let c=1;c<=e;c++)try{return await s()}catch(o){i=o;const u=R(o);if(_(u),n&&n(o,u,c),c===e||!E(u))throw o;const l=P(u,c);a&&a(c,l,u),await new Promise(T=>setTimeout(T,l))}throw i}class O{constructor(){this.stats=new Map}record(t,e){const n=t||"unknown";this.stats.has(n)||this.stats.set(n,{total:0,errors:{},lastError:null});const a=this.stats.get(n);a.total++,a.errors[e]=(a.errors[e]||0)+1,a.lastError=new Date}getStats(t){return this.stats.get(t)||null}getAllStats(){return Object.fromEntries(this.stats)}getWorstNodes(t=3){return Array.from(this.stats.entries()).sort((e,n)=>n[1].total-e[1].total).slice(0,t).map(([e,n])=>({url:e,...n}))}clear(){this.stats.clear()}}const d=new O;export{r as RPC_ERROR_TYPES,R as analyzeRpcError,_ as getErrorDescription,P as getRetryDelay,d as rpcErrorStats,E as shouldRetry,h as withRpcErrorHandling};
