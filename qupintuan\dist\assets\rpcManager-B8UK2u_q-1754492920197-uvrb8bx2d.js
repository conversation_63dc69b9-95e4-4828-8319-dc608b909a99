const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web3-BiUUTWDm-1754492920197-uvrb8bx2d.js","assets/vendor-Caz4khA--1754492920197-uvrb8bx2d.js"])))=>i.map(i=>d[i]);
import{_ as b}from"./vendor-Caz4khA--1754492920197-uvrb8bx2d.js";const f=["https://data-seed-prebsc-1-s1.binance.org:8545","https://data-seed-prebsc-2-s1.binance.org:8545","https://data-seed-prebsc-1-s2.binance.org:8545","https://bsc-testnet.blockpi.network/v1/rpc/public"],h=["https://bsc-dataseed1.binance.org","https://bsc-dataseed2.binance.org","https://bsc-dataseed3.binance.org","https://bsc-dataseed1.defibit.io"];async function u(t,e=5e3){try{const r=new AbortController,c=setTimeout(()=>r.abort(),e),i=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({jsonrpc:"2.0",method:"eth_blockNumber",params:[],id:1}),signal:r.signal});if(clearTimeout(c),i.ok){const s=await i.json();return s.result&&s.result.startsWith("0x")}return!1}catch{return!1}}async function w(t){let e;if(t===97)e=f;else if(t===56)e=h;else throw new Error(`不支持的链ID: ${t}`);const r=5e3,c=e.map(async(s,n)=>await u(s,r)?{rpc:s,priority:n}:null);try{const n=(await Promise.allSettled(c)).map(a=>a.status==="fulfilled"?a.value:null).filter(Boolean).sort((a,o)=>a.priority-o.priority);if(n.length>0)return n[0].rpc}catch(s){console.warn("⚠️ [getFastestRpc] 测试RPC节点时发生错误:",s)}return e[0]}async function _(t){let e;if(t===97)e="https://data-seed-prebsc-1-s1.binance.org:8545";else if(t===56)e="https://bsc-dataseed1.binance.org";else throw new Error(`不支持的链ID: ${t}`);return e&&await u(e,3e3)?e:await w(t)}async function R(t=97,e={}){const{timeout:r=1e4,retryCount:c=3,retryDelay:i=1e3}=e,{createPublicClient:s,http:n}=await b(async()=>{const{createPublicClient:l,http:p}=await import("./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js").then(d=>d.x);return{createPublicClient:l,http:p}},__vite__mapDeps([0,1])),{bscTestnet:a}=await b(async()=>{const{bscTestnet:l}=await import("./web3-BiUUTWDm-1754492920197-uvrb8bx2d.js").then(p=>p.A);return{bscTestnet:l}},__vite__mapDeps([0,1]));let o;try{o=await _(t)}catch{if(t===97)o="https://data-seed-prebsc-1-s1.binance.org:8545";else if(t===56)o="https://bsc-dataseed1.binance.org";else throw new Error(`不支持的链ID: ${t}`)}return s({chain:a,transport:n(o,{timeout:r,retryCount:c,retryDelay:i})})}export{R as createRpcClient,w as getFastestRpc,_ as getRpcUrl};
