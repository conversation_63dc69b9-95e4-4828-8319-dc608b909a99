var R1=t=>{throw TypeError(t)};var uc=(t,n,i)=>n.has(t)||R1("Cannot "+i);var L=(t,n,i)=>(uc(t,n,"read from private field"),i?i.call(t):n.get(t)),pe=(t,n,i)=>n.has(t)?R1("Cannot add the same private member more than once"):n instanceof WeakSet?n.add(t):n.set(t,i),le=(t,n,i,o)=>(uc(t,n,"write to private field"),o?o.call(t,i):n.set(t,i),i),be=(t,n,i)=>(uc(t,n,"access private method"),i);var ls=(t,n,i,o)=>({set _(s){le(t,n,s,i)},get _(){return L(t,n,o)}});const z3="modulepreload",H3=function(t){return"/"+t},P1={},P7=function(n,i,o){let s=Promise.resolve();if(i&&i.length>0){let x=function(f){return Promise.all(f.map(p=>Promise.resolve(p).then(m=>({status:"fulfilled",value:m}),m=>({status:"rejected",reason:m}))))};document.getElementsByTagName("link");const c=document.querySelector("meta[property=csp-nonce]"),h=c?.nonce||c?.getAttribute("nonce");s=x(i.map(f=>{if(f=H3(f),f in P1)return;P1[f]=!0;const p=f.endsWith(".css"),m=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${f}"]${m}`))return;const y=document.createElement("link");if(y.rel=p?"stylesheet":z3,p||(y.as="script"),y.crossOrigin="",y.href=f,h&&y.setAttribute("nonce",h),document.head.appendChild(y),p)return new Promise((A,w)=>{y.addEventListener("load",A),y.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${f}`)))})}))}function u(x){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=x,window.dispatchEvent(c),!c.defaultPrevented)throw x}return s.then(x=>{for(const c of x||[])c.status==="rejected"&&u(c.reason);return n().catch(u)})};function yl(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function U3(t){if(Object.prototype.hasOwnProperty.call(t,"__esModule"))return t;var n=t.default;if(typeof n=="function"){var i=function o(){return this instanceof o?Reflect.construct(n,arguments,this.constructor):n.apply(this,arguments)};i.prototype=n.prototype}else i={};return Object.defineProperty(i,"__esModule",{value:!0}),Object.keys(t).forEach(function(o){var s=Object.getOwnPropertyDescriptor(t,o);Object.defineProperty(i,o,s.get?s:{enumerable:!0,get:function(){return t[o]}})}),i}var cc={exports:{}},Aa={},fc={exports:{}},Ee={},_1;function Q3(){if(_1)return Ee;_1=1;/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t=Symbol.for("react.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),x=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),m=Symbol.iterator;function y(F){return F===null||typeof F!="object"?null:(F=m&&F[m]||F["@@iterator"],typeof F=="function"?F:null)}var A={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,g={};function b(F,M,G){this.props=F,this.context=M,this.refs=g,this.updater=G||A}b.prototype.isReactComponent={},b.prototype.setState=function(F,M){if(typeof F!="object"&&typeof F!="function"&&F!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,F,M,"setState")},b.prototype.forceUpdate=function(F){this.updater.enqueueForceUpdate(this,F,"forceUpdate")};function S(){}S.prototype=b.prototype;function B(F,M,G){this.props=F,this.context=M,this.refs=g,this.updater=G||A}var E=B.prototype=new S;E.constructor=B,w(E,b.prototype),E.isPureReactComponent=!0;var D=Array.isArray,k=Object.prototype.hasOwnProperty,R={current:null},I={key:!0,ref:!0,__self:!0,__source:!0};function O(F,M,G){var W,fe={},ce=null,ye=null;if(M!=null)for(W in M.ref!==void 0&&(ye=M.ref),M.key!==void 0&&(ce=""+M.key),M)k.call(M,W)&&!I.hasOwnProperty(W)&&(fe[W]=M[W]);var oe=arguments.length-2;if(oe===1)fe.children=G;else if(1<oe){for(var we=Array(oe),Pe=0;Pe<oe;Pe++)we[Pe]=arguments[Pe+2];fe.children=we}if(F&&F.defaultProps)for(W in oe=F.defaultProps,oe)fe[W]===void 0&&(fe[W]=oe[W]);return{$$typeof:t,type:F,key:ce,ref:ye,props:fe,_owner:R.current}}function Q(F,M){return{$$typeof:t,type:F.type,key:M,ref:F.ref,props:F.props,_owner:F._owner}}function _(F){return typeof F=="object"&&F!==null&&F.$$typeof===t}function T(F){var M={"=":"=0",":":"=2"};return"$"+F.replace(/[=:]/g,function(G){return M[G]})}var q=/\/+/g;function $(F,M){return typeof F=="object"&&F!==null&&F.key!=null?T(""+F.key):M.toString(36)}function ae(F,M,G,W,fe){var ce=typeof F;(ce==="undefined"||ce==="boolean")&&(F=null);var ye=!1;if(F===null)ye=!0;else switch(ce){case"string":case"number":ye=!0;break;case"object":switch(F.$$typeof){case t:case n:ye=!0}}if(ye)return ye=F,fe=fe(ye),F=W===""?"."+$(ye,0):W,D(fe)?(G="",F!=null&&(G=F.replace(q,"$&/")+"/"),ae(fe,M,G,"",function(Pe){return Pe})):fe!=null&&(_(fe)&&(fe=Q(fe,G+(!fe.key||ye&&ye.key===fe.key?"":(""+fe.key).replace(q,"$&/")+"/")+F)),M.push(fe)),1;if(ye=0,W=W===""?".":W+":",D(F))for(var oe=0;oe<F.length;oe++){ce=F[oe];var we=W+$(ce,oe);ye+=ae(ce,M,G,we,fe)}else if(we=y(F),typeof we=="function")for(F=we.call(F),oe=0;!(ce=F.next()).done;)ce=ce.value,we=W+$(ce,oe++),ye+=ae(ce,M,G,we,fe);else if(ce==="object")throw M=String(F),Error("Objects are not valid as a React child (found: "+(M==="[object Object]"?"object with keys {"+Object.keys(F).join(", ")+"}":M)+"). If you meant to render a collection of children, use an array instead.");return ye}function K(F,M,G){if(F==null)return F;var W=[],fe=0;return ae(F,W,"","",function(ce){return M.call(G,ce,fe++)}),W}function Y(F){if(F._status===-1){var M=F._result;M=M(),M.then(function(G){(F._status===0||F._status===-1)&&(F._status=1,F._result=G)},function(G){(F._status===0||F._status===-1)&&(F._status=2,F._result=G)}),F._status===-1&&(F._status=0,F._result=M)}if(F._status===1)return F._result.default;throw F._result}var ne={current:null},Z={transition:null},J={ReactCurrentDispatcher:ne,ReactCurrentBatchConfig:Z,ReactCurrentOwner:R};function X(){throw Error("act(...) is not supported in production builds of React.")}return Ee.Children={map:K,forEach:function(F,M,G){K(F,function(){M.apply(this,arguments)},G)},count:function(F){var M=0;return K(F,function(){M++}),M},toArray:function(F){return K(F,function(M){return M})||[]},only:function(F){if(!_(F))throw Error("React.Children.only expected to receive a single React element child.");return F}},Ee.Component=b,Ee.Fragment=i,Ee.Profiler=s,Ee.PureComponent=B,Ee.StrictMode=o,Ee.Suspense=h,Ee.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=J,Ee.act=X,Ee.cloneElement=function(F,M,G){if(F==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+F+".");var W=w({},F.props),fe=F.key,ce=F.ref,ye=F._owner;if(M!=null){if(M.ref!==void 0&&(ce=M.ref,ye=R.current),M.key!==void 0&&(fe=""+M.key),F.type&&F.type.defaultProps)var oe=F.type.defaultProps;for(we in M)k.call(M,we)&&!I.hasOwnProperty(we)&&(W[we]=M[we]===void 0&&oe!==void 0?oe[we]:M[we])}var we=arguments.length-2;if(we===1)W.children=G;else if(1<we){oe=Array(we);for(var Pe=0;Pe<we;Pe++)oe[Pe]=arguments[Pe+2];W.children=oe}return{$$typeof:t,type:F.type,key:fe,ref:ce,props:W,_owner:ye}},Ee.createContext=function(F){return F={$$typeof:x,_currentValue:F,_currentValue2:F,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},F.Provider={$$typeof:u,_context:F},F.Consumer=F},Ee.createElement=O,Ee.createFactory=function(F){var M=O.bind(null,F);return M.type=F,M},Ee.createRef=function(){return{current:null}},Ee.forwardRef=function(F){return{$$typeof:c,render:F}},Ee.isValidElement=_,Ee.lazy=function(F){return{$$typeof:p,_payload:{_status:-1,_result:F},_init:Y}},Ee.memo=function(F,M){return{$$typeof:f,type:F,compare:M===void 0?null:M}},Ee.startTransition=function(F){var M=Z.transition;Z.transition={};try{F()}finally{Z.transition=M}},Ee.unstable_act=X,Ee.useCallback=function(F,M){return ne.current.useCallback(F,M)},Ee.useContext=function(F){return ne.current.useContext(F)},Ee.useDebugValue=function(){},Ee.useDeferredValue=function(F){return ne.current.useDeferredValue(F)},Ee.useEffect=function(F,M){return ne.current.useEffect(F,M)},Ee.useId=function(){return ne.current.useId()},Ee.useImperativeHandle=function(F,M,G){return ne.current.useImperativeHandle(F,M,G)},Ee.useInsertionEffect=function(F,M){return ne.current.useInsertionEffect(F,M)},Ee.useLayoutEffect=function(F,M){return ne.current.useLayoutEffect(F,M)},Ee.useMemo=function(F,M){return ne.current.useMemo(F,M)},Ee.useReducer=function(F,M,G){return ne.current.useReducer(F,M,G)},Ee.useRef=function(F){return ne.current.useRef(F)},Ee.useState=function(F){return ne.current.useState(F)},Ee.useSyncExternalStore=function(F,M,G){return ne.current.useSyncExternalStore(F,M,G)},Ee.useTransition=function(){return ne.current.useTransition()},Ee.version="18.3.1",Ee}var I1;function Wa(){return I1||(I1=1,fc.exports=Q3()),fc.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var O1;function j3(){if(O1)return Aa;O1=1;var t=Wa(),n=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,s=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function x(c,h,f){var p,m={},y=null,A=null;f!==void 0&&(y=""+f),h.key!==void 0&&(y=""+h.key),h.ref!==void 0&&(A=h.ref);for(p in h)o.call(h,p)&&!u.hasOwnProperty(p)&&(m[p]=h[p]);if(c&&c.defaultProps)for(p in h=c.defaultProps,h)m[p]===void 0&&(m[p]=h[p]);return{$$typeof:n,type:c,key:y,ref:A,props:m,_owner:s.current}}return Aa.Fragment=i,Aa.jsx=x,Aa.jsxs=x,Aa}var M1;function W3(){return M1||(M1=1,cc.exports=j3()),cc.exports}var V3=W3(),U=Wa();const _7=yl(U);var us={},xc={exports:{}},St={},dc={exports:{}},hc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var L1;function q3(){return L1||(L1=1,function(t){function n(Z,J){var X=Z.length;Z.push(J);e:for(;0<X;){var F=X-1>>>1,M=Z[F];if(0<s(M,J))Z[F]=J,Z[X]=M,X=F;else break e}}function i(Z){return Z.length===0?null:Z[0]}function o(Z){if(Z.length===0)return null;var J=Z[0],X=Z.pop();if(X!==J){Z[0]=X;e:for(var F=0,M=Z.length,G=M>>>1;F<G;){var W=2*(F+1)-1,fe=Z[W],ce=W+1,ye=Z[ce];if(0>s(fe,X))ce<M&&0>s(ye,fe)?(Z[F]=ye,Z[ce]=X,F=ce):(Z[F]=fe,Z[W]=X,F=W);else if(ce<M&&0>s(ye,X))Z[F]=ye,Z[ce]=X,F=ce;else break e}}return J}function s(Z,J){var X=Z.sortIndex-J.sortIndex;return X!==0?X:Z.id-J.id}if(typeof performance=="object"&&typeof performance.now=="function"){var u=performance;t.unstable_now=function(){return u.now()}}else{var x=Date,c=x.now();t.unstable_now=function(){return x.now()-c}}var h=[],f=[],p=1,m=null,y=3,A=!1,w=!1,g=!1,b=typeof setTimeout=="function"?setTimeout:null,S=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function E(Z){for(var J=i(f);J!==null;){if(J.callback===null)o(f);else if(J.startTime<=Z)o(f),J.sortIndex=J.expirationTime,n(h,J);else break;J=i(f)}}function D(Z){if(g=!1,E(Z),!w)if(i(h)!==null)w=!0,Y(k);else{var J=i(f);J!==null&&ne(D,J.startTime-Z)}}function k(Z,J){w=!1,g&&(g=!1,S(O),O=-1),A=!0;var X=y;try{for(E(J),m=i(h);m!==null&&(!(m.expirationTime>J)||Z&&!T());){var F=m.callback;if(typeof F=="function"){m.callback=null,y=m.priorityLevel;var M=F(m.expirationTime<=J);J=t.unstable_now(),typeof M=="function"?m.callback=M:m===i(h)&&o(h),E(J)}else o(h);m=i(h)}if(m!==null)var G=!0;else{var W=i(f);W!==null&&ne(D,W.startTime-J),G=!1}return G}finally{m=null,y=X,A=!1}}var R=!1,I=null,O=-1,Q=5,_=-1;function T(){return!(t.unstable_now()-_<Q)}function q(){if(I!==null){var Z=t.unstable_now();_=Z;var J=!0;try{J=I(!0,Z)}finally{J?$():(R=!1,I=null)}}else R=!1}var $;if(typeof B=="function")$=function(){B(q)};else if(typeof MessageChannel<"u"){var ae=new MessageChannel,K=ae.port2;ae.port1.onmessage=q,$=function(){K.postMessage(null)}}else $=function(){b(q,0)};function Y(Z){I=Z,R||(R=!0,$())}function ne(Z,J){O=b(function(){Z(t.unstable_now())},J)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(Z){Z.callback=null},t.unstable_continueExecution=function(){w||A||(w=!0,Y(k))},t.unstable_forceFrameRate=function(Z){0>Z||125<Z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Q=0<Z?Math.floor(1e3/Z):5},t.unstable_getCurrentPriorityLevel=function(){return y},t.unstable_getFirstCallbackNode=function(){return i(h)},t.unstable_next=function(Z){switch(y){case 1:case 2:case 3:var J=3;break;default:J=y}var X=y;y=J;try{return Z()}finally{y=X}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(Z,J){switch(Z){case 1:case 2:case 3:case 4:case 5:break;default:Z=3}var X=y;y=Z;try{return J()}finally{y=X}},t.unstable_scheduleCallback=function(Z,J,X){var F=t.unstable_now();switch(typeof X=="object"&&X!==null?(X=X.delay,X=typeof X=="number"&&0<X?F+X:F):X=F,Z){case 1:var M=-1;break;case 2:M=250;break;case 5:M=**********;break;case 4:M=1e4;break;default:M=5e3}return M=X+M,Z={id:p++,callback:J,priorityLevel:Z,startTime:X,expirationTime:M,sortIndex:-1},X>F?(Z.sortIndex=X,n(f,Z),i(h)===null&&Z===i(f)&&(g?(S(O),O=-1):g=!0,ne(D,X-F))):(Z.sortIndex=M,n(h,Z),w||A||(w=!0,Y(k))),Z},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(Z){var J=y;return function(){var X=y;y=J;try{return Z.apply(this,arguments)}finally{y=X}}}}(hc)),hc}var N1;function K3(){return N1||(N1=1,dc.exports=q3()),dc.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var T1;function $3(){if(T1)return St;T1=1;var t=Wa(),n=K3();function i(e){for(var r="https://reactjs.org/docs/error-decoder.html?invariant="+e,a=1;a<arguments.length;a++)r+="&args[]="+encodeURIComponent(arguments[a]);return"Minified React error #"+e+"; visit "+r+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,s={};function u(e,r){x(e,r),x(e+"Capture",r)}function x(e,r){for(s[e]=r,e=0;e<r.length;e++)o.add(r[e])}var c=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),h=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},m={};function y(e){return h.call(m,e)?!0:h.call(p,e)?!1:f.test(e)?m[e]=!0:(p[e]=!0,!1)}function A(e,r,a,l){if(a!==null&&a.type===0)return!1;switch(typeof r){case"function":case"symbol":return!0;case"boolean":return l?!1:a!==null?!a.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function w(e,r,a,l){if(r===null||typeof r>"u"||A(e,r,a,l))return!0;if(l)return!1;if(a!==null)switch(a.type){case 3:return!r;case 4:return r===!1;case 5:return isNaN(r);case 6:return isNaN(r)||1>r}return!1}function g(e,r,a,l,d,v,C){this.acceptsBooleans=r===2||r===3||r===4,this.attributeName=l,this.attributeNamespace=d,this.mustUseProperty=a,this.propertyName=e,this.type=r,this.sanitizeURL=v,this.removeEmptyString=C}var b={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){b[e]=new g(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var r=e[0];b[r]=new g(r,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){b[e]=new g(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){b[e]=new g(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){b[e]=new g(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){b[e]=new g(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){b[e]=new g(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){b[e]=new g(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){b[e]=new g(e,5,!1,e.toLowerCase(),null,!1,!1)});var S=/[\-:]([a-z])/g;function B(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var r=e.replace(S,B);b[r]=new g(r,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var r=e.replace(S,B);b[r]=new g(r,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var r=e.replace(S,B);b[r]=new g(r,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){b[e]=new g(e,1,!1,e.toLowerCase(),null,!1,!1)}),b.xlinkHref=new g("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){b[e]=new g(e,1,!1,e.toLowerCase(),null,!0,!0)});function E(e,r,a,l){var d=b.hasOwnProperty(r)?b[r]:null;(d!==null?d.type!==0:l||!(2<r.length)||r[0]!=="o"&&r[0]!=="O"||r[1]!=="n"&&r[1]!=="N")&&(w(r,a,d,l)&&(a=null),l||d===null?y(r)&&(a===null?e.removeAttribute(r):e.setAttribute(r,""+a)):d.mustUseProperty?e[d.propertyName]=a===null?d.type===3?!1:"":a:(r=d.attributeName,l=d.attributeNamespace,a===null?e.removeAttribute(r):(d=d.type,a=d===3||d===4&&a===!0?"":""+a,l?e.setAttributeNS(l,r,a):e.setAttribute(r,a))))}var D=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),R=Symbol.for("react.portal"),I=Symbol.for("react.fragment"),O=Symbol.for("react.strict_mode"),Q=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),T=Symbol.for("react.context"),q=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),ae=Symbol.for("react.suspense_list"),K=Symbol.for("react.memo"),Y=Symbol.for("react.lazy"),ne=Symbol.for("react.offscreen"),Z=Symbol.iterator;function J(e){return e===null||typeof e!="object"?null:(e=Z&&e[Z]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,F;function M(e){if(F===void 0)try{throw Error()}catch(a){var r=a.stack.trim().match(/\n( *(at )?)/);F=r&&r[1]||""}return`
`+F+e}var G=!1;function W(e,r){if(!e||G)return"";G=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(r)if(r=function(){throw Error()},Object.defineProperty(r.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(r,[])}catch(V){var l=V}Reflect.construct(e,[],r)}else{try{r.call()}catch(V){l=V}e.call(r.prototype)}else{try{throw Error()}catch(V){l=V}e()}}catch(V){if(V&&l&&typeof V.stack=="string"){for(var d=V.stack.split(`
`),v=l.stack.split(`
`),C=d.length-1,P=v.length-1;1<=C&&0<=P&&d[C]!==v[P];)P--;for(;1<=C&&0<=P;C--,P--)if(d[C]!==v[P]){if(C!==1||P!==1)do if(C--,P--,0>P||d[C]!==v[P]){var N=`
`+d[C].replace(" at new "," at ");return e.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",e.displayName)),N}while(1<=C&&0<=P);break}}}finally{G=!1,Error.prepareStackTrace=a}return(e=e?e.displayName||e.name:"")?M(e):""}function fe(e){switch(e.tag){case 5:return M(e.type);case 16:return M("Lazy");case 13:return M("Suspense");case 19:return M("SuspenseList");case 0:case 2:case 15:return e=W(e.type,!1),e;case 11:return e=W(e.type.render,!1),e;case 1:return e=W(e.type,!0),e;default:return""}}function ce(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case I:return"Fragment";case R:return"Portal";case Q:return"Profiler";case O:return"StrictMode";case $:return"Suspense";case ae:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case T:return(e.displayName||"Context")+".Consumer";case _:return(e._context.displayName||"Context")+".Provider";case q:var r=e.render;return e=e.displayName,e||(e=r.displayName||r.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case K:return r=e.displayName||null,r!==null?r:ce(e.type)||"Memo";case Y:r=e._payload,e=e._init;try{return ce(e(r))}catch{}}return null}function ye(e){var r=e.type;switch(e.tag){case 24:return"Cache";case 9:return(r.displayName||"Context")+".Consumer";case 10:return(r._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=r.render,e=e.displayName||e.name||"",r.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return r;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ce(r);case 8:return r===O?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof r=="function")return r.displayName||r.name||null;if(typeof r=="string")return r}return null}function oe(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function we(e){var r=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(r==="checkbox"||r==="radio")}function Pe(e){var r=we(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,r),l=""+e[r];if(!e.hasOwnProperty(r)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var d=a.get,v=a.set;return Object.defineProperty(e,r,{configurable:!0,get:function(){return d.call(this)},set:function(C){l=""+C,v.call(this,C)}}),Object.defineProperty(e,r,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(C){l=""+C},stopTracking:function(){e._valueTracker=null,delete e[r]}}}}function Jt(e){e._valueTracker||(e._valueTracker=Pe(e))}function $e(e){if(!e)return!1;var r=e._valueTracker;if(!r)return!0;var a=r.getValue(),l="";return e&&(l=we(e)?e.checked?"true":"false":e.value),e=l,e!==a?(r.setValue(e),!0):!1}function Te(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ue(e,r){var a=r.checked;return X({},r,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:a??e._wrapperState.initialChecked})}function vr(e,r){var a=r.defaultValue==null?"":r.defaultValue,l=r.checked!=null?r.checked:r.defaultChecked;a=oe(r.value!=null?r.value:a),e._wrapperState={initialChecked:l,initialValue:a,controlled:r.type==="checkbox"||r.type==="radio"?r.checked!=null:r.value!=null}}function er(e,r){r=r.checked,r!=null&&E(e,"checked",r,!1)}function Jr(e,r){er(e,r);var a=oe(r.value),l=r.type;if(a!=null)l==="number"?(a===0&&e.value===""||e.value!=a)&&(e.value=""+a):e.value!==""+a&&(e.value=""+a);else if(l==="submit"||l==="reset"){e.removeAttribute("value");return}r.hasOwnProperty("value")?jn(e,r.type,a):r.hasOwnProperty("defaultValue")&&jn(e,r.type,oe(r.defaultValue)),r.checked==null&&r.defaultChecked!=null&&(e.defaultChecked=!!r.defaultChecked)}function en(e,r,a){if(r.hasOwnProperty("value")||r.hasOwnProperty("defaultValue")){var l=r.type;if(!(l!=="submit"&&l!=="reset"||r.value!==void 0&&r.value!==null))return;r=""+e._wrapperState.initialValue,a||r===e.value||(e.value=r),e.defaultValue=r}a=e.name,a!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,a!==""&&(e.name=a)}function jn(e,r,a){(r!=="number"||Te(e.ownerDocument)!==e)&&(a==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+a&&(e.defaultValue=""+a))}var tr=Array.isArray;function Je(e,r,a,l){if(e=e.options,r){r={};for(var d=0;d<a.length;d++)r["$"+a[d]]=!0;for(a=0;a<e.length;a++)d=r.hasOwnProperty("$"+e[a].value),e[a].selected!==d&&(e[a].selected=d),d&&l&&(e[a].defaultSelected=!0)}else{for(a=""+oe(a),r=null,d=0;d<e.length;d++){if(e[d].value===a){e[d].selected=!0,l&&(e[d].defaultSelected=!0);return}r!==null||e[d].disabled||(r=e[d])}r!==null&&(r.selected=!0)}}function lt(e,r){if(r.dangerouslySetInnerHTML!=null)throw Error(i(91));return X({},r,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function rr(e,r){var a=r.value;if(a==null){if(a=r.children,r=r.defaultValue,a!=null){if(r!=null)throw Error(i(92));if(tr(a)){if(1<a.length)throw Error(i(93));a=a[0]}r=a}r==null&&(r=""),a=r}e._wrapperState={initialValue:oe(a)}}function Wn(e,r){var a=oe(r.value),l=oe(r.defaultValue);a!=null&&(a=""+a,a!==e.value&&(e.value=a),r.defaultValue==null&&e.defaultValue!==a&&(e.defaultValue=a)),l!=null&&(e.defaultValue=""+l)}function Ii(e){var r=e.textContent;r===e._wrapperState.initialValue&&r!==""&&r!==null&&(e.value=r)}function kr(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Rr(e,r){return e==null||e==="http://www.w3.org/1999/xhtml"?kr(r):e==="http://www.w3.org/2000/svg"&&r==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var R0,Oi=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(r,a,l,d){MSApp.execUnsafeLocalFunction(function(){return e(r,a,l,d)})}:e}(function(e,r){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=r;else{for(R0=R0||document.createElement("div"),R0.innerHTML="<svg>"+r.valueOf().toString()+"</svg>",r=R0.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;r.firstChild;)e.appendChild(r.firstChild)}});function tn(e,r){if(r){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=r;return}}e.textContent=r}var Lt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Vn=["Webkit","ms","Moz","O"];Object.keys(Lt).forEach(function(e){Vn.forEach(function(r){r=r+e.charAt(0).toUpperCase()+e.substring(1),Lt[r]=Lt[e]})});function Ga(e,r,a){return r==null||typeof r=="boolean"||r===""?"":a||typeof r!="number"||r===0||Lt.hasOwnProperty(e)&&Lt[e]?(""+r).trim():r+"px"}function Mi(e,r){e=e.style;for(var a in r)if(r.hasOwnProperty(a)){var l=a.indexOf("--")===0,d=Ga(a,r[a],l);a==="float"&&(a="cssFloat"),l?e.setProperty(a,d):e[a]=d}}var Za=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Li(e,r){if(r){if(Za[e]&&(r.children!=null||r.dangerouslySetInnerHTML!=null))throw Error(i(137,e));if(r.dangerouslySetInnerHTML!=null){if(r.children!=null)throw Error(i(60));if(typeof r.dangerouslySetInnerHTML!="object"||!("__html"in r.dangerouslySetInnerHTML))throw Error(i(61))}if(r.style!=null&&typeof r.style!="object")throw Error(i(62))}}function Ni(e,r){if(e.indexOf("-")===-1)return typeof r.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var P0=null;function Ti(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _0=null,rn=null,Pr=null;function Ya(e){if(e=oa(e)){if(typeof _0!="function")throw Error(i(280));var r=e.stateNode;r&&(r=Eo(r),_0(e.stateNode,e.type,r))}}function Xa(e){rn?Pr?Pr.push(e):Pr=[e]:rn=e}function Ja(){if(rn){var e=rn,r=Pr;if(Pr=rn=null,Ya(e),r)for(e=0;e<r.length;e++)Ya(r[e])}}function zi(e,r){return e(r)}function eo(){}var Hi=!1;function Ui(e,r,a){if(Hi)return e(r,a);Hi=!0;try{return zi(e,r,a)}finally{Hi=!1,(rn!==null||Pr!==null)&&(eo(),Ja())}}function qn(e,r){var a=e.stateNode;if(a===null)return null;var l=Eo(a);if(l===null)return null;a=l[r];e:switch(r){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(i(231,r,typeof a));return a}var I0=!1;if(c)try{var Ge={};Object.defineProperty(Ge,"passive",{get:function(){I0=!0}}),window.addEventListener("test",Ge,Ge),window.removeEventListener("test",Ge,Ge)}catch{I0=!1}function mr(e,r,a,l,d,v,C,P,N){var V=Array.prototype.slice.call(arguments,3);try{r.apply(a,V)}catch(te){this.onError(te)}}var nn=!1,O0=null,to=!1,Fl=null,q4={onError:function(e){nn=!0,O0=e}};function K4(e,r,a,l,d,v,C,P,N){nn=!1,O0=null,mr.apply(q4,arguments)}function $4(e,r,a,l,d,v,C,P,N){if(K4.apply(this,arguments),nn){if(nn){var V=O0;nn=!1,O0=null}else throw Error(i(198));to||(to=!0,Fl=V)}}function Kn(e){var r=e,a=e;if(e.alternate)for(;r.return;)r=r.return;else{e=r;do r=e,(r.flags&4098)!==0&&(a=r.return),e=r.return;while(e)}return r.tag===3?a:null}function Jf(e){if(e.tag===13){var r=e.memoizedState;if(r===null&&(e=e.alternate,e!==null&&(r=e.memoizedState)),r!==null)return r.dehydrated}return null}function ex(e){if(Kn(e)!==e)throw Error(i(188))}function G4(e){var r=e.alternate;if(!r){if(r=Kn(e),r===null)throw Error(i(188));return r!==e?null:e}for(var a=e,l=r;;){var d=a.return;if(d===null)break;var v=d.alternate;if(v===null){if(l=d.return,l!==null){a=l;continue}break}if(d.child===v.child){for(v=d.child;v;){if(v===a)return ex(d),e;if(v===l)return ex(d),r;v=v.sibling}throw Error(i(188))}if(a.return!==l.return)a=d,l=v;else{for(var C=!1,P=d.child;P;){if(P===a){C=!0,a=d,l=v;break}if(P===l){C=!0,l=d,a=v;break}P=P.sibling}if(!C){for(P=v.child;P;){if(P===a){C=!0,a=v,l=d;break}if(P===l){C=!0,l=v,a=d;break}P=P.sibling}if(!C)throw Error(i(189))}}if(a.alternate!==l)throw Error(i(190))}if(a.tag!==3)throw Error(i(188));return a.stateNode.current===a?e:r}function tx(e){return e=G4(e),e!==null?rx(e):null}function rx(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var r=rx(e);if(r!==null)return r;e=e.sibling}return null}var nx=n.unstable_scheduleCallback,ix=n.unstable_cancelCallback,Z4=n.unstable_shouldYield,Y4=n.unstable_requestPaint,We=n.unstable_now,X4=n.unstable_getCurrentPriorityLevel,kl=n.unstable_ImmediatePriority,ax=n.unstable_UserBlockingPriority,ro=n.unstable_NormalPriority,J4=n.unstable_LowPriority,ox=n.unstable_IdlePriority,no=null,yr=null;function e8(e){if(yr&&typeof yr.onCommitFiberRoot=="function")try{yr.onCommitFiberRoot(no,e,void 0,(e.current.flags&128)===128)}catch{}}var nr=Math.clz32?Math.clz32:n8,t8=Math.log,r8=Math.LN2;function n8(e){return e>>>=0,e===0?32:31-(t8(e)/r8|0)|0}var io=64,ao=4194304;function Qi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function oo(e,r){var a=e.pendingLanes;if(a===0)return 0;var l=0,d=e.suspendedLanes,v=e.pingedLanes,C=a&268435455;if(C!==0){var P=C&~d;P!==0?l=Qi(P):(v&=C,v!==0&&(l=Qi(v)))}else C=a&~d,C!==0?l=Qi(C):v!==0&&(l=Qi(v));if(l===0)return 0;if(r!==0&&r!==l&&(r&d)===0&&(d=l&-l,v=r&-r,d>=v||d===16&&(v&4194240)!==0))return r;if((l&4)!==0&&(l|=a&16),r=e.entangledLanes,r!==0)for(e=e.entanglements,r&=l;0<r;)a=31-nr(r),d=1<<a,l|=e[a],r&=~d;return l}function i8(e,r){switch(e){case 1:case 2:case 4:return r+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return r+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function a8(e,r){for(var a=e.suspendedLanes,l=e.pingedLanes,d=e.expirationTimes,v=e.pendingLanes;0<v;){var C=31-nr(v),P=1<<C,N=d[C];N===-1?((P&a)===0||(P&l)!==0)&&(d[C]=i8(P,r)):N<=r&&(e.expiredLanes|=P),v&=~P}}function Rl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function sx(){var e=io;return io<<=1,(io&4194240)===0&&(io=64),e}function Pl(e){for(var r=[],a=0;31>a;a++)r.push(e);return r}function ji(e,r,a){e.pendingLanes|=r,r!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,r=31-nr(r),e[r]=a}function o8(e,r){var a=e.pendingLanes&~r;e.pendingLanes=r,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=r,e.mutableReadLanes&=r,e.entangledLanes&=r,r=e.entanglements;var l=e.eventTimes;for(e=e.expirationTimes;0<a;){var d=31-nr(a),v=1<<d;r[d]=0,l[d]=-1,e[d]=-1,a&=~v}}function _l(e,r){var a=e.entangledLanes|=r;for(e=e.entanglements;a;){var l=31-nr(a),d=1<<l;d&r|e[l]&r&&(e[l]|=r),a&=~d}}var Re=0;function lx(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var ux,Il,cx,fx,xx,Ol=!1,so=[],an=null,on=null,sn=null,Wi=new Map,Vi=new Map,ln=[],s8="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function dx(e,r){switch(e){case"focusin":case"focusout":an=null;break;case"dragenter":case"dragleave":on=null;break;case"mouseover":case"mouseout":sn=null;break;case"pointerover":case"pointerout":Wi.delete(r.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vi.delete(r.pointerId)}}function qi(e,r,a,l,d,v){return e===null||e.nativeEvent!==v?(e={blockedOn:r,domEventName:a,eventSystemFlags:l,nativeEvent:v,targetContainers:[d]},r!==null&&(r=oa(r),r!==null&&Il(r)),e):(e.eventSystemFlags|=l,r=e.targetContainers,d!==null&&r.indexOf(d)===-1&&r.push(d),e)}function l8(e,r,a,l,d){switch(r){case"focusin":return an=qi(an,e,r,a,l,d),!0;case"dragenter":return on=qi(on,e,r,a,l,d),!0;case"mouseover":return sn=qi(sn,e,r,a,l,d),!0;case"pointerover":var v=d.pointerId;return Wi.set(v,qi(Wi.get(v)||null,e,r,a,l,d)),!0;case"gotpointercapture":return v=d.pointerId,Vi.set(v,qi(Vi.get(v)||null,e,r,a,l,d)),!0}return!1}function hx(e){var r=$n(e.target);if(r!==null){var a=Kn(r);if(a!==null){if(r=a.tag,r===13){if(r=Jf(a),r!==null){e.blockedOn=r,xx(e.priority,function(){cx(a)});return}}else if(r===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function lo(e){if(e.blockedOn!==null)return!1;for(var r=e.targetContainers;0<r.length;){var a=Ll(e.domEventName,e.eventSystemFlags,r[0],e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);P0=l,a.target.dispatchEvent(l),P0=null}else return r=oa(a),r!==null&&Il(r),e.blockedOn=a,!1;r.shift()}return!0}function px(e,r,a){lo(e)&&a.delete(r)}function u8(){Ol=!1,an!==null&&lo(an)&&(an=null),on!==null&&lo(on)&&(on=null),sn!==null&&lo(sn)&&(sn=null),Wi.forEach(px),Vi.forEach(px)}function Ki(e,r){e.blockedOn===r&&(e.blockedOn=null,Ol||(Ol=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,u8)))}function $i(e){function r(d){return Ki(d,e)}if(0<so.length){Ki(so[0],e);for(var a=1;a<so.length;a++){var l=so[a];l.blockedOn===e&&(l.blockedOn=null)}}for(an!==null&&Ki(an,e),on!==null&&Ki(on,e),sn!==null&&Ki(sn,e),Wi.forEach(r),Vi.forEach(r),a=0;a<ln.length;a++)l=ln[a],l.blockedOn===e&&(l.blockedOn=null);for(;0<ln.length&&(a=ln[0],a.blockedOn===null);)hx(a),a.blockedOn===null&&ln.shift()}var M0=D.ReactCurrentBatchConfig,uo=!0;function c8(e,r,a,l){var d=Re,v=M0.transition;M0.transition=null;try{Re=1,Ml(e,r,a,l)}finally{Re=d,M0.transition=v}}function f8(e,r,a,l){var d=Re,v=M0.transition;M0.transition=null;try{Re=4,Ml(e,r,a,l)}finally{Re=d,M0.transition=v}}function Ml(e,r,a,l){if(uo){var d=Ll(e,r,a,l);if(d===null)Jl(e,r,l,co,a),dx(e,l);else if(l8(d,e,r,a,l))l.stopPropagation();else if(dx(e,l),r&4&&-1<s8.indexOf(e)){for(;d!==null;){var v=oa(d);if(v!==null&&ux(v),v=Ll(e,r,a,l),v===null&&Jl(e,r,l,co,a),v===d)break;d=v}d!==null&&l.stopPropagation()}else Jl(e,r,l,null,a)}}var co=null;function Ll(e,r,a,l){if(co=null,e=Ti(l),e=$n(e),e!==null)if(r=Kn(e),r===null)e=null;else if(a=r.tag,a===13){if(e=Jf(r),e!==null)return e;e=null}else if(a===3){if(r.stateNode.current.memoizedState.isDehydrated)return r.tag===3?r.stateNode.containerInfo:null;e=null}else r!==e&&(e=null);return co=e,null}function vx(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(X4()){case kl:return 1;case ax:return 4;case ro:case J4:return 16;case ox:return 536870912;default:return 16}default:return 16}}var un=null,Nl=null,fo=null;function mx(){if(fo)return fo;var e,r=Nl,a=r.length,l,d="value"in un?un.value:un.textContent,v=d.length;for(e=0;e<a&&r[e]===d[e];e++);var C=a-e;for(l=1;l<=C&&r[a-l]===d[v-l];l++);return fo=d.slice(e,1<l?1-l:void 0)}function xo(e){var r=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&r===13&&(e=13)):e=r,e===10&&(e=13),32<=e||e===13?e:0}function ho(){return!0}function yx(){return!1}function Rt(e){function r(a,l,d,v,C){this._reactName=a,this._targetInst=d,this.type=l,this.nativeEvent=v,this.target=C,this.currentTarget=null;for(var P in e)e.hasOwnProperty(P)&&(a=e[P],this[P]=a?a(v):v[P]);return this.isDefaultPrevented=(v.defaultPrevented!=null?v.defaultPrevented:v.returnValue===!1)?ho:yx,this.isPropagationStopped=yx,this}return X(r.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=ho)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=ho)},persist:function(){},isPersistent:ho}),r}var L0={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Tl=Rt(L0),Gi=X({},L0,{view:0,detail:0}),x8=Rt(Gi),zl,Hl,Zi,po=X({},Gi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ql,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Zi&&(Zi&&e.type==="mousemove"?(zl=e.screenX-Zi.screenX,Hl=e.screenY-Zi.screenY):Hl=zl=0,Zi=e),zl)},movementY:function(e){return"movementY"in e?e.movementY:Hl}}),gx=Rt(po),d8=X({},po,{dataTransfer:0}),h8=Rt(d8),p8=X({},Gi,{relatedTarget:0}),Ul=Rt(p8),v8=X({},L0,{animationName:0,elapsedTime:0,pseudoElement:0}),m8=Rt(v8),y8=X({},L0,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),g8=Rt(y8),b8=X({},L0,{data:0}),bx=Rt(b8),A8={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},w8={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},E8={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function C8(e){var r=this.nativeEvent;return r.getModifierState?r.getModifierState(e):(e=E8[e])?!!r[e]:!1}function Ql(){return C8}var B8=X({},Gi,{key:function(e){if(e.key){var r=A8[e.key]||e.key;if(r!=="Unidentified")return r}return e.type==="keypress"?(e=xo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?w8[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ql,charCode:function(e){return e.type==="keypress"?xo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?xo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),S8=Rt(B8),D8=X({},po,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ax=Rt(D8),F8=X({},Gi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ql}),k8=Rt(F8),R8=X({},L0,{propertyName:0,elapsedTime:0,pseudoElement:0}),P8=Rt(R8),_8=X({},po,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),I8=Rt(_8),O8=[9,13,27,32],jl=c&&"CompositionEvent"in window,Yi=null;c&&"documentMode"in document&&(Yi=document.documentMode);var M8=c&&"TextEvent"in window&&!Yi,wx=c&&(!jl||Yi&&8<Yi&&11>=Yi),Ex=" ",Cx=!1;function Bx(e,r){switch(e){case"keyup":return O8.indexOf(r.keyCode)!==-1;case"keydown":return r.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Sx(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var N0=!1;function L8(e,r){switch(e){case"compositionend":return Sx(r);case"keypress":return r.which!==32?null:(Cx=!0,Ex);case"textInput":return e=r.data,e===Ex&&Cx?null:e;default:return null}}function N8(e,r){if(N0)return e==="compositionend"||!jl&&Bx(e,r)?(e=mx(),fo=Nl=un=null,N0=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(r.ctrlKey||r.altKey||r.metaKey)||r.ctrlKey&&r.altKey){if(r.char&&1<r.char.length)return r.char;if(r.which)return String.fromCharCode(r.which)}return null;case"compositionend":return wx&&r.locale!=="ko"?null:r.data;default:return null}}var T8={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Dx(e){var r=e&&e.nodeName&&e.nodeName.toLowerCase();return r==="input"?!!T8[e.type]:r==="textarea"}function Fx(e,r,a,l){Xa(l),r=bo(r,"onChange"),0<r.length&&(a=new Tl("onChange","change",null,a,l),e.push({event:a,listeners:r}))}var Xi=null,Ji=null;function z8(e){qx(e,0)}function vo(e){var r=Q0(e);if($e(r))return e}function H8(e,r){if(e==="change")return r}var kx=!1;if(c){var Wl;if(c){var Vl="oninput"in document;if(!Vl){var Rx=document.createElement("div");Rx.setAttribute("oninput","return;"),Vl=typeof Rx.oninput=="function"}Wl=Vl}else Wl=!1;kx=Wl&&(!document.documentMode||9<document.documentMode)}function Px(){Xi&&(Xi.detachEvent("onpropertychange",_x),Ji=Xi=null)}function _x(e){if(e.propertyName==="value"&&vo(Ji)){var r=[];Fx(r,Ji,e,Ti(e)),Ui(z8,r)}}function U8(e,r,a){e==="focusin"?(Px(),Xi=r,Ji=a,Xi.attachEvent("onpropertychange",_x)):e==="focusout"&&Px()}function Q8(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return vo(Ji)}function j8(e,r){if(e==="click")return vo(r)}function W8(e,r){if(e==="input"||e==="change")return vo(r)}function V8(e,r){return e===r&&(e!==0||1/e===1/r)||e!==e&&r!==r}var ir=typeof Object.is=="function"?Object.is:V8;function ea(e,r){if(ir(e,r))return!0;if(typeof e!="object"||e===null||typeof r!="object"||r===null)return!1;var a=Object.keys(e),l=Object.keys(r);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var d=a[l];if(!h.call(r,d)||!ir(e[d],r[d]))return!1}return!0}function Ix(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ox(e,r){var a=Ix(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=r&&l>=r)return{node:a,offset:r-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Ix(a)}}function Mx(e,r){return e&&r?e===r?!0:e&&e.nodeType===3?!1:r&&r.nodeType===3?Mx(e,r.parentNode):"contains"in e?e.contains(r):e.compareDocumentPosition?!!(e.compareDocumentPosition(r)&16):!1:!1}function Lx(){for(var e=window,r=Te();r instanceof e.HTMLIFrameElement;){try{var a=typeof r.contentWindow.location.href=="string"}catch{a=!1}if(a)e=r.contentWindow;else break;r=Te(e.document)}return r}function ql(e){var r=e&&e.nodeName&&e.nodeName.toLowerCase();return r&&(r==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||r==="textarea"||e.contentEditable==="true")}function q8(e){var r=Lx(),a=e.focusedElem,l=e.selectionRange;if(r!==a&&a&&a.ownerDocument&&Mx(a.ownerDocument.documentElement,a)){if(l!==null&&ql(a)){if(r=l.start,e=l.end,e===void 0&&(e=r),"selectionStart"in a)a.selectionStart=r,a.selectionEnd=Math.min(e,a.value.length);else if(e=(r=a.ownerDocument||document)&&r.defaultView||window,e.getSelection){e=e.getSelection();var d=a.textContent.length,v=Math.min(l.start,d);l=l.end===void 0?v:Math.min(l.end,d),!e.extend&&v>l&&(d=l,l=v,v=d),d=Ox(a,v);var C=Ox(a,l);d&&C&&(e.rangeCount!==1||e.anchorNode!==d.node||e.anchorOffset!==d.offset||e.focusNode!==C.node||e.focusOffset!==C.offset)&&(r=r.createRange(),r.setStart(d.node,d.offset),e.removeAllRanges(),v>l?(e.addRange(r),e.extend(C.node,C.offset)):(r.setEnd(C.node,C.offset),e.addRange(r)))}}for(r=[],e=a;e=e.parentNode;)e.nodeType===1&&r.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof a.focus=="function"&&a.focus(),a=0;a<r.length;a++)e=r[a],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var K8=c&&"documentMode"in document&&11>=document.documentMode,T0=null,Kl=null,ta=null,$l=!1;function Nx(e,r,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;$l||T0==null||T0!==Te(l)||(l=T0,"selectionStart"in l&&ql(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),ta&&ea(ta,l)||(ta=l,l=bo(Kl,"onSelect"),0<l.length&&(r=new Tl("onSelect","select",null,r,a),e.push({event:r,listeners:l}),r.target=T0)))}function mo(e,r){var a={};return a[e.toLowerCase()]=r.toLowerCase(),a["Webkit"+e]="webkit"+r,a["Moz"+e]="moz"+r,a}var z0={animationend:mo("Animation","AnimationEnd"),animationiteration:mo("Animation","AnimationIteration"),animationstart:mo("Animation","AnimationStart"),transitionend:mo("Transition","TransitionEnd")},Gl={},Tx={};c&&(Tx=document.createElement("div").style,"AnimationEvent"in window||(delete z0.animationend.animation,delete z0.animationiteration.animation,delete z0.animationstart.animation),"TransitionEvent"in window||delete z0.transitionend.transition);function yo(e){if(Gl[e])return Gl[e];if(!z0[e])return e;var r=z0[e],a;for(a in r)if(r.hasOwnProperty(a)&&a in Tx)return Gl[e]=r[a];return e}var zx=yo("animationend"),Hx=yo("animationiteration"),Ux=yo("animationstart"),Qx=yo("transitionend"),jx=new Map,Wx="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function cn(e,r){jx.set(e,r),u(r,[e])}for(var Zl=0;Zl<Wx.length;Zl++){var Yl=Wx[Zl],$8=Yl.toLowerCase(),G8=Yl[0].toUpperCase()+Yl.slice(1);cn($8,"on"+G8)}cn(zx,"onAnimationEnd"),cn(Hx,"onAnimationIteration"),cn(Ux,"onAnimationStart"),cn("dblclick","onDoubleClick"),cn("focusin","onFocus"),cn("focusout","onBlur"),cn(Qx,"onTransitionEnd"),x("onMouseEnter",["mouseout","mouseover"]),x("onMouseLeave",["mouseout","mouseover"]),x("onPointerEnter",["pointerout","pointerover"]),x("onPointerLeave",["pointerout","pointerover"]),u("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),u("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),u("onBeforeInput",["compositionend","keypress","textInput","paste"]),u("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ra="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Z8=new Set("cancel close invalid load scroll toggle".split(" ").concat(ra));function Vx(e,r,a){var l=e.type||"unknown-event";e.currentTarget=a,$4(l,r,void 0,e),e.currentTarget=null}function qx(e,r){r=(r&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],d=l.event;l=l.listeners;e:{var v=void 0;if(r)for(var C=l.length-1;0<=C;C--){var P=l[C],N=P.instance,V=P.currentTarget;if(P=P.listener,N!==v&&d.isPropagationStopped())break e;Vx(d,P,V),v=N}else for(C=0;C<l.length;C++){if(P=l[C],N=P.instance,V=P.currentTarget,P=P.listener,N!==v&&d.isPropagationStopped())break e;Vx(d,P,V),v=N}}}if(to)throw e=Fl,to=!1,Fl=null,e}function Ie(e,r){var a=r[au];a===void 0&&(a=r[au]=new Set);var l=e+"__bubble";a.has(l)||(Kx(r,e,2,!1),a.add(l))}function Xl(e,r,a){var l=0;r&&(l|=4),Kx(a,e,l,r)}var go="_reactListening"+Math.random().toString(36).slice(2);function na(e){if(!e[go]){e[go]=!0,o.forEach(function(a){a!=="selectionchange"&&(Z8.has(a)||Xl(a,!1,e),Xl(a,!0,e))});var r=e.nodeType===9?e:e.ownerDocument;r===null||r[go]||(r[go]=!0,Xl("selectionchange",!1,r))}}function Kx(e,r,a,l){switch(vx(r)){case 1:var d=c8;break;case 4:d=f8;break;default:d=Ml}a=d.bind(null,r,a,e),d=void 0,!I0||r!=="touchstart"&&r!=="touchmove"&&r!=="wheel"||(d=!0),l?d!==void 0?e.addEventListener(r,a,{capture:!0,passive:d}):e.addEventListener(r,a,!0):d!==void 0?e.addEventListener(r,a,{passive:d}):e.addEventListener(r,a,!1)}function Jl(e,r,a,l,d){var v=l;if((r&1)===0&&(r&2)===0&&l!==null)e:for(;;){if(l===null)return;var C=l.tag;if(C===3||C===4){var P=l.stateNode.containerInfo;if(P===d||P.nodeType===8&&P.parentNode===d)break;if(C===4)for(C=l.return;C!==null;){var N=C.tag;if((N===3||N===4)&&(N=C.stateNode.containerInfo,N===d||N.nodeType===8&&N.parentNode===d))return;C=C.return}for(;P!==null;){if(C=$n(P),C===null)return;if(N=C.tag,N===5||N===6){l=v=C;continue e}P=P.parentNode}}l=l.return}Ui(function(){var V=v,te=Ti(a),re=[];e:{var ee=jx.get(e);if(ee!==void 0){var se=Tl,xe=e;switch(e){case"keypress":if(xo(a)===0)break e;case"keydown":case"keyup":se=S8;break;case"focusin":xe="focus",se=Ul;break;case"focusout":xe="blur",se=Ul;break;case"beforeblur":case"afterblur":se=Ul;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":se=gx;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":se=h8;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":se=k8;break;case zx:case Hx:case Ux:se=m8;break;case Qx:se=P8;break;case"scroll":se=x8;break;case"wheel":se=I8;break;case"copy":case"cut":case"paste":se=g8;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":se=Ax}var de=(r&4)!==0,Ve=!de&&e==="scroll",H=de?ee!==null?ee+"Capture":null:ee;de=[];for(var z=V,j;z!==null;){j=z;var ie=j.stateNode;if(j.tag===5&&ie!==null&&(j=ie,H!==null&&(ie=qn(z,H),ie!=null&&de.push(ia(z,ie,j)))),Ve)break;z=z.return}0<de.length&&(ee=new se(ee,xe,null,a,te),re.push({event:ee,listeners:de}))}}if((r&7)===0){e:{if(ee=e==="mouseover"||e==="pointerover",se=e==="mouseout"||e==="pointerout",ee&&a!==P0&&(xe=a.relatedTarget||a.fromElement)&&($n(xe)||xe[_r]))break e;if((se||ee)&&(ee=te.window===te?te:(ee=te.ownerDocument)?ee.defaultView||ee.parentWindow:window,se?(xe=a.relatedTarget||a.toElement,se=V,xe=xe?$n(xe):null,xe!==null&&(Ve=Kn(xe),xe!==Ve||xe.tag!==5&&xe.tag!==6)&&(xe=null)):(se=null,xe=V),se!==xe)){if(de=gx,ie="onMouseLeave",H="onMouseEnter",z="mouse",(e==="pointerout"||e==="pointerover")&&(de=Ax,ie="onPointerLeave",H="onPointerEnter",z="pointer"),Ve=se==null?ee:Q0(se),j=xe==null?ee:Q0(xe),ee=new de(ie,z+"leave",se,a,te),ee.target=Ve,ee.relatedTarget=j,ie=null,$n(te)===V&&(de=new de(H,z+"enter",xe,a,te),de.target=j,de.relatedTarget=Ve,ie=de),Ve=ie,se&&xe)t:{for(de=se,H=xe,z=0,j=de;j;j=H0(j))z++;for(j=0,ie=H;ie;ie=H0(ie))j++;for(;0<z-j;)de=H0(de),z--;for(;0<j-z;)H=H0(H),j--;for(;z--;){if(de===H||H!==null&&de===H.alternate)break t;de=H0(de),H=H0(H)}de=null}else de=null;se!==null&&$x(re,ee,se,de,!1),xe!==null&&Ve!==null&&$x(re,Ve,xe,de,!0)}}e:{if(ee=V?Q0(V):window,se=ee.nodeName&&ee.nodeName.toLowerCase(),se==="select"||se==="input"&&ee.type==="file")var he=H8;else if(Dx(ee))if(kx)he=W8;else{he=Q8;var ve=U8}else(se=ee.nodeName)&&se.toLowerCase()==="input"&&(ee.type==="checkbox"||ee.type==="radio")&&(he=j8);if(he&&(he=he(e,V))){Fx(re,he,a,te);break e}ve&&ve(e,ee,V),e==="focusout"&&(ve=ee._wrapperState)&&ve.controlled&&ee.type==="number"&&jn(ee,"number",ee.value)}switch(ve=V?Q0(V):window,e){case"focusin":(Dx(ve)||ve.contentEditable==="true")&&(T0=ve,Kl=V,ta=null);break;case"focusout":ta=Kl=T0=null;break;case"mousedown":$l=!0;break;case"contextmenu":case"mouseup":case"dragend":$l=!1,Nx(re,a,te);break;case"selectionchange":if(K8)break;case"keydown":case"keyup":Nx(re,a,te)}var me;if(jl)e:{switch(e){case"compositionstart":var ge="onCompositionStart";break e;case"compositionend":ge="onCompositionEnd";break e;case"compositionupdate":ge="onCompositionUpdate";break e}ge=void 0}else N0?Bx(e,a)&&(ge="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(ge="onCompositionStart");ge&&(wx&&a.locale!=="ko"&&(N0||ge!=="onCompositionStart"?ge==="onCompositionEnd"&&N0&&(me=mx()):(un=te,Nl="value"in un?un.value:un.textContent,N0=!0)),ve=bo(V,ge),0<ve.length&&(ge=new bx(ge,e,null,a,te),re.push({event:ge,listeners:ve}),me?ge.data=me:(me=Sx(a),me!==null&&(ge.data=me)))),(me=M8?L8(e,a):N8(e,a))&&(V=bo(V,"onBeforeInput"),0<V.length&&(te=new bx("onBeforeInput","beforeinput",null,a,te),re.push({event:te,listeners:V}),te.data=me))}qx(re,r)})}function ia(e,r,a){return{instance:e,listener:r,currentTarget:a}}function bo(e,r){for(var a=r+"Capture",l=[];e!==null;){var d=e,v=d.stateNode;d.tag===5&&v!==null&&(d=v,v=qn(e,a),v!=null&&l.unshift(ia(e,v,d)),v=qn(e,r),v!=null&&l.push(ia(e,v,d))),e=e.return}return l}function H0(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function $x(e,r,a,l,d){for(var v=r._reactName,C=[];a!==null&&a!==l;){var P=a,N=P.alternate,V=P.stateNode;if(N!==null&&N===l)break;P.tag===5&&V!==null&&(P=V,d?(N=qn(a,v),N!=null&&C.unshift(ia(a,N,P))):d||(N=qn(a,v),N!=null&&C.push(ia(a,N,P)))),a=a.return}C.length!==0&&e.push({event:r,listeners:C})}var Y8=/\r\n?/g,X8=/\u0000|\uFFFD/g;function Gx(e){return(typeof e=="string"?e:""+e).replace(Y8,`
`).replace(X8,"")}function Ao(e,r,a){if(r=Gx(r),Gx(e)!==r&&a)throw Error(i(425))}function wo(){}var eu=null,tu=null;function ru(e,r){return e==="textarea"||e==="noscript"||typeof r.children=="string"||typeof r.children=="number"||typeof r.dangerouslySetInnerHTML=="object"&&r.dangerouslySetInnerHTML!==null&&r.dangerouslySetInnerHTML.__html!=null}var nu=typeof setTimeout=="function"?setTimeout:void 0,J8=typeof clearTimeout=="function"?clearTimeout:void 0,Zx=typeof Promise=="function"?Promise:void 0,e3=typeof queueMicrotask=="function"?queueMicrotask:typeof Zx<"u"?function(e){return Zx.resolve(null).then(e).catch(t3)}:nu;function t3(e){setTimeout(function(){throw e})}function iu(e,r){var a=r,l=0;do{var d=a.nextSibling;if(e.removeChild(a),d&&d.nodeType===8)if(a=d.data,a==="/$"){if(l===0){e.removeChild(d),$i(r);return}l--}else a!=="$"&&a!=="$?"&&a!=="$!"||l++;a=d}while(a);$i(r)}function fn(e){for(;e!=null;e=e.nextSibling){var r=e.nodeType;if(r===1||r===3)break;if(r===8){if(r=e.data,r==="$"||r==="$!"||r==="$?")break;if(r==="/$")return null}}return e}function Yx(e){e=e.previousSibling;for(var r=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(r===0)return e;r--}else a==="/$"&&r++}e=e.previousSibling}return null}var U0=Math.random().toString(36).slice(2),gr="__reactFiber$"+U0,aa="__reactProps$"+U0,_r="__reactContainer$"+U0,au="__reactEvents$"+U0,r3="__reactListeners$"+U0,n3="__reactHandles$"+U0;function $n(e){var r=e[gr];if(r)return r;for(var a=e.parentNode;a;){if(r=a[_r]||a[gr]){if(a=r.alternate,r.child!==null||a!==null&&a.child!==null)for(e=Yx(e);e!==null;){if(a=e[gr])return a;e=Yx(e)}return r}e=a,a=e.parentNode}return null}function oa(e){return e=e[gr]||e[_r],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Q0(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(i(33))}function Eo(e){return e[aa]||null}var ou=[],j0=-1;function xn(e){return{current:e}}function Oe(e){0>j0||(e.current=ou[j0],ou[j0]=null,j0--)}function _e(e,r){j0++,ou[j0]=e.current,e.current=r}var dn={},ut=xn(dn),At=xn(!1),Gn=dn;function W0(e,r){var a=e.type.contextTypes;if(!a)return dn;var l=e.stateNode;if(l&&l.__reactInternalMemoizedUnmaskedChildContext===r)return l.__reactInternalMemoizedMaskedChildContext;var d={},v;for(v in a)d[v]=r[v];return l&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=r,e.__reactInternalMemoizedMaskedChildContext=d),d}function wt(e){return e=e.childContextTypes,e!=null}function Co(){Oe(At),Oe(ut)}function Xx(e,r,a){if(ut.current!==dn)throw Error(i(168));_e(ut,r),_e(At,a)}function Jx(e,r,a){var l=e.stateNode;if(r=r.childContextTypes,typeof l.getChildContext!="function")return a;l=l.getChildContext();for(var d in l)if(!(d in r))throw Error(i(108,ye(e)||"Unknown",d));return X({},a,l)}function Bo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||dn,Gn=ut.current,_e(ut,e),_e(At,At.current),!0}function ed(e,r,a){var l=e.stateNode;if(!l)throw Error(i(169));a?(e=Jx(e,r,Gn),l.__reactInternalMemoizedMergedChildContext=e,Oe(At),Oe(ut),_e(ut,e)):Oe(At),_e(At,a)}var Ir=null,So=!1,su=!1;function td(e){Ir===null?Ir=[e]:Ir.push(e)}function i3(e){So=!0,td(e)}function hn(){if(!su&&Ir!==null){su=!0;var e=0,r=Re;try{var a=Ir;for(Re=1;e<a.length;e++){var l=a[e];do l=l(!0);while(l!==null)}Ir=null,So=!1}catch(d){throw Ir!==null&&(Ir=Ir.slice(e+1)),nx(kl,hn),d}finally{Re=r,su=!1}}return null}var V0=[],q0=0,Do=null,Fo=0,Nt=[],Tt=0,Zn=null,Or=1,Mr="";function Yn(e,r){V0[q0++]=Fo,V0[q0++]=Do,Do=e,Fo=r}function rd(e,r,a){Nt[Tt++]=Or,Nt[Tt++]=Mr,Nt[Tt++]=Zn,Zn=e;var l=Or;e=Mr;var d=32-nr(l)-1;l&=~(1<<d),a+=1;var v=32-nr(r)+d;if(30<v){var C=d-d%5;v=(l&(1<<C)-1).toString(32),l>>=C,d-=C,Or=1<<32-nr(r)+d|a<<d|l,Mr=v+e}else Or=1<<v|a<<d|l,Mr=e}function lu(e){e.return!==null&&(Yn(e,1),rd(e,1,0))}function uu(e){for(;e===Do;)Do=V0[--q0],V0[q0]=null,Fo=V0[--q0],V0[q0]=null;for(;e===Zn;)Zn=Nt[--Tt],Nt[Tt]=null,Mr=Nt[--Tt],Nt[Tt]=null,Or=Nt[--Tt],Nt[Tt]=null}var Pt=null,_t=null,Me=!1,ar=null;function nd(e,r){var a=Qt(5,null,null,0);a.elementType="DELETED",a.stateNode=r,a.return=e,r=e.deletions,r===null?(e.deletions=[a],e.flags|=16):r.push(a)}function id(e,r){switch(e.tag){case 5:var a=e.type;return r=r.nodeType!==1||a.toLowerCase()!==r.nodeName.toLowerCase()?null:r,r!==null?(e.stateNode=r,Pt=e,_t=fn(r.firstChild),!0):!1;case 6:return r=e.pendingProps===""||r.nodeType!==3?null:r,r!==null?(e.stateNode=r,Pt=e,_t=null,!0):!1;case 13:return r=r.nodeType!==8?null:r,r!==null?(a=Zn!==null?{id:Or,overflow:Mr}:null,e.memoizedState={dehydrated:r,treeContext:a,retryLane:1073741824},a=Qt(18,null,null,0),a.stateNode=r,a.return=e,e.child=a,Pt=e,_t=null,!0):!1;default:return!1}}function cu(e){return(e.mode&1)!==0&&(e.flags&128)===0}function fu(e){if(Me){var r=_t;if(r){var a=r;if(!id(e,r)){if(cu(e))throw Error(i(418));r=fn(a.nextSibling);var l=Pt;r&&id(e,r)?nd(l,a):(e.flags=e.flags&-4097|2,Me=!1,Pt=e)}}else{if(cu(e))throw Error(i(418));e.flags=e.flags&-4097|2,Me=!1,Pt=e}}}function ad(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Pt=e}function ko(e){if(e!==Pt)return!1;if(!Me)return ad(e),Me=!0,!1;var r;if((r=e.tag!==3)&&!(r=e.tag!==5)&&(r=e.type,r=r!=="head"&&r!=="body"&&!ru(e.type,e.memoizedProps)),r&&(r=_t)){if(cu(e))throw od(),Error(i(418));for(;r;)nd(e,r),r=fn(r.nextSibling)}if(ad(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,r=0;e;){if(e.nodeType===8){var a=e.data;if(a==="/$"){if(r===0){_t=fn(e.nextSibling);break e}r--}else a!=="$"&&a!=="$!"&&a!=="$?"||r++}e=e.nextSibling}_t=null}}else _t=Pt?fn(e.stateNode.nextSibling):null;return!0}function od(){for(var e=_t;e;)e=fn(e.nextSibling)}function K0(){_t=Pt=null,Me=!1}function xu(e){ar===null?ar=[e]:ar.push(e)}var a3=D.ReactCurrentBatchConfig;function sa(e,r,a){if(e=a.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(a._owner){if(a=a._owner,a){if(a.tag!==1)throw Error(i(309));var l=a.stateNode}if(!l)throw Error(i(147,e));var d=l,v=""+e;return r!==null&&r.ref!==null&&typeof r.ref=="function"&&r.ref._stringRef===v?r.ref:(r=function(C){var P=d.refs;C===null?delete P[v]:P[v]=C},r._stringRef=v,r)}if(typeof e!="string")throw Error(i(284));if(!a._owner)throw Error(i(290,e))}return e}function Ro(e,r){throw e=Object.prototype.toString.call(r),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(r).join(", ")+"}":e))}function sd(e){var r=e._init;return r(e._payload)}function ld(e){function r(H,z){if(e){var j=H.deletions;j===null?(H.deletions=[z],H.flags|=16):j.push(z)}}function a(H,z){if(!e)return null;for(;z!==null;)r(H,z),z=z.sibling;return null}function l(H,z){for(H=new Map;z!==null;)z.key!==null?H.set(z.key,z):H.set(z.index,z),z=z.sibling;return H}function d(H,z){return H=wn(H,z),H.index=0,H.sibling=null,H}function v(H,z,j){return H.index=j,e?(j=H.alternate,j!==null?(j=j.index,j<z?(H.flags|=2,z):j):(H.flags|=2,z)):(H.flags|=1048576,z)}function C(H){return e&&H.alternate===null&&(H.flags|=2),H}function P(H,z,j,ie){return z===null||z.tag!==6?(z=nc(j,H.mode,ie),z.return=H,z):(z=d(z,j),z.return=H,z)}function N(H,z,j,ie){var he=j.type;return he===I?te(H,z,j.props.children,ie,j.key):z!==null&&(z.elementType===he||typeof he=="object"&&he!==null&&he.$$typeof===Y&&sd(he)===z.type)?(ie=d(z,j.props),ie.ref=sa(H,z,j),ie.return=H,ie):(ie=es(j.type,j.key,j.props,null,H.mode,ie),ie.ref=sa(H,z,j),ie.return=H,ie)}function V(H,z,j,ie){return z===null||z.tag!==4||z.stateNode.containerInfo!==j.containerInfo||z.stateNode.implementation!==j.implementation?(z=ic(j,H.mode,ie),z.return=H,z):(z=d(z,j.children||[]),z.return=H,z)}function te(H,z,j,ie,he){return z===null||z.tag!==7?(z=a0(j,H.mode,ie,he),z.return=H,z):(z=d(z,j),z.return=H,z)}function re(H,z,j){if(typeof z=="string"&&z!==""||typeof z=="number")return z=nc(""+z,H.mode,j),z.return=H,z;if(typeof z=="object"&&z!==null){switch(z.$$typeof){case k:return j=es(z.type,z.key,z.props,null,H.mode,j),j.ref=sa(H,null,z),j.return=H,j;case R:return z=ic(z,H.mode,j),z.return=H,z;case Y:var ie=z._init;return re(H,ie(z._payload),j)}if(tr(z)||J(z))return z=a0(z,H.mode,j,null),z.return=H,z;Ro(H,z)}return null}function ee(H,z,j,ie){var he=z!==null?z.key:null;if(typeof j=="string"&&j!==""||typeof j=="number")return he!==null?null:P(H,z,""+j,ie);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case k:return j.key===he?N(H,z,j,ie):null;case R:return j.key===he?V(H,z,j,ie):null;case Y:return he=j._init,ee(H,z,he(j._payload),ie)}if(tr(j)||J(j))return he!==null?null:te(H,z,j,ie,null);Ro(H,j)}return null}function se(H,z,j,ie,he){if(typeof ie=="string"&&ie!==""||typeof ie=="number")return H=H.get(j)||null,P(z,H,""+ie,he);if(typeof ie=="object"&&ie!==null){switch(ie.$$typeof){case k:return H=H.get(ie.key===null?j:ie.key)||null,N(z,H,ie,he);case R:return H=H.get(ie.key===null?j:ie.key)||null,V(z,H,ie,he);case Y:var ve=ie._init;return se(H,z,j,ve(ie._payload),he)}if(tr(ie)||J(ie))return H=H.get(j)||null,te(z,H,ie,he,null);Ro(z,ie)}return null}function xe(H,z,j,ie){for(var he=null,ve=null,me=z,ge=z=0,rt=null;me!==null&&ge<j.length;ge++){me.index>ge?(rt=me,me=null):rt=me.sibling;var De=ee(H,me,j[ge],ie);if(De===null){me===null&&(me=rt);break}e&&me&&De.alternate===null&&r(H,me),z=v(De,z,ge),ve===null?he=De:ve.sibling=De,ve=De,me=rt}if(ge===j.length)return a(H,me),Me&&Yn(H,ge),he;if(me===null){for(;ge<j.length;ge++)me=re(H,j[ge],ie),me!==null&&(z=v(me,z,ge),ve===null?he=me:ve.sibling=me,ve=me);return Me&&Yn(H,ge),he}for(me=l(H,me);ge<j.length;ge++)rt=se(me,H,ge,j[ge],ie),rt!==null&&(e&&rt.alternate!==null&&me.delete(rt.key===null?ge:rt.key),z=v(rt,z,ge),ve===null?he=rt:ve.sibling=rt,ve=rt);return e&&me.forEach(function(En){return r(H,En)}),Me&&Yn(H,ge),he}function de(H,z,j,ie){var he=J(j);if(typeof he!="function")throw Error(i(150));if(j=he.call(j),j==null)throw Error(i(151));for(var ve=he=null,me=z,ge=z=0,rt=null,De=j.next();me!==null&&!De.done;ge++,De=j.next()){me.index>ge?(rt=me,me=null):rt=me.sibling;var En=ee(H,me,De.value,ie);if(En===null){me===null&&(me=rt);break}e&&me&&En.alternate===null&&r(H,me),z=v(En,z,ge),ve===null?he=En:ve.sibling=En,ve=En,me=rt}if(De.done)return a(H,me),Me&&Yn(H,ge),he;if(me===null){for(;!De.done;ge++,De=j.next())De=re(H,De.value,ie),De!==null&&(z=v(De,z,ge),ve===null?he=De:ve.sibling=De,ve=De);return Me&&Yn(H,ge),he}for(me=l(H,me);!De.done;ge++,De=j.next())De=se(me,H,ge,De.value,ie),De!==null&&(e&&De.alternate!==null&&me.delete(De.key===null?ge:De.key),z=v(De,z,ge),ve===null?he=De:ve.sibling=De,ve=De);return e&&me.forEach(function(T3){return r(H,T3)}),Me&&Yn(H,ge),he}function Ve(H,z,j,ie){if(typeof j=="object"&&j!==null&&j.type===I&&j.key===null&&(j=j.props.children),typeof j=="object"&&j!==null){switch(j.$$typeof){case k:e:{for(var he=j.key,ve=z;ve!==null;){if(ve.key===he){if(he=j.type,he===I){if(ve.tag===7){a(H,ve.sibling),z=d(ve,j.props.children),z.return=H,H=z;break e}}else if(ve.elementType===he||typeof he=="object"&&he!==null&&he.$$typeof===Y&&sd(he)===ve.type){a(H,ve.sibling),z=d(ve,j.props),z.ref=sa(H,ve,j),z.return=H,H=z;break e}a(H,ve);break}else r(H,ve);ve=ve.sibling}j.type===I?(z=a0(j.props.children,H.mode,ie,j.key),z.return=H,H=z):(ie=es(j.type,j.key,j.props,null,H.mode,ie),ie.ref=sa(H,z,j),ie.return=H,H=ie)}return C(H);case R:e:{for(ve=j.key;z!==null;){if(z.key===ve)if(z.tag===4&&z.stateNode.containerInfo===j.containerInfo&&z.stateNode.implementation===j.implementation){a(H,z.sibling),z=d(z,j.children||[]),z.return=H,H=z;break e}else{a(H,z);break}else r(H,z);z=z.sibling}z=ic(j,H.mode,ie),z.return=H,H=z}return C(H);case Y:return ve=j._init,Ve(H,z,ve(j._payload),ie)}if(tr(j))return xe(H,z,j,ie);if(J(j))return de(H,z,j,ie);Ro(H,j)}return typeof j=="string"&&j!==""||typeof j=="number"?(j=""+j,z!==null&&z.tag===6?(a(H,z.sibling),z=d(z,j),z.return=H,H=z):(a(H,z),z=nc(j,H.mode,ie),z.return=H,H=z),C(H)):a(H,z)}return Ve}var $0=ld(!0),ud=ld(!1),Po=xn(null),_o=null,G0=null,du=null;function hu(){du=G0=_o=null}function pu(e){var r=Po.current;Oe(Po),e._currentValue=r}function vu(e,r,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&r)!==r?(e.childLanes|=r,l!==null&&(l.childLanes|=r)):l!==null&&(l.childLanes&r)!==r&&(l.childLanes|=r),e===a)break;e=e.return}}function Z0(e,r){_o=e,du=G0=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&r)!==0&&(Et=!0),e.firstContext=null)}function zt(e){var r=e._currentValue;if(du!==e)if(e={context:e,memoizedValue:r,next:null},G0===null){if(_o===null)throw Error(i(308));G0=e,_o.dependencies={lanes:0,firstContext:e}}else G0=G0.next=e;return r}var Xn=null;function mu(e){Xn===null?Xn=[e]:Xn.push(e)}function cd(e,r,a,l){var d=r.interleaved;return d===null?(a.next=a,mu(r)):(a.next=d.next,d.next=a),r.interleaved=a,Lr(e,l)}function Lr(e,r){e.lanes|=r;var a=e.alternate;for(a!==null&&(a.lanes|=r),a=e,e=e.return;e!==null;)e.childLanes|=r,a=e.alternate,a!==null&&(a.childLanes|=r),a=e,e=e.return;return a.tag===3?a.stateNode:null}var pn=!1;function yu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function fd(e,r){e=e.updateQueue,r.updateQueue===e&&(r.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Nr(e,r){return{eventTime:e,lane:r,tag:0,payload:null,callback:null,next:null}}function vn(e,r,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Se&2)!==0){var d=l.pending;return d===null?r.next=r:(r.next=d.next,d.next=r),l.pending=r,Lr(e,a)}return d=l.interleaved,d===null?(r.next=r,mu(l)):(r.next=d.next,d.next=r),l.interleaved=r,Lr(e,a)}function Io(e,r,a){if(r=r.updateQueue,r!==null&&(r=r.shared,(a&4194240)!==0)){var l=r.lanes;l&=e.pendingLanes,a|=l,r.lanes=a,_l(e,a)}}function xd(e,r){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var d=null,v=null;if(a=a.firstBaseUpdate,a!==null){do{var C={eventTime:a.eventTime,lane:a.lane,tag:a.tag,payload:a.payload,callback:a.callback,next:null};v===null?d=v=C:v=v.next=C,a=a.next}while(a!==null);v===null?d=v=r:v=v.next=r}else d=v=r;a={baseState:l.baseState,firstBaseUpdate:d,lastBaseUpdate:v,shared:l.shared,effects:l.effects},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=r:e.next=r,a.lastBaseUpdate=r}function Oo(e,r,a,l){var d=e.updateQueue;pn=!1;var v=d.firstBaseUpdate,C=d.lastBaseUpdate,P=d.shared.pending;if(P!==null){d.shared.pending=null;var N=P,V=N.next;N.next=null,C===null?v=V:C.next=V,C=N;var te=e.alternate;te!==null&&(te=te.updateQueue,P=te.lastBaseUpdate,P!==C&&(P===null?te.firstBaseUpdate=V:P.next=V,te.lastBaseUpdate=N))}if(v!==null){var re=d.baseState;C=0,te=V=N=null,P=v;do{var ee=P.lane,se=P.eventTime;if((l&ee)===ee){te!==null&&(te=te.next={eventTime:se,lane:0,tag:P.tag,payload:P.payload,callback:P.callback,next:null});e:{var xe=e,de=P;switch(ee=r,se=a,de.tag){case 1:if(xe=de.payload,typeof xe=="function"){re=xe.call(se,re,ee);break e}re=xe;break e;case 3:xe.flags=xe.flags&-65537|128;case 0:if(xe=de.payload,ee=typeof xe=="function"?xe.call(se,re,ee):xe,ee==null)break e;re=X({},re,ee);break e;case 2:pn=!0}}P.callback!==null&&P.lane!==0&&(e.flags|=64,ee=d.effects,ee===null?d.effects=[P]:ee.push(P))}else se={eventTime:se,lane:ee,tag:P.tag,payload:P.payload,callback:P.callback,next:null},te===null?(V=te=se,N=re):te=te.next=se,C|=ee;if(P=P.next,P===null){if(P=d.shared.pending,P===null)break;ee=P,P=ee.next,ee.next=null,d.lastBaseUpdate=ee,d.shared.pending=null}}while(!0);if(te===null&&(N=re),d.baseState=N,d.firstBaseUpdate=V,d.lastBaseUpdate=te,r=d.shared.interleaved,r!==null){d=r;do C|=d.lane,d=d.next;while(d!==r)}else v===null&&(d.shared.lanes=0);t0|=C,e.lanes=C,e.memoizedState=re}}function dd(e,r,a){if(e=r.effects,r.effects=null,e!==null)for(r=0;r<e.length;r++){var l=e[r],d=l.callback;if(d!==null){if(l.callback=null,l=a,typeof d!="function")throw Error(i(191,d));d.call(l)}}}var la={},br=xn(la),ua=xn(la),ca=xn(la);function Jn(e){if(e===la)throw Error(i(174));return e}function gu(e,r){switch(_e(ca,r),_e(ua,e),_e(br,la),e=r.nodeType,e){case 9:case 11:r=(r=r.documentElement)?r.namespaceURI:Rr(null,"");break;default:e=e===8?r.parentNode:r,r=e.namespaceURI||null,e=e.tagName,r=Rr(r,e)}Oe(br),_e(br,r)}function Y0(){Oe(br),Oe(ua),Oe(ca)}function hd(e){Jn(ca.current);var r=Jn(br.current),a=Rr(r,e.type);r!==a&&(_e(ua,e),_e(br,a))}function bu(e){ua.current===e&&(Oe(br),Oe(ua))}var ze=xn(0);function Mo(e){for(var r=e;r!==null;){if(r.tag===13){var a=r.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||a.data==="$!"))return r}else if(r.tag===19&&r.memoizedProps.revealOrder!==void 0){if((r.flags&128)!==0)return r}else if(r.child!==null){r.child.return=r,r=r.child;continue}if(r===e)break;for(;r.sibling===null;){if(r.return===null||r.return===e)return null;r=r.return}r.sibling.return=r.return,r=r.sibling}return null}var Au=[];function wu(){for(var e=0;e<Au.length;e++)Au[e]._workInProgressVersionPrimary=null;Au.length=0}var Lo=D.ReactCurrentDispatcher,Eu=D.ReactCurrentBatchConfig,e0=0,He=null,Ze=null,et=null,No=!1,fa=!1,xa=0,o3=0;function ct(){throw Error(i(321))}function Cu(e,r){if(r===null)return!1;for(var a=0;a<r.length&&a<e.length;a++)if(!ir(e[a],r[a]))return!1;return!0}function Bu(e,r,a,l,d,v){if(e0=v,He=r,r.memoizedState=null,r.updateQueue=null,r.lanes=0,Lo.current=e===null||e.memoizedState===null?c3:f3,e=a(l,d),fa){v=0;do{if(fa=!1,xa=0,25<=v)throw Error(i(301));v+=1,et=Ze=null,r.updateQueue=null,Lo.current=x3,e=a(l,d)}while(fa)}if(Lo.current=Ho,r=Ze!==null&&Ze.next!==null,e0=0,et=Ze=He=null,No=!1,r)throw Error(i(300));return e}function Su(){var e=xa!==0;return xa=0,e}function Ar(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return et===null?He.memoizedState=et=e:et=et.next=e,et}function Ht(){if(Ze===null){var e=He.alternate;e=e!==null?e.memoizedState:null}else e=Ze.next;var r=et===null?He.memoizedState:et.next;if(r!==null)et=r,Ze=e;else{if(e===null)throw Error(i(310));Ze=e,e={memoizedState:Ze.memoizedState,baseState:Ze.baseState,baseQueue:Ze.baseQueue,queue:Ze.queue,next:null},et===null?He.memoizedState=et=e:et=et.next=e}return et}function da(e,r){return typeof r=="function"?r(e):r}function Du(e){var r=Ht(),a=r.queue;if(a===null)throw Error(i(311));a.lastRenderedReducer=e;var l=Ze,d=l.baseQueue,v=a.pending;if(v!==null){if(d!==null){var C=d.next;d.next=v.next,v.next=C}l.baseQueue=d=v,a.pending=null}if(d!==null){v=d.next,l=l.baseState;var P=C=null,N=null,V=v;do{var te=V.lane;if((e0&te)===te)N!==null&&(N=N.next={lane:0,action:V.action,hasEagerState:V.hasEagerState,eagerState:V.eagerState,next:null}),l=V.hasEagerState?V.eagerState:e(l,V.action);else{var re={lane:te,action:V.action,hasEagerState:V.hasEagerState,eagerState:V.eagerState,next:null};N===null?(P=N=re,C=l):N=N.next=re,He.lanes|=te,t0|=te}V=V.next}while(V!==null&&V!==v);N===null?C=l:N.next=P,ir(l,r.memoizedState)||(Et=!0),r.memoizedState=l,r.baseState=C,r.baseQueue=N,a.lastRenderedState=l}if(e=a.interleaved,e!==null){d=e;do v=d.lane,He.lanes|=v,t0|=v,d=d.next;while(d!==e)}else d===null&&(a.lanes=0);return[r.memoizedState,a.dispatch]}function Fu(e){var r=Ht(),a=r.queue;if(a===null)throw Error(i(311));a.lastRenderedReducer=e;var l=a.dispatch,d=a.pending,v=r.memoizedState;if(d!==null){a.pending=null;var C=d=d.next;do v=e(v,C.action),C=C.next;while(C!==d);ir(v,r.memoizedState)||(Et=!0),r.memoizedState=v,r.baseQueue===null&&(r.baseState=v),a.lastRenderedState=v}return[v,l]}function pd(){}function vd(e,r){var a=He,l=Ht(),d=r(),v=!ir(l.memoizedState,d);if(v&&(l.memoizedState=d,Et=!0),l=l.queue,ku(gd.bind(null,a,l,e),[e]),l.getSnapshot!==r||v||et!==null&&et.memoizedState.tag&1){if(a.flags|=2048,ha(9,yd.bind(null,a,l,d,r),void 0,null),tt===null)throw Error(i(349));(e0&30)!==0||md(a,r,d)}return d}function md(e,r,a){e.flags|=16384,e={getSnapshot:r,value:a},r=He.updateQueue,r===null?(r={lastEffect:null,stores:null},He.updateQueue=r,r.stores=[e]):(a=r.stores,a===null?r.stores=[e]:a.push(e))}function yd(e,r,a,l){r.value=a,r.getSnapshot=l,bd(r)&&Ad(e)}function gd(e,r,a){return a(function(){bd(r)&&Ad(e)})}function bd(e){var r=e.getSnapshot;e=e.value;try{var a=r();return!ir(e,a)}catch{return!0}}function Ad(e){var r=Lr(e,1);r!==null&&ur(r,e,1,-1)}function wd(e){var r=Ar();return typeof e=="function"&&(e=e()),r.memoizedState=r.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:da,lastRenderedState:e},r.queue=e,e=e.dispatch=u3.bind(null,He,e),[r.memoizedState,e]}function ha(e,r,a,l){return e={tag:e,create:r,destroy:a,deps:l,next:null},r=He.updateQueue,r===null?(r={lastEffect:null,stores:null},He.updateQueue=r,r.lastEffect=e.next=e):(a=r.lastEffect,a===null?r.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,r.lastEffect=e)),e}function Ed(){return Ht().memoizedState}function To(e,r,a,l){var d=Ar();He.flags|=e,d.memoizedState=ha(1|r,a,void 0,l===void 0?null:l)}function zo(e,r,a,l){var d=Ht();l=l===void 0?null:l;var v=void 0;if(Ze!==null){var C=Ze.memoizedState;if(v=C.destroy,l!==null&&Cu(l,C.deps)){d.memoizedState=ha(r,a,v,l);return}}He.flags|=e,d.memoizedState=ha(1|r,a,v,l)}function Cd(e,r){return To(8390656,8,e,r)}function ku(e,r){return zo(2048,8,e,r)}function Bd(e,r){return zo(4,2,e,r)}function Sd(e,r){return zo(4,4,e,r)}function Dd(e,r){if(typeof r=="function")return e=e(),r(e),function(){r(null)};if(r!=null)return e=e(),r.current=e,function(){r.current=null}}function Fd(e,r,a){return a=a!=null?a.concat([e]):null,zo(4,4,Dd.bind(null,r,e),a)}function Ru(){}function kd(e,r){var a=Ht();r=r===void 0?null:r;var l=a.memoizedState;return l!==null&&r!==null&&Cu(r,l[1])?l[0]:(a.memoizedState=[e,r],e)}function Rd(e,r){var a=Ht();r=r===void 0?null:r;var l=a.memoizedState;return l!==null&&r!==null&&Cu(r,l[1])?l[0]:(e=e(),a.memoizedState=[e,r],e)}function Pd(e,r,a){return(e0&21)===0?(e.baseState&&(e.baseState=!1,Et=!0),e.memoizedState=a):(ir(a,r)||(a=sx(),He.lanes|=a,t0|=a,e.baseState=!0),r)}function s3(e,r){var a=Re;Re=a!==0&&4>a?a:4,e(!0);var l=Eu.transition;Eu.transition={};try{e(!1),r()}finally{Re=a,Eu.transition=l}}function _d(){return Ht().memoizedState}function l3(e,r,a){var l=bn(e);if(a={lane:l,action:a,hasEagerState:!1,eagerState:null,next:null},Id(e))Od(r,a);else if(a=cd(e,r,a,l),a!==null){var d=vt();ur(a,e,l,d),Md(a,r,l)}}function u3(e,r,a){var l=bn(e),d={lane:l,action:a,hasEagerState:!1,eagerState:null,next:null};if(Id(e))Od(r,d);else{var v=e.alternate;if(e.lanes===0&&(v===null||v.lanes===0)&&(v=r.lastRenderedReducer,v!==null))try{var C=r.lastRenderedState,P=v(C,a);if(d.hasEagerState=!0,d.eagerState=P,ir(P,C)){var N=r.interleaved;N===null?(d.next=d,mu(r)):(d.next=N.next,N.next=d),r.interleaved=d;return}}catch{}finally{}a=cd(e,r,d,l),a!==null&&(d=vt(),ur(a,e,l,d),Md(a,r,l))}}function Id(e){var r=e.alternate;return e===He||r!==null&&r===He}function Od(e,r){fa=No=!0;var a=e.pending;a===null?r.next=r:(r.next=a.next,a.next=r),e.pending=r}function Md(e,r,a){if((a&4194240)!==0){var l=r.lanes;l&=e.pendingLanes,a|=l,r.lanes=a,_l(e,a)}}var Ho={readContext:zt,useCallback:ct,useContext:ct,useEffect:ct,useImperativeHandle:ct,useInsertionEffect:ct,useLayoutEffect:ct,useMemo:ct,useReducer:ct,useRef:ct,useState:ct,useDebugValue:ct,useDeferredValue:ct,useTransition:ct,useMutableSource:ct,useSyncExternalStore:ct,useId:ct,unstable_isNewReconciler:!1},c3={readContext:zt,useCallback:function(e,r){return Ar().memoizedState=[e,r===void 0?null:r],e},useContext:zt,useEffect:Cd,useImperativeHandle:function(e,r,a){return a=a!=null?a.concat([e]):null,To(4194308,4,Dd.bind(null,r,e),a)},useLayoutEffect:function(e,r){return To(4194308,4,e,r)},useInsertionEffect:function(e,r){return To(4,2,e,r)},useMemo:function(e,r){var a=Ar();return r=r===void 0?null:r,e=e(),a.memoizedState=[e,r],e},useReducer:function(e,r,a){var l=Ar();return r=a!==void 0?a(r):r,l.memoizedState=l.baseState=r,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:r},l.queue=e,e=e.dispatch=l3.bind(null,He,e),[l.memoizedState,e]},useRef:function(e){var r=Ar();return e={current:e},r.memoizedState=e},useState:wd,useDebugValue:Ru,useDeferredValue:function(e){return Ar().memoizedState=e},useTransition:function(){var e=wd(!1),r=e[0];return e=s3.bind(null,e[1]),Ar().memoizedState=e,[r,e]},useMutableSource:function(){},useSyncExternalStore:function(e,r,a){var l=He,d=Ar();if(Me){if(a===void 0)throw Error(i(407));a=a()}else{if(a=r(),tt===null)throw Error(i(349));(e0&30)!==0||md(l,r,a)}d.memoizedState=a;var v={value:a,getSnapshot:r};return d.queue=v,Cd(gd.bind(null,l,v,e),[e]),l.flags|=2048,ha(9,yd.bind(null,l,v,a,r),void 0,null),a},useId:function(){var e=Ar(),r=tt.identifierPrefix;if(Me){var a=Mr,l=Or;a=(l&~(1<<32-nr(l)-1)).toString(32)+a,r=":"+r+"R"+a,a=xa++,0<a&&(r+="H"+a.toString(32)),r+=":"}else a=o3++,r=":"+r+"r"+a.toString(32)+":";return e.memoizedState=r},unstable_isNewReconciler:!1},f3={readContext:zt,useCallback:kd,useContext:zt,useEffect:ku,useImperativeHandle:Fd,useInsertionEffect:Bd,useLayoutEffect:Sd,useMemo:Rd,useReducer:Du,useRef:Ed,useState:function(){return Du(da)},useDebugValue:Ru,useDeferredValue:function(e){var r=Ht();return Pd(r,Ze.memoizedState,e)},useTransition:function(){var e=Du(da)[0],r=Ht().memoizedState;return[e,r]},useMutableSource:pd,useSyncExternalStore:vd,useId:_d,unstable_isNewReconciler:!1},x3={readContext:zt,useCallback:kd,useContext:zt,useEffect:ku,useImperativeHandle:Fd,useInsertionEffect:Bd,useLayoutEffect:Sd,useMemo:Rd,useReducer:Fu,useRef:Ed,useState:function(){return Fu(da)},useDebugValue:Ru,useDeferredValue:function(e){var r=Ht();return Ze===null?r.memoizedState=e:Pd(r,Ze.memoizedState,e)},useTransition:function(){var e=Fu(da)[0],r=Ht().memoizedState;return[e,r]},useMutableSource:pd,useSyncExternalStore:vd,useId:_d,unstable_isNewReconciler:!1};function or(e,r){if(e&&e.defaultProps){r=X({},r),e=e.defaultProps;for(var a in e)r[a]===void 0&&(r[a]=e[a]);return r}return r}function Pu(e,r,a,l){r=e.memoizedState,a=a(l,r),a=a==null?r:X({},r,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var Uo={isMounted:function(e){return(e=e._reactInternals)?Kn(e)===e:!1},enqueueSetState:function(e,r,a){e=e._reactInternals;var l=vt(),d=bn(e),v=Nr(l,d);v.payload=r,a!=null&&(v.callback=a),r=vn(e,v,d),r!==null&&(ur(r,e,d,l),Io(r,e,d))},enqueueReplaceState:function(e,r,a){e=e._reactInternals;var l=vt(),d=bn(e),v=Nr(l,d);v.tag=1,v.payload=r,a!=null&&(v.callback=a),r=vn(e,v,d),r!==null&&(ur(r,e,d,l),Io(r,e,d))},enqueueForceUpdate:function(e,r){e=e._reactInternals;var a=vt(),l=bn(e),d=Nr(a,l);d.tag=2,r!=null&&(d.callback=r),r=vn(e,d,l),r!==null&&(ur(r,e,l,a),Io(r,e,l))}};function Ld(e,r,a,l,d,v,C){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,v,C):r.prototype&&r.prototype.isPureReactComponent?!ea(a,l)||!ea(d,v):!0}function Nd(e,r,a){var l=!1,d=dn,v=r.contextType;return typeof v=="object"&&v!==null?v=zt(v):(d=wt(r)?Gn:ut.current,l=r.contextTypes,v=(l=l!=null)?W0(e,d):dn),r=new r(a,v),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Uo,e.stateNode=r,r._reactInternals=e,l&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=d,e.__reactInternalMemoizedMaskedChildContext=v),r}function Td(e,r,a,l){e=r.state,typeof r.componentWillReceiveProps=="function"&&r.componentWillReceiveProps(a,l),typeof r.UNSAFE_componentWillReceiveProps=="function"&&r.UNSAFE_componentWillReceiveProps(a,l),r.state!==e&&Uo.enqueueReplaceState(r,r.state,null)}function _u(e,r,a,l){var d=e.stateNode;d.props=a,d.state=e.memoizedState,d.refs={},yu(e);var v=r.contextType;typeof v=="object"&&v!==null?d.context=zt(v):(v=wt(r)?Gn:ut.current,d.context=W0(e,v)),d.state=e.memoizedState,v=r.getDerivedStateFromProps,typeof v=="function"&&(Pu(e,r,v,a),d.state=e.memoizedState),typeof r.getDerivedStateFromProps=="function"||typeof d.getSnapshotBeforeUpdate=="function"||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(r=d.state,typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount(),r!==d.state&&Uo.enqueueReplaceState(d,d.state,null),Oo(e,a,d,l),d.state=e.memoizedState),typeof d.componentDidMount=="function"&&(e.flags|=4194308)}function X0(e,r){try{var a="",l=r;do a+=fe(l),l=l.return;while(l);var d=a}catch(v){d=`
Error generating stack: `+v.message+`
`+v.stack}return{value:e,source:r,stack:d,digest:null}}function Iu(e,r,a){return{value:e,source:null,stack:a??null,digest:r??null}}function Ou(e,r){try{console.error(r.value)}catch(a){setTimeout(function(){throw a})}}var d3=typeof WeakMap=="function"?WeakMap:Map;function zd(e,r,a){a=Nr(-1,a),a.tag=3,a.payload={element:null};var l=r.value;return a.callback=function(){$o||($o=!0,Gu=l),Ou(e,r)},a}function Hd(e,r,a){a=Nr(-1,a),a.tag=3;var l=e.type.getDerivedStateFromError;if(typeof l=="function"){var d=r.value;a.payload=function(){return l(d)},a.callback=function(){Ou(e,r)}}var v=e.stateNode;return v!==null&&typeof v.componentDidCatch=="function"&&(a.callback=function(){Ou(e,r),typeof l!="function"&&(yn===null?yn=new Set([this]):yn.add(this));var C=r.stack;this.componentDidCatch(r.value,{componentStack:C!==null?C:""})}),a}function Ud(e,r,a){var l=e.pingCache;if(l===null){l=e.pingCache=new d3;var d=new Set;l.set(r,d)}else d=l.get(r),d===void 0&&(d=new Set,l.set(r,d));d.has(a)||(d.add(a),e=D3.bind(null,e,r,a),r.then(e,e))}function Qd(e){do{var r;if((r=e.tag===13)&&(r=e.memoizedState,r=r!==null?r.dehydrated!==null:!0),r)return e;e=e.return}while(e!==null);return null}function jd(e,r,a,l,d){return(e.mode&1)===0?(e===r?e.flags|=65536:(e.flags|=128,a.flags|=131072,a.flags&=-52805,a.tag===1&&(a.alternate===null?a.tag=17:(r=Nr(-1,1),r.tag=2,vn(a,r,1))),a.lanes|=1),e):(e.flags|=65536,e.lanes=d,e)}var h3=D.ReactCurrentOwner,Et=!1;function pt(e,r,a,l){r.child=e===null?ud(r,null,a,l):$0(r,e.child,a,l)}function Wd(e,r,a,l,d){a=a.render;var v=r.ref;return Z0(r,d),l=Bu(e,r,a,l,v,d),a=Su(),e!==null&&!Et?(r.updateQueue=e.updateQueue,r.flags&=-2053,e.lanes&=~d,Tr(e,r,d)):(Me&&a&&lu(r),r.flags|=1,pt(e,r,l,d),r.child)}function Vd(e,r,a,l,d){if(e===null){var v=a.type;return typeof v=="function"&&!rc(v)&&v.defaultProps===void 0&&a.compare===null&&a.defaultProps===void 0?(r.tag=15,r.type=v,qd(e,r,v,l,d)):(e=es(a.type,null,l,r,r.mode,d),e.ref=r.ref,e.return=r,r.child=e)}if(v=e.child,(e.lanes&d)===0){var C=v.memoizedProps;if(a=a.compare,a=a!==null?a:ea,a(C,l)&&e.ref===r.ref)return Tr(e,r,d)}return r.flags|=1,e=wn(v,l),e.ref=r.ref,e.return=r,r.child=e}function qd(e,r,a,l,d){if(e!==null){var v=e.memoizedProps;if(ea(v,l)&&e.ref===r.ref)if(Et=!1,r.pendingProps=l=v,(e.lanes&d)!==0)(e.flags&131072)!==0&&(Et=!0);else return r.lanes=e.lanes,Tr(e,r,d)}return Mu(e,r,a,l,d)}function Kd(e,r,a){var l=r.pendingProps,d=l.children,v=e!==null?e.memoizedState:null;if(l.mode==="hidden")if((r.mode&1)===0)r.memoizedState={baseLanes:0,cachePool:null,transitions:null},_e(ei,It),It|=a;else{if((a&1073741824)===0)return e=v!==null?v.baseLanes|a:a,r.lanes=r.childLanes=1073741824,r.memoizedState={baseLanes:e,cachePool:null,transitions:null},r.updateQueue=null,_e(ei,It),It|=e,null;r.memoizedState={baseLanes:0,cachePool:null,transitions:null},l=v!==null?v.baseLanes:a,_e(ei,It),It|=l}else v!==null?(l=v.baseLanes|a,r.memoizedState=null):l=a,_e(ei,It),It|=l;return pt(e,r,d,a),r.child}function $d(e,r){var a=r.ref;(e===null&&a!==null||e!==null&&e.ref!==a)&&(r.flags|=512,r.flags|=2097152)}function Mu(e,r,a,l,d){var v=wt(a)?Gn:ut.current;return v=W0(r,v),Z0(r,d),a=Bu(e,r,a,l,v,d),l=Su(),e!==null&&!Et?(r.updateQueue=e.updateQueue,r.flags&=-2053,e.lanes&=~d,Tr(e,r,d)):(Me&&l&&lu(r),r.flags|=1,pt(e,r,a,d),r.child)}function Gd(e,r,a,l,d){if(wt(a)){var v=!0;Bo(r)}else v=!1;if(Z0(r,d),r.stateNode===null)jo(e,r),Nd(r,a,l),_u(r,a,l,d),l=!0;else if(e===null){var C=r.stateNode,P=r.memoizedProps;C.props=P;var N=C.context,V=a.contextType;typeof V=="object"&&V!==null?V=zt(V):(V=wt(a)?Gn:ut.current,V=W0(r,V));var te=a.getDerivedStateFromProps,re=typeof te=="function"||typeof C.getSnapshotBeforeUpdate=="function";re||typeof C.UNSAFE_componentWillReceiveProps!="function"&&typeof C.componentWillReceiveProps!="function"||(P!==l||N!==V)&&Td(r,C,l,V),pn=!1;var ee=r.memoizedState;C.state=ee,Oo(r,l,C,d),N=r.memoizedState,P!==l||ee!==N||At.current||pn?(typeof te=="function"&&(Pu(r,a,te,l),N=r.memoizedState),(P=pn||Ld(r,a,P,l,ee,N,V))?(re||typeof C.UNSAFE_componentWillMount!="function"&&typeof C.componentWillMount!="function"||(typeof C.componentWillMount=="function"&&C.componentWillMount(),typeof C.UNSAFE_componentWillMount=="function"&&C.UNSAFE_componentWillMount()),typeof C.componentDidMount=="function"&&(r.flags|=4194308)):(typeof C.componentDidMount=="function"&&(r.flags|=4194308),r.memoizedProps=l,r.memoizedState=N),C.props=l,C.state=N,C.context=V,l=P):(typeof C.componentDidMount=="function"&&(r.flags|=4194308),l=!1)}else{C=r.stateNode,fd(e,r),P=r.memoizedProps,V=r.type===r.elementType?P:or(r.type,P),C.props=V,re=r.pendingProps,ee=C.context,N=a.contextType,typeof N=="object"&&N!==null?N=zt(N):(N=wt(a)?Gn:ut.current,N=W0(r,N));var se=a.getDerivedStateFromProps;(te=typeof se=="function"||typeof C.getSnapshotBeforeUpdate=="function")||typeof C.UNSAFE_componentWillReceiveProps!="function"&&typeof C.componentWillReceiveProps!="function"||(P!==re||ee!==N)&&Td(r,C,l,N),pn=!1,ee=r.memoizedState,C.state=ee,Oo(r,l,C,d);var xe=r.memoizedState;P!==re||ee!==xe||At.current||pn?(typeof se=="function"&&(Pu(r,a,se,l),xe=r.memoizedState),(V=pn||Ld(r,a,V,l,ee,xe,N)||!1)?(te||typeof C.UNSAFE_componentWillUpdate!="function"&&typeof C.componentWillUpdate!="function"||(typeof C.componentWillUpdate=="function"&&C.componentWillUpdate(l,xe,N),typeof C.UNSAFE_componentWillUpdate=="function"&&C.UNSAFE_componentWillUpdate(l,xe,N)),typeof C.componentDidUpdate=="function"&&(r.flags|=4),typeof C.getSnapshotBeforeUpdate=="function"&&(r.flags|=1024)):(typeof C.componentDidUpdate!="function"||P===e.memoizedProps&&ee===e.memoizedState||(r.flags|=4),typeof C.getSnapshotBeforeUpdate!="function"||P===e.memoizedProps&&ee===e.memoizedState||(r.flags|=1024),r.memoizedProps=l,r.memoizedState=xe),C.props=l,C.state=xe,C.context=N,l=V):(typeof C.componentDidUpdate!="function"||P===e.memoizedProps&&ee===e.memoizedState||(r.flags|=4),typeof C.getSnapshotBeforeUpdate!="function"||P===e.memoizedProps&&ee===e.memoizedState||(r.flags|=1024),l=!1)}return Lu(e,r,a,l,v,d)}function Lu(e,r,a,l,d,v){$d(e,r);var C=(r.flags&128)!==0;if(!l&&!C)return d&&ed(r,a,!1),Tr(e,r,v);l=r.stateNode,h3.current=r;var P=C&&typeof a.getDerivedStateFromError!="function"?null:l.render();return r.flags|=1,e!==null&&C?(r.child=$0(r,e.child,null,v),r.child=$0(r,null,P,v)):pt(e,r,P,v),r.memoizedState=l.state,d&&ed(r,a,!0),r.child}function Zd(e){var r=e.stateNode;r.pendingContext?Xx(e,r.pendingContext,r.pendingContext!==r.context):r.context&&Xx(e,r.context,!1),gu(e,r.containerInfo)}function Yd(e,r,a,l,d){return K0(),xu(d),r.flags|=256,pt(e,r,a,l),r.child}var Nu={dehydrated:null,treeContext:null,retryLane:0};function Tu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Xd(e,r,a){var l=r.pendingProps,d=ze.current,v=!1,C=(r.flags&128)!==0,P;if((P=C)||(P=e!==null&&e.memoizedState===null?!1:(d&2)!==0),P?(v=!0,r.flags&=-129):(e===null||e.memoizedState!==null)&&(d|=1),_e(ze,d&1),e===null)return fu(r),e=r.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((r.mode&1)===0?r.lanes=1:e.data==="$!"?r.lanes=8:r.lanes=1073741824,null):(C=l.children,e=l.fallback,v?(l=r.mode,v=r.child,C={mode:"hidden",children:C},(l&1)===0&&v!==null?(v.childLanes=0,v.pendingProps=C):v=ts(C,l,0,null),e=a0(e,l,a,null),v.return=r,e.return=r,v.sibling=e,r.child=v,r.child.memoizedState=Tu(a),r.memoizedState=Nu,e):zu(r,C));if(d=e.memoizedState,d!==null&&(P=d.dehydrated,P!==null))return p3(e,r,C,l,P,d,a);if(v){v=l.fallback,C=r.mode,d=e.child,P=d.sibling;var N={mode:"hidden",children:l.children};return(C&1)===0&&r.child!==d?(l=r.child,l.childLanes=0,l.pendingProps=N,r.deletions=null):(l=wn(d,N),l.subtreeFlags=d.subtreeFlags&14680064),P!==null?v=wn(P,v):(v=a0(v,C,a,null),v.flags|=2),v.return=r,l.return=r,l.sibling=v,r.child=l,l=v,v=r.child,C=e.child.memoizedState,C=C===null?Tu(a):{baseLanes:C.baseLanes|a,cachePool:null,transitions:C.transitions},v.memoizedState=C,v.childLanes=e.childLanes&~a,r.memoizedState=Nu,l}return v=e.child,e=v.sibling,l=wn(v,{mode:"visible",children:l.children}),(r.mode&1)===0&&(l.lanes=a),l.return=r,l.sibling=null,e!==null&&(a=r.deletions,a===null?(r.deletions=[e],r.flags|=16):a.push(e)),r.child=l,r.memoizedState=null,l}function zu(e,r){return r=ts({mode:"visible",children:r},e.mode,0,null),r.return=e,e.child=r}function Qo(e,r,a,l){return l!==null&&xu(l),$0(r,e.child,null,a),e=zu(r,r.pendingProps.children),e.flags|=2,r.memoizedState=null,e}function p3(e,r,a,l,d,v,C){if(a)return r.flags&256?(r.flags&=-257,l=Iu(Error(i(422))),Qo(e,r,C,l)):r.memoizedState!==null?(r.child=e.child,r.flags|=128,null):(v=l.fallback,d=r.mode,l=ts({mode:"visible",children:l.children},d,0,null),v=a0(v,d,C,null),v.flags|=2,l.return=r,v.return=r,l.sibling=v,r.child=l,(r.mode&1)!==0&&$0(r,e.child,null,C),r.child.memoizedState=Tu(C),r.memoizedState=Nu,v);if((r.mode&1)===0)return Qo(e,r,C,null);if(d.data==="$!"){if(l=d.nextSibling&&d.nextSibling.dataset,l)var P=l.dgst;return l=P,v=Error(i(419)),l=Iu(v,l,void 0),Qo(e,r,C,l)}if(P=(C&e.childLanes)!==0,Et||P){if(l=tt,l!==null){switch(C&-C){case 4:d=2;break;case 16:d=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:d=32;break;case 536870912:d=268435456;break;default:d=0}d=(d&(l.suspendedLanes|C))!==0?0:d,d!==0&&d!==v.retryLane&&(v.retryLane=d,Lr(e,d),ur(l,e,d,-1))}return tc(),l=Iu(Error(i(421))),Qo(e,r,C,l)}return d.data==="$?"?(r.flags|=128,r.child=e.child,r=F3.bind(null,e),d._reactRetry=r,null):(e=v.treeContext,_t=fn(d.nextSibling),Pt=r,Me=!0,ar=null,e!==null&&(Nt[Tt++]=Or,Nt[Tt++]=Mr,Nt[Tt++]=Zn,Or=e.id,Mr=e.overflow,Zn=r),r=zu(r,l.children),r.flags|=4096,r)}function Jd(e,r,a){e.lanes|=r;var l=e.alternate;l!==null&&(l.lanes|=r),vu(e.return,r,a)}function Hu(e,r,a,l,d){var v=e.memoizedState;v===null?e.memoizedState={isBackwards:r,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:d}:(v.isBackwards=r,v.rendering=null,v.renderingStartTime=0,v.last=l,v.tail=a,v.tailMode=d)}function e1(e,r,a){var l=r.pendingProps,d=l.revealOrder,v=l.tail;if(pt(e,r,l.children,a),l=ze.current,(l&2)!==0)l=l&1|2,r.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=r.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Jd(e,a,r);else if(e.tag===19)Jd(e,a,r);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===r)break e;for(;e.sibling===null;){if(e.return===null||e.return===r)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}if(_e(ze,l),(r.mode&1)===0)r.memoizedState=null;else switch(d){case"forwards":for(a=r.child,d=null;a!==null;)e=a.alternate,e!==null&&Mo(e)===null&&(d=a),a=a.sibling;a=d,a===null?(d=r.child,r.child=null):(d=a.sibling,a.sibling=null),Hu(r,!1,d,a,v);break;case"backwards":for(a=null,d=r.child,r.child=null;d!==null;){if(e=d.alternate,e!==null&&Mo(e)===null){r.child=d;break}e=d.sibling,d.sibling=a,a=d,d=e}Hu(r,!0,a,null,v);break;case"together":Hu(r,!1,null,null,void 0);break;default:r.memoizedState=null}return r.child}function jo(e,r){(r.mode&1)===0&&e!==null&&(e.alternate=null,r.alternate=null,r.flags|=2)}function Tr(e,r,a){if(e!==null&&(r.dependencies=e.dependencies),t0|=r.lanes,(a&r.childLanes)===0)return null;if(e!==null&&r.child!==e.child)throw Error(i(153));if(r.child!==null){for(e=r.child,a=wn(e,e.pendingProps),r.child=a,a.return=r;e.sibling!==null;)e=e.sibling,a=a.sibling=wn(e,e.pendingProps),a.return=r;a.sibling=null}return r.child}function v3(e,r,a){switch(r.tag){case 3:Zd(r),K0();break;case 5:hd(r);break;case 1:wt(r.type)&&Bo(r);break;case 4:gu(r,r.stateNode.containerInfo);break;case 10:var l=r.type._context,d=r.memoizedProps.value;_e(Po,l._currentValue),l._currentValue=d;break;case 13:if(l=r.memoizedState,l!==null)return l.dehydrated!==null?(_e(ze,ze.current&1),r.flags|=128,null):(a&r.child.childLanes)!==0?Xd(e,r,a):(_e(ze,ze.current&1),e=Tr(e,r,a),e!==null?e.sibling:null);_e(ze,ze.current&1);break;case 19:if(l=(a&r.childLanes)!==0,(e.flags&128)!==0){if(l)return e1(e,r,a);r.flags|=128}if(d=r.memoizedState,d!==null&&(d.rendering=null,d.tail=null,d.lastEffect=null),_e(ze,ze.current),l)break;return null;case 22:case 23:return r.lanes=0,Kd(e,r,a)}return Tr(e,r,a)}var t1,Uu,r1,n1;t1=function(e,r){for(var a=r.child;a!==null;){if(a.tag===5||a.tag===6)e.appendChild(a.stateNode);else if(a.tag!==4&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===r)break;for(;a.sibling===null;){if(a.return===null||a.return===r)return;a=a.return}a.sibling.return=a.return,a=a.sibling}},Uu=function(){},r1=function(e,r,a,l){var d=e.memoizedProps;if(d!==l){e=r.stateNode,Jn(br.current);var v=null;switch(a){case"input":d=Ue(e,d),l=Ue(e,l),v=[];break;case"select":d=X({},d,{value:void 0}),l=X({},l,{value:void 0}),v=[];break;case"textarea":d=lt(e,d),l=lt(e,l),v=[];break;default:typeof d.onClick!="function"&&typeof l.onClick=="function"&&(e.onclick=wo)}Li(a,l);var C;a=null;for(V in d)if(!l.hasOwnProperty(V)&&d.hasOwnProperty(V)&&d[V]!=null)if(V==="style"){var P=d[V];for(C in P)P.hasOwnProperty(C)&&(a||(a={}),a[C]="")}else V!=="dangerouslySetInnerHTML"&&V!=="children"&&V!=="suppressContentEditableWarning"&&V!=="suppressHydrationWarning"&&V!=="autoFocus"&&(s.hasOwnProperty(V)?v||(v=[]):(v=v||[]).push(V,null));for(V in l){var N=l[V];if(P=d?.[V],l.hasOwnProperty(V)&&N!==P&&(N!=null||P!=null))if(V==="style")if(P){for(C in P)!P.hasOwnProperty(C)||N&&N.hasOwnProperty(C)||(a||(a={}),a[C]="");for(C in N)N.hasOwnProperty(C)&&P[C]!==N[C]&&(a||(a={}),a[C]=N[C])}else a||(v||(v=[]),v.push(V,a)),a=N;else V==="dangerouslySetInnerHTML"?(N=N?N.__html:void 0,P=P?P.__html:void 0,N!=null&&P!==N&&(v=v||[]).push(V,N)):V==="children"?typeof N!="string"&&typeof N!="number"||(v=v||[]).push(V,""+N):V!=="suppressContentEditableWarning"&&V!=="suppressHydrationWarning"&&(s.hasOwnProperty(V)?(N!=null&&V==="onScroll"&&Ie("scroll",e),v||P===N||(v=[])):(v=v||[]).push(V,N))}a&&(v=v||[]).push("style",a);var V=v;(r.updateQueue=V)&&(r.flags|=4)}},n1=function(e,r,a,l){a!==l&&(r.flags|=4)};function pa(e,r){if(!Me)switch(e.tailMode){case"hidden":r=e.tail;for(var a=null;r!==null;)r.alternate!==null&&(a=r),r=r.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?r||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function ft(e){var r=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(r)for(var d=e.child;d!==null;)a|=d.lanes|d.childLanes,l|=d.subtreeFlags&14680064,l|=d.flags&14680064,d.return=e,d=d.sibling;else for(d=e.child;d!==null;)a|=d.lanes|d.childLanes,l|=d.subtreeFlags,l|=d.flags,d.return=e,d=d.sibling;return e.subtreeFlags|=l,e.childLanes=a,r}function m3(e,r,a){var l=r.pendingProps;switch(uu(r),r.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ft(r),null;case 1:return wt(r.type)&&Co(),ft(r),null;case 3:return l=r.stateNode,Y0(),Oe(At),Oe(ut),wu(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(ko(r)?r.flags|=4:e===null||e.memoizedState.isDehydrated&&(r.flags&256)===0||(r.flags|=1024,ar!==null&&(Xu(ar),ar=null))),Uu(e,r),ft(r),null;case 5:bu(r);var d=Jn(ca.current);if(a=r.type,e!==null&&r.stateNode!=null)r1(e,r,a,l,d),e.ref!==r.ref&&(r.flags|=512,r.flags|=2097152);else{if(!l){if(r.stateNode===null)throw Error(i(166));return ft(r),null}if(e=Jn(br.current),ko(r)){l=r.stateNode,a=r.type;var v=r.memoizedProps;switch(l[gr]=r,l[aa]=v,e=(r.mode&1)!==0,a){case"dialog":Ie("cancel",l),Ie("close",l);break;case"iframe":case"object":case"embed":Ie("load",l);break;case"video":case"audio":for(d=0;d<ra.length;d++)Ie(ra[d],l);break;case"source":Ie("error",l);break;case"img":case"image":case"link":Ie("error",l),Ie("load",l);break;case"details":Ie("toggle",l);break;case"input":vr(l,v),Ie("invalid",l);break;case"select":l._wrapperState={wasMultiple:!!v.multiple},Ie("invalid",l);break;case"textarea":rr(l,v),Ie("invalid",l)}Li(a,v),d=null;for(var C in v)if(v.hasOwnProperty(C)){var P=v[C];C==="children"?typeof P=="string"?l.textContent!==P&&(v.suppressHydrationWarning!==!0&&Ao(l.textContent,P,e),d=["children",P]):typeof P=="number"&&l.textContent!==""+P&&(v.suppressHydrationWarning!==!0&&Ao(l.textContent,P,e),d=["children",""+P]):s.hasOwnProperty(C)&&P!=null&&C==="onScroll"&&Ie("scroll",l)}switch(a){case"input":Jt(l),en(l,v,!0);break;case"textarea":Jt(l),Ii(l);break;case"select":case"option":break;default:typeof v.onClick=="function"&&(l.onclick=wo)}l=d,r.updateQueue=l,l!==null&&(r.flags|=4)}else{C=d.nodeType===9?d:d.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=kr(a)),e==="http://www.w3.org/1999/xhtml"?a==="script"?(e=C.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof l.is=="string"?e=C.createElement(a,{is:l.is}):(e=C.createElement(a),a==="select"&&(C=e,l.multiple?C.multiple=!0:l.size&&(C.size=l.size))):e=C.createElementNS(e,a),e[gr]=r,e[aa]=l,t1(e,r,!1,!1),r.stateNode=e;e:{switch(C=Ni(a,l),a){case"dialog":Ie("cancel",e),Ie("close",e),d=l;break;case"iframe":case"object":case"embed":Ie("load",e),d=l;break;case"video":case"audio":for(d=0;d<ra.length;d++)Ie(ra[d],e);d=l;break;case"source":Ie("error",e),d=l;break;case"img":case"image":case"link":Ie("error",e),Ie("load",e),d=l;break;case"details":Ie("toggle",e),d=l;break;case"input":vr(e,l),d=Ue(e,l),Ie("invalid",e);break;case"option":d=l;break;case"select":e._wrapperState={wasMultiple:!!l.multiple},d=X({},l,{value:void 0}),Ie("invalid",e);break;case"textarea":rr(e,l),d=lt(e,l),Ie("invalid",e);break;default:d=l}Li(a,d),P=d;for(v in P)if(P.hasOwnProperty(v)){var N=P[v];v==="style"?Mi(e,N):v==="dangerouslySetInnerHTML"?(N=N?N.__html:void 0,N!=null&&Oi(e,N)):v==="children"?typeof N=="string"?(a!=="textarea"||N!=="")&&tn(e,N):typeof N=="number"&&tn(e,""+N):v!=="suppressContentEditableWarning"&&v!=="suppressHydrationWarning"&&v!=="autoFocus"&&(s.hasOwnProperty(v)?N!=null&&v==="onScroll"&&Ie("scroll",e):N!=null&&E(e,v,N,C))}switch(a){case"input":Jt(e),en(e,l,!1);break;case"textarea":Jt(e),Ii(e);break;case"option":l.value!=null&&e.setAttribute("value",""+oe(l.value));break;case"select":e.multiple=!!l.multiple,v=l.value,v!=null?Je(e,!!l.multiple,v,!1):l.defaultValue!=null&&Je(e,!!l.multiple,l.defaultValue,!0);break;default:typeof d.onClick=="function"&&(e.onclick=wo)}switch(a){case"button":case"input":case"select":case"textarea":l=!!l.autoFocus;break e;case"img":l=!0;break e;default:l=!1}}l&&(r.flags|=4)}r.ref!==null&&(r.flags|=512,r.flags|=2097152)}return ft(r),null;case 6:if(e&&r.stateNode!=null)n1(e,r,e.memoizedProps,l);else{if(typeof l!="string"&&r.stateNode===null)throw Error(i(166));if(a=Jn(ca.current),Jn(br.current),ko(r)){if(l=r.stateNode,a=r.memoizedProps,l[gr]=r,(v=l.nodeValue!==a)&&(e=Pt,e!==null))switch(e.tag){case 3:Ao(l.nodeValue,a,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ao(l.nodeValue,a,(e.mode&1)!==0)}v&&(r.flags|=4)}else l=(a.nodeType===9?a:a.ownerDocument).createTextNode(l),l[gr]=r,r.stateNode=l}return ft(r),null;case 13:if(Oe(ze),l=r.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Me&&_t!==null&&(r.mode&1)!==0&&(r.flags&128)===0)od(),K0(),r.flags|=98560,v=!1;else if(v=ko(r),l!==null&&l.dehydrated!==null){if(e===null){if(!v)throw Error(i(318));if(v=r.memoizedState,v=v!==null?v.dehydrated:null,!v)throw Error(i(317));v[gr]=r}else K0(),(r.flags&128)===0&&(r.memoizedState=null),r.flags|=4;ft(r),v=!1}else ar!==null&&(Xu(ar),ar=null),v=!0;if(!v)return r.flags&65536?r:null}return(r.flags&128)!==0?(r.lanes=a,r):(l=l!==null,l!==(e!==null&&e.memoizedState!==null)&&l&&(r.child.flags|=8192,(r.mode&1)!==0&&(e===null||(ze.current&1)!==0?Ye===0&&(Ye=3):tc())),r.updateQueue!==null&&(r.flags|=4),ft(r),null);case 4:return Y0(),Uu(e,r),e===null&&na(r.stateNode.containerInfo),ft(r),null;case 10:return pu(r.type._context),ft(r),null;case 17:return wt(r.type)&&Co(),ft(r),null;case 19:if(Oe(ze),v=r.memoizedState,v===null)return ft(r),null;if(l=(r.flags&128)!==0,C=v.rendering,C===null)if(l)pa(v,!1);else{if(Ye!==0||e!==null&&(e.flags&128)!==0)for(e=r.child;e!==null;){if(C=Mo(e),C!==null){for(r.flags|=128,pa(v,!1),l=C.updateQueue,l!==null&&(r.updateQueue=l,r.flags|=4),r.subtreeFlags=0,l=a,a=r.child;a!==null;)v=a,e=l,v.flags&=14680066,C=v.alternate,C===null?(v.childLanes=0,v.lanes=e,v.child=null,v.subtreeFlags=0,v.memoizedProps=null,v.memoizedState=null,v.updateQueue=null,v.dependencies=null,v.stateNode=null):(v.childLanes=C.childLanes,v.lanes=C.lanes,v.child=C.child,v.subtreeFlags=0,v.deletions=null,v.memoizedProps=C.memoizedProps,v.memoizedState=C.memoizedState,v.updateQueue=C.updateQueue,v.type=C.type,e=C.dependencies,v.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),a=a.sibling;return _e(ze,ze.current&1|2),r.child}e=e.sibling}v.tail!==null&&We()>ti&&(r.flags|=128,l=!0,pa(v,!1),r.lanes=4194304)}else{if(!l)if(e=Mo(C),e!==null){if(r.flags|=128,l=!0,a=e.updateQueue,a!==null&&(r.updateQueue=a,r.flags|=4),pa(v,!0),v.tail===null&&v.tailMode==="hidden"&&!C.alternate&&!Me)return ft(r),null}else 2*We()-v.renderingStartTime>ti&&a!==1073741824&&(r.flags|=128,l=!0,pa(v,!1),r.lanes=4194304);v.isBackwards?(C.sibling=r.child,r.child=C):(a=v.last,a!==null?a.sibling=C:r.child=C,v.last=C)}return v.tail!==null?(r=v.tail,v.rendering=r,v.tail=r.sibling,v.renderingStartTime=We(),r.sibling=null,a=ze.current,_e(ze,l?a&1|2:a&1),r):(ft(r),null);case 22:case 23:return ec(),l=r.memoizedState!==null,e!==null&&e.memoizedState!==null!==l&&(r.flags|=8192),l&&(r.mode&1)!==0?(It&1073741824)!==0&&(ft(r),r.subtreeFlags&6&&(r.flags|=8192)):ft(r),null;case 24:return null;case 25:return null}throw Error(i(156,r.tag))}function y3(e,r){switch(uu(r),r.tag){case 1:return wt(r.type)&&Co(),e=r.flags,e&65536?(r.flags=e&-65537|128,r):null;case 3:return Y0(),Oe(At),Oe(ut),wu(),e=r.flags,(e&65536)!==0&&(e&128)===0?(r.flags=e&-65537|128,r):null;case 5:return bu(r),null;case 13:if(Oe(ze),e=r.memoizedState,e!==null&&e.dehydrated!==null){if(r.alternate===null)throw Error(i(340));K0()}return e=r.flags,e&65536?(r.flags=e&-65537|128,r):null;case 19:return Oe(ze),null;case 4:return Y0(),null;case 10:return pu(r.type._context),null;case 22:case 23:return ec(),null;case 24:return null;default:return null}}var Wo=!1,xt=!1,g3=typeof WeakSet=="function"?WeakSet:Set,ue=null;function J0(e,r){var a=e.ref;if(a!==null)if(typeof a=="function")try{a(null)}catch(l){Qe(e,r,l)}else a.current=null}function Qu(e,r,a){try{a()}catch(l){Qe(e,r,l)}}var i1=!1;function b3(e,r){if(eu=uo,e=Lx(),ql(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var d=l.anchorOffset,v=l.focusNode;l=l.focusOffset;try{a.nodeType,v.nodeType}catch{a=null;break e}var C=0,P=-1,N=-1,V=0,te=0,re=e,ee=null;t:for(;;){for(var se;re!==a||d!==0&&re.nodeType!==3||(P=C+d),re!==v||l!==0&&re.nodeType!==3||(N=C+l),re.nodeType===3&&(C+=re.nodeValue.length),(se=re.firstChild)!==null;)ee=re,re=se;for(;;){if(re===e)break t;if(ee===a&&++V===d&&(P=C),ee===v&&++te===l&&(N=C),(se=re.nextSibling)!==null)break;re=ee,ee=re.parentNode}re=se}a=P===-1||N===-1?null:{start:P,end:N}}else a=null}a=a||{start:0,end:0}}else a=null;for(tu={focusedElem:e,selectionRange:a},uo=!1,ue=r;ue!==null;)if(r=ue,e=r.child,(r.subtreeFlags&1028)!==0&&e!==null)e.return=r,ue=e;else for(;ue!==null;){r=ue;try{var xe=r.alternate;if((r.flags&1024)!==0)switch(r.tag){case 0:case 11:case 15:break;case 1:if(xe!==null){var de=xe.memoizedProps,Ve=xe.memoizedState,H=r.stateNode,z=H.getSnapshotBeforeUpdate(r.elementType===r.type?de:or(r.type,de),Ve);H.__reactInternalSnapshotBeforeUpdate=z}break;case 3:var j=r.stateNode.containerInfo;j.nodeType===1?j.textContent="":j.nodeType===9&&j.documentElement&&j.removeChild(j.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(i(163))}}catch(ie){Qe(r,r.return,ie)}if(e=r.sibling,e!==null){e.return=r.return,ue=e;break}ue=r.return}return xe=i1,i1=!1,xe}function va(e,r,a){var l=r.updateQueue;if(l=l!==null?l.lastEffect:null,l!==null){var d=l=l.next;do{if((d.tag&e)===e){var v=d.destroy;d.destroy=void 0,v!==void 0&&Qu(r,a,v)}d=d.next}while(d!==l)}}function Vo(e,r){if(r=r.updateQueue,r=r!==null?r.lastEffect:null,r!==null){var a=r=r.next;do{if((a.tag&e)===e){var l=a.create;a.destroy=l()}a=a.next}while(a!==r)}}function ju(e){var r=e.ref;if(r!==null){var a=e.stateNode;switch(e.tag){case 5:e=a;break;default:e=a}typeof r=="function"?r(e):r.current=e}}function a1(e){var r=e.alternate;r!==null&&(e.alternate=null,a1(r)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(r=e.stateNode,r!==null&&(delete r[gr],delete r[aa],delete r[au],delete r[r3],delete r[n3])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function o1(e){return e.tag===5||e.tag===3||e.tag===4}function s1(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||o1(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Wu(e,r,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,r?a.nodeType===8?a.parentNode.insertBefore(e,r):a.insertBefore(e,r):(a.nodeType===8?(r=a.parentNode,r.insertBefore(e,a)):(r=a,r.appendChild(e)),a=a._reactRootContainer,a!=null||r.onclick!==null||(r.onclick=wo));else if(l!==4&&(e=e.child,e!==null))for(Wu(e,r,a),e=e.sibling;e!==null;)Wu(e,r,a),e=e.sibling}function Vu(e,r,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,r?a.insertBefore(e,r):a.appendChild(e);else if(l!==4&&(e=e.child,e!==null))for(Vu(e,r,a),e=e.sibling;e!==null;)Vu(e,r,a),e=e.sibling}var at=null,sr=!1;function mn(e,r,a){for(a=a.child;a!==null;)l1(e,r,a),a=a.sibling}function l1(e,r,a){if(yr&&typeof yr.onCommitFiberUnmount=="function")try{yr.onCommitFiberUnmount(no,a)}catch{}switch(a.tag){case 5:xt||J0(a,r);case 6:var l=at,d=sr;at=null,mn(e,r,a),at=l,sr=d,at!==null&&(sr?(e=at,a=a.stateNode,e.nodeType===8?e.parentNode.removeChild(a):e.removeChild(a)):at.removeChild(a.stateNode));break;case 18:at!==null&&(sr?(e=at,a=a.stateNode,e.nodeType===8?iu(e.parentNode,a):e.nodeType===1&&iu(e,a),$i(e)):iu(at,a.stateNode));break;case 4:l=at,d=sr,at=a.stateNode.containerInfo,sr=!0,mn(e,r,a),at=l,sr=d;break;case 0:case 11:case 14:case 15:if(!xt&&(l=a.updateQueue,l!==null&&(l=l.lastEffect,l!==null))){d=l=l.next;do{var v=d,C=v.destroy;v=v.tag,C!==void 0&&((v&2)!==0||(v&4)!==0)&&Qu(a,r,C),d=d.next}while(d!==l)}mn(e,r,a);break;case 1:if(!xt&&(J0(a,r),l=a.stateNode,typeof l.componentWillUnmount=="function"))try{l.props=a.memoizedProps,l.state=a.memoizedState,l.componentWillUnmount()}catch(P){Qe(a,r,P)}mn(e,r,a);break;case 21:mn(e,r,a);break;case 22:a.mode&1?(xt=(l=xt)||a.memoizedState!==null,mn(e,r,a),xt=l):mn(e,r,a);break;default:mn(e,r,a)}}function u1(e){var r=e.updateQueue;if(r!==null){e.updateQueue=null;var a=e.stateNode;a===null&&(a=e.stateNode=new g3),r.forEach(function(l){var d=k3.bind(null,e,l);a.has(l)||(a.add(l),l.then(d,d))})}}function lr(e,r){var a=r.deletions;if(a!==null)for(var l=0;l<a.length;l++){var d=a[l];try{var v=e,C=r,P=C;e:for(;P!==null;){switch(P.tag){case 5:at=P.stateNode,sr=!1;break e;case 3:at=P.stateNode.containerInfo,sr=!0;break e;case 4:at=P.stateNode.containerInfo,sr=!0;break e}P=P.return}if(at===null)throw Error(i(160));l1(v,C,d),at=null,sr=!1;var N=d.alternate;N!==null&&(N.return=null),d.return=null}catch(V){Qe(d,r,V)}}if(r.subtreeFlags&12854)for(r=r.child;r!==null;)c1(r,e),r=r.sibling}function c1(e,r){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(lr(r,e),wr(e),l&4){try{va(3,e,e.return),Vo(3,e)}catch(de){Qe(e,e.return,de)}try{va(5,e,e.return)}catch(de){Qe(e,e.return,de)}}break;case 1:lr(r,e),wr(e),l&512&&a!==null&&J0(a,a.return);break;case 5:if(lr(r,e),wr(e),l&512&&a!==null&&J0(a,a.return),e.flags&32){var d=e.stateNode;try{tn(d,"")}catch(de){Qe(e,e.return,de)}}if(l&4&&(d=e.stateNode,d!=null)){var v=e.memoizedProps,C=a!==null?a.memoizedProps:v,P=e.type,N=e.updateQueue;if(e.updateQueue=null,N!==null)try{P==="input"&&v.type==="radio"&&v.name!=null&&er(d,v),Ni(P,C);var V=Ni(P,v);for(C=0;C<N.length;C+=2){var te=N[C],re=N[C+1];te==="style"?Mi(d,re):te==="dangerouslySetInnerHTML"?Oi(d,re):te==="children"?tn(d,re):E(d,te,re,V)}switch(P){case"input":Jr(d,v);break;case"textarea":Wn(d,v);break;case"select":var ee=d._wrapperState.wasMultiple;d._wrapperState.wasMultiple=!!v.multiple;var se=v.value;se!=null?Je(d,!!v.multiple,se,!1):ee!==!!v.multiple&&(v.defaultValue!=null?Je(d,!!v.multiple,v.defaultValue,!0):Je(d,!!v.multiple,v.multiple?[]:"",!1))}d[aa]=v}catch(de){Qe(e,e.return,de)}}break;case 6:if(lr(r,e),wr(e),l&4){if(e.stateNode===null)throw Error(i(162));d=e.stateNode,v=e.memoizedProps;try{d.nodeValue=v}catch(de){Qe(e,e.return,de)}}break;case 3:if(lr(r,e),wr(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{$i(r.containerInfo)}catch(de){Qe(e,e.return,de)}break;case 4:lr(r,e),wr(e);break;case 13:lr(r,e),wr(e),d=e.child,d.flags&8192&&(v=d.memoizedState!==null,d.stateNode.isHidden=v,!v||d.alternate!==null&&d.alternate.memoizedState!==null||($u=We())),l&4&&u1(e);break;case 22:if(te=a!==null&&a.memoizedState!==null,e.mode&1?(xt=(V=xt)||te,lr(r,e),xt=V):lr(r,e),wr(e),l&8192){if(V=e.memoizedState!==null,(e.stateNode.isHidden=V)&&!te&&(e.mode&1)!==0)for(ue=e,te=e.child;te!==null;){for(re=ue=te;ue!==null;){switch(ee=ue,se=ee.child,ee.tag){case 0:case 11:case 14:case 15:va(4,ee,ee.return);break;case 1:J0(ee,ee.return);var xe=ee.stateNode;if(typeof xe.componentWillUnmount=="function"){l=ee,a=ee.return;try{r=l,xe.props=r.memoizedProps,xe.state=r.memoizedState,xe.componentWillUnmount()}catch(de){Qe(l,a,de)}}break;case 5:J0(ee,ee.return);break;case 22:if(ee.memoizedState!==null){d1(re);continue}}se!==null?(se.return=ee,ue=se):d1(re)}te=te.sibling}e:for(te=null,re=e;;){if(re.tag===5){if(te===null){te=re;try{d=re.stateNode,V?(v=d.style,typeof v.setProperty=="function"?v.setProperty("display","none","important"):v.display="none"):(P=re.stateNode,N=re.memoizedProps.style,C=N!=null&&N.hasOwnProperty("display")?N.display:null,P.style.display=Ga("display",C))}catch(de){Qe(e,e.return,de)}}}else if(re.tag===6){if(te===null)try{re.stateNode.nodeValue=V?"":re.memoizedProps}catch(de){Qe(e,e.return,de)}}else if((re.tag!==22&&re.tag!==23||re.memoizedState===null||re===e)&&re.child!==null){re.child.return=re,re=re.child;continue}if(re===e)break e;for(;re.sibling===null;){if(re.return===null||re.return===e)break e;te===re&&(te=null),re=re.return}te===re&&(te=null),re.sibling.return=re.return,re=re.sibling}}break;case 19:lr(r,e),wr(e),l&4&&u1(e);break;case 21:break;default:lr(r,e),wr(e)}}function wr(e){var r=e.flags;if(r&2){try{e:{for(var a=e.return;a!==null;){if(o1(a)){var l=a;break e}a=a.return}throw Error(i(160))}switch(l.tag){case 5:var d=l.stateNode;l.flags&32&&(tn(d,""),l.flags&=-33);var v=s1(e);Vu(e,v,d);break;case 3:case 4:var C=l.stateNode.containerInfo,P=s1(e);Wu(e,P,C);break;default:throw Error(i(161))}}catch(N){Qe(e,e.return,N)}e.flags&=-3}r&4096&&(e.flags&=-4097)}function A3(e,r,a){ue=e,f1(e)}function f1(e,r,a){for(var l=(e.mode&1)!==0;ue!==null;){var d=ue,v=d.child;if(d.tag===22&&l){var C=d.memoizedState!==null||Wo;if(!C){var P=d.alternate,N=P!==null&&P.memoizedState!==null||xt;P=Wo;var V=xt;if(Wo=C,(xt=N)&&!V)for(ue=d;ue!==null;)C=ue,N=C.child,C.tag===22&&C.memoizedState!==null?h1(d):N!==null?(N.return=C,ue=N):h1(d);for(;v!==null;)ue=v,f1(v),v=v.sibling;ue=d,Wo=P,xt=V}x1(e)}else(d.subtreeFlags&8772)!==0&&v!==null?(v.return=d,ue=v):x1(e)}}function x1(e){for(;ue!==null;){var r=ue;if((r.flags&8772)!==0){var a=r.alternate;try{if((r.flags&8772)!==0)switch(r.tag){case 0:case 11:case 15:xt||Vo(5,r);break;case 1:var l=r.stateNode;if(r.flags&4&&!xt)if(a===null)l.componentDidMount();else{var d=r.elementType===r.type?a.memoizedProps:or(r.type,a.memoizedProps);l.componentDidUpdate(d,a.memoizedState,l.__reactInternalSnapshotBeforeUpdate)}var v=r.updateQueue;v!==null&&dd(r,v,l);break;case 3:var C=r.updateQueue;if(C!==null){if(a=null,r.child!==null)switch(r.child.tag){case 5:a=r.child.stateNode;break;case 1:a=r.child.stateNode}dd(r,C,a)}break;case 5:var P=r.stateNode;if(a===null&&r.flags&4){a=P;var N=r.memoizedProps;switch(r.type){case"button":case"input":case"select":case"textarea":N.autoFocus&&a.focus();break;case"img":N.src&&(a.src=N.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(r.memoizedState===null){var V=r.alternate;if(V!==null){var te=V.memoizedState;if(te!==null){var re=te.dehydrated;re!==null&&$i(re)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(i(163))}xt||r.flags&512&&ju(r)}catch(ee){Qe(r,r.return,ee)}}if(r===e){ue=null;break}if(a=r.sibling,a!==null){a.return=r.return,ue=a;break}ue=r.return}}function d1(e){for(;ue!==null;){var r=ue;if(r===e){ue=null;break}var a=r.sibling;if(a!==null){a.return=r.return,ue=a;break}ue=r.return}}function h1(e){for(;ue!==null;){var r=ue;try{switch(r.tag){case 0:case 11:case 15:var a=r.return;try{Vo(4,r)}catch(N){Qe(r,a,N)}break;case 1:var l=r.stateNode;if(typeof l.componentDidMount=="function"){var d=r.return;try{l.componentDidMount()}catch(N){Qe(r,d,N)}}var v=r.return;try{ju(r)}catch(N){Qe(r,v,N)}break;case 5:var C=r.return;try{ju(r)}catch(N){Qe(r,C,N)}}}catch(N){Qe(r,r.return,N)}if(r===e){ue=null;break}var P=r.sibling;if(P!==null){P.return=r.return,ue=P;break}ue=r.return}}var w3=Math.ceil,qo=D.ReactCurrentDispatcher,qu=D.ReactCurrentOwner,Ut=D.ReactCurrentBatchConfig,Se=0,tt=null,Ke=null,ot=0,It=0,ei=xn(0),Ye=0,ma=null,t0=0,Ko=0,Ku=0,ya=null,Ct=null,$u=0,ti=1/0,zr=null,$o=!1,Gu=null,yn=null,Go=!1,gn=null,Zo=0,ga=0,Zu=null,Yo=-1,Xo=0;function vt(){return(Se&6)!==0?We():Yo!==-1?Yo:Yo=We()}function bn(e){return(e.mode&1)===0?1:(Se&2)!==0&&ot!==0?ot&-ot:a3.transition!==null?(Xo===0&&(Xo=sx()),Xo):(e=Re,e!==0||(e=window.event,e=e===void 0?16:vx(e.type)),e)}function ur(e,r,a,l){if(50<ga)throw ga=0,Zu=null,Error(i(185));ji(e,a,l),((Se&2)===0||e!==tt)&&(e===tt&&((Se&2)===0&&(Ko|=a),Ye===4&&An(e,ot)),Bt(e,l),a===1&&Se===0&&(r.mode&1)===0&&(ti=We()+500,So&&hn()))}function Bt(e,r){var a=e.callbackNode;a8(e,r);var l=oo(e,e===tt?ot:0);if(l===0)a!==null&&ix(a),e.callbackNode=null,e.callbackPriority=0;else if(r=l&-l,e.callbackPriority!==r){if(a!=null&&ix(a),r===1)e.tag===0?i3(v1.bind(null,e)):td(v1.bind(null,e)),e3(function(){(Se&6)===0&&hn()}),a=null;else{switch(lx(l)){case 1:a=kl;break;case 4:a=ax;break;case 16:a=ro;break;case 536870912:a=ox;break;default:a=ro}a=C1(a,p1.bind(null,e))}e.callbackPriority=r,e.callbackNode=a}}function p1(e,r){if(Yo=-1,Xo=0,(Se&6)!==0)throw Error(i(327));var a=e.callbackNode;if(ri()&&e.callbackNode!==a)return null;var l=oo(e,e===tt?ot:0);if(l===0)return null;if((l&30)!==0||(l&e.expiredLanes)!==0||r)r=Jo(e,l);else{r=l;var d=Se;Se|=2;var v=y1();(tt!==e||ot!==r)&&(zr=null,ti=We()+500,n0(e,r));do try{B3();break}catch(P){m1(e,P)}while(!0);hu(),qo.current=v,Se=d,Ke!==null?r=0:(tt=null,ot=0,r=Ye)}if(r!==0){if(r===2&&(d=Rl(e),d!==0&&(l=d,r=Yu(e,d))),r===1)throw a=ma,n0(e,0),An(e,l),Bt(e,We()),a;if(r===6)An(e,l);else{if(d=e.current.alternate,(l&30)===0&&!E3(d)&&(r=Jo(e,l),r===2&&(v=Rl(e),v!==0&&(l=v,r=Yu(e,v))),r===1))throw a=ma,n0(e,0),An(e,l),Bt(e,We()),a;switch(e.finishedWork=d,e.finishedLanes=l,r){case 0:case 1:throw Error(i(345));case 2:i0(e,Ct,zr);break;case 3:if(An(e,l),(l&130023424)===l&&(r=$u+500-We(),10<r)){if(oo(e,0)!==0)break;if(d=e.suspendedLanes,(d&l)!==l){vt(),e.pingedLanes|=e.suspendedLanes&d;break}e.timeoutHandle=nu(i0.bind(null,e,Ct,zr),r);break}i0(e,Ct,zr);break;case 4:if(An(e,l),(l&4194240)===l)break;for(r=e.eventTimes,d=-1;0<l;){var C=31-nr(l);v=1<<C,C=r[C],C>d&&(d=C),l&=~v}if(l=d,l=We()-l,l=(120>l?120:480>l?480:1080>l?1080:1920>l?1920:3e3>l?3e3:4320>l?4320:1960*w3(l/1960))-l,10<l){e.timeoutHandle=nu(i0.bind(null,e,Ct,zr),l);break}i0(e,Ct,zr);break;case 5:i0(e,Ct,zr);break;default:throw Error(i(329))}}}return Bt(e,We()),e.callbackNode===a?p1.bind(null,e):null}function Yu(e,r){var a=ya;return e.current.memoizedState.isDehydrated&&(n0(e,r).flags|=256),e=Jo(e,r),e!==2&&(r=Ct,Ct=a,r!==null&&Xu(r)),e}function Xu(e){Ct===null?Ct=e:Ct.push.apply(Ct,e)}function E3(e){for(var r=e;;){if(r.flags&16384){var a=r.updateQueue;if(a!==null&&(a=a.stores,a!==null))for(var l=0;l<a.length;l++){var d=a[l],v=d.getSnapshot;d=d.value;try{if(!ir(v(),d))return!1}catch{return!1}}}if(a=r.child,r.subtreeFlags&16384&&a!==null)a.return=r,r=a;else{if(r===e)break;for(;r.sibling===null;){if(r.return===null||r.return===e)return!0;r=r.return}r.sibling.return=r.return,r=r.sibling}}return!0}function An(e,r){for(r&=~Ku,r&=~Ko,e.suspendedLanes|=r,e.pingedLanes&=~r,e=e.expirationTimes;0<r;){var a=31-nr(r),l=1<<a;e[a]=-1,r&=~l}}function v1(e){if((Se&6)!==0)throw Error(i(327));ri();var r=oo(e,0);if((r&1)===0)return Bt(e,We()),null;var a=Jo(e,r);if(e.tag!==0&&a===2){var l=Rl(e);l!==0&&(r=l,a=Yu(e,l))}if(a===1)throw a=ma,n0(e,0),An(e,r),Bt(e,We()),a;if(a===6)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=r,i0(e,Ct,zr),Bt(e,We()),null}function Ju(e,r){var a=Se;Se|=1;try{return e(r)}finally{Se=a,Se===0&&(ti=We()+500,So&&hn())}}function r0(e){gn!==null&&gn.tag===0&&(Se&6)===0&&ri();var r=Se;Se|=1;var a=Ut.transition,l=Re;try{if(Ut.transition=null,Re=1,e)return e()}finally{Re=l,Ut.transition=a,Se=r,(Se&6)===0&&hn()}}function ec(){It=ei.current,Oe(ei)}function n0(e,r){e.finishedWork=null,e.finishedLanes=0;var a=e.timeoutHandle;if(a!==-1&&(e.timeoutHandle=-1,J8(a)),Ke!==null)for(a=Ke.return;a!==null;){var l=a;switch(uu(l),l.tag){case 1:l=l.type.childContextTypes,l!=null&&Co();break;case 3:Y0(),Oe(At),Oe(ut),wu();break;case 5:bu(l);break;case 4:Y0();break;case 13:Oe(ze);break;case 19:Oe(ze);break;case 10:pu(l.type._context);break;case 22:case 23:ec()}a=a.return}if(tt=e,Ke=e=wn(e.current,null),ot=It=r,Ye=0,ma=null,Ku=Ko=t0=0,Ct=ya=null,Xn!==null){for(r=0;r<Xn.length;r++)if(a=Xn[r],l=a.interleaved,l!==null){a.interleaved=null;var d=l.next,v=a.pending;if(v!==null){var C=v.next;v.next=d,l.next=C}a.pending=l}Xn=null}return e}function m1(e,r){do{var a=Ke;try{if(hu(),Lo.current=Ho,No){for(var l=He.memoizedState;l!==null;){var d=l.queue;d!==null&&(d.pending=null),l=l.next}No=!1}if(e0=0,et=Ze=He=null,fa=!1,xa=0,qu.current=null,a===null||a.return===null){Ye=1,ma=r,Ke=null;break}e:{var v=e,C=a.return,P=a,N=r;if(r=ot,P.flags|=32768,N!==null&&typeof N=="object"&&typeof N.then=="function"){var V=N,te=P,re=te.tag;if((te.mode&1)===0&&(re===0||re===11||re===15)){var ee=te.alternate;ee?(te.updateQueue=ee.updateQueue,te.memoizedState=ee.memoizedState,te.lanes=ee.lanes):(te.updateQueue=null,te.memoizedState=null)}var se=Qd(C);if(se!==null){se.flags&=-257,jd(se,C,P,v,r),se.mode&1&&Ud(v,V,r),r=se,N=V;var xe=r.updateQueue;if(xe===null){var de=new Set;de.add(N),r.updateQueue=de}else xe.add(N);break e}else{if((r&1)===0){Ud(v,V,r),tc();break e}N=Error(i(426))}}else if(Me&&P.mode&1){var Ve=Qd(C);if(Ve!==null){(Ve.flags&65536)===0&&(Ve.flags|=256),jd(Ve,C,P,v,r),xu(X0(N,P));break e}}v=N=X0(N,P),Ye!==4&&(Ye=2),ya===null?ya=[v]:ya.push(v),v=C;do{switch(v.tag){case 3:v.flags|=65536,r&=-r,v.lanes|=r;var H=zd(v,N,r);xd(v,H);break e;case 1:P=N;var z=v.type,j=v.stateNode;if((v.flags&128)===0&&(typeof z.getDerivedStateFromError=="function"||j!==null&&typeof j.componentDidCatch=="function"&&(yn===null||!yn.has(j)))){v.flags|=65536,r&=-r,v.lanes|=r;var ie=Hd(v,P,r);xd(v,ie);break e}}v=v.return}while(v!==null)}b1(a)}catch(he){r=he,Ke===a&&a!==null&&(Ke=a=a.return);continue}break}while(!0)}function y1(){var e=qo.current;return qo.current=Ho,e===null?Ho:e}function tc(){(Ye===0||Ye===3||Ye===2)&&(Ye=4),tt===null||(t0&268435455)===0&&(Ko&268435455)===0||An(tt,ot)}function Jo(e,r){var a=Se;Se|=2;var l=y1();(tt!==e||ot!==r)&&(zr=null,n0(e,r));do try{C3();break}catch(d){m1(e,d)}while(!0);if(hu(),Se=a,qo.current=l,Ke!==null)throw Error(i(261));return tt=null,ot=0,Ye}function C3(){for(;Ke!==null;)g1(Ke)}function B3(){for(;Ke!==null&&!Z4();)g1(Ke)}function g1(e){var r=E1(e.alternate,e,It);e.memoizedProps=e.pendingProps,r===null?b1(e):Ke=r,qu.current=null}function b1(e){var r=e;do{var a=r.alternate;if(e=r.return,(r.flags&32768)===0){if(a=m3(a,r,It),a!==null){Ke=a;return}}else{if(a=y3(a,r),a!==null){a.flags&=32767,Ke=a;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ye=6,Ke=null;return}}if(r=r.sibling,r!==null){Ke=r;return}Ke=r=e}while(r!==null);Ye===0&&(Ye=5)}function i0(e,r,a){var l=Re,d=Ut.transition;try{Ut.transition=null,Re=1,S3(e,r,a,l)}finally{Ut.transition=d,Re=l}return null}function S3(e,r,a,l){do ri();while(gn!==null);if((Se&6)!==0)throw Error(i(327));a=e.finishedWork;var d=e.finishedLanes;if(a===null)return null;if(e.finishedWork=null,e.finishedLanes=0,a===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var v=a.lanes|a.childLanes;if(o8(e,v),e===tt&&(Ke=tt=null,ot=0),(a.subtreeFlags&2064)===0&&(a.flags&2064)===0||Go||(Go=!0,C1(ro,function(){return ri(),null})),v=(a.flags&15990)!==0,(a.subtreeFlags&15990)!==0||v){v=Ut.transition,Ut.transition=null;var C=Re;Re=1;var P=Se;Se|=4,qu.current=null,b3(e,a),c1(a,e),q8(tu),uo=!!eu,tu=eu=null,e.current=a,A3(a),Y4(),Se=P,Re=C,Ut.transition=v}else e.current=a;if(Go&&(Go=!1,gn=e,Zo=d),v=e.pendingLanes,v===0&&(yn=null),e8(a.stateNode),Bt(e,We()),r!==null)for(l=e.onRecoverableError,a=0;a<r.length;a++)d=r[a],l(d.value,{componentStack:d.stack,digest:d.digest});if($o)throw $o=!1,e=Gu,Gu=null,e;return(Zo&1)!==0&&e.tag!==0&&ri(),v=e.pendingLanes,(v&1)!==0?e===Zu?ga++:(ga=0,Zu=e):ga=0,hn(),null}function ri(){if(gn!==null){var e=lx(Zo),r=Ut.transition,a=Re;try{if(Ut.transition=null,Re=16>e?16:e,gn===null)var l=!1;else{if(e=gn,gn=null,Zo=0,(Se&6)!==0)throw Error(i(331));var d=Se;for(Se|=4,ue=e.current;ue!==null;){var v=ue,C=v.child;if((ue.flags&16)!==0){var P=v.deletions;if(P!==null){for(var N=0;N<P.length;N++){var V=P[N];for(ue=V;ue!==null;){var te=ue;switch(te.tag){case 0:case 11:case 15:va(8,te,v)}var re=te.child;if(re!==null)re.return=te,ue=re;else for(;ue!==null;){te=ue;var ee=te.sibling,se=te.return;if(a1(te),te===V){ue=null;break}if(ee!==null){ee.return=se,ue=ee;break}ue=se}}}var xe=v.alternate;if(xe!==null){var de=xe.child;if(de!==null){xe.child=null;do{var Ve=de.sibling;de.sibling=null,de=Ve}while(de!==null)}}ue=v}}if((v.subtreeFlags&2064)!==0&&C!==null)C.return=v,ue=C;else e:for(;ue!==null;){if(v=ue,(v.flags&2048)!==0)switch(v.tag){case 0:case 11:case 15:va(9,v,v.return)}var H=v.sibling;if(H!==null){H.return=v.return,ue=H;break e}ue=v.return}}var z=e.current;for(ue=z;ue!==null;){C=ue;var j=C.child;if((C.subtreeFlags&2064)!==0&&j!==null)j.return=C,ue=j;else e:for(C=z;ue!==null;){if(P=ue,(P.flags&2048)!==0)try{switch(P.tag){case 0:case 11:case 15:Vo(9,P)}}catch(he){Qe(P,P.return,he)}if(P===C){ue=null;break e}var ie=P.sibling;if(ie!==null){ie.return=P.return,ue=ie;break e}ue=P.return}}if(Se=d,hn(),yr&&typeof yr.onPostCommitFiberRoot=="function")try{yr.onPostCommitFiberRoot(no,e)}catch{}l=!0}return l}finally{Re=a,Ut.transition=r}}return!1}function A1(e,r,a){r=X0(a,r),r=zd(e,r,1),e=vn(e,r,1),r=vt(),e!==null&&(ji(e,1,r),Bt(e,r))}function Qe(e,r,a){if(e.tag===3)A1(e,e,a);else for(;r!==null;){if(r.tag===3){A1(r,e,a);break}else if(r.tag===1){var l=r.stateNode;if(typeof r.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(yn===null||!yn.has(l))){e=X0(a,e),e=Hd(r,e,1),r=vn(r,e,1),e=vt(),r!==null&&(ji(r,1,e),Bt(r,e));break}}r=r.return}}function D3(e,r,a){var l=e.pingCache;l!==null&&l.delete(r),r=vt(),e.pingedLanes|=e.suspendedLanes&a,tt===e&&(ot&a)===a&&(Ye===4||Ye===3&&(ot&130023424)===ot&&500>We()-$u?n0(e,0):Ku|=a),Bt(e,r)}function w1(e,r){r===0&&((e.mode&1)===0?r=1:(r=ao,ao<<=1,(ao&130023424)===0&&(ao=4194304)));var a=vt();e=Lr(e,r),e!==null&&(ji(e,r,a),Bt(e,a))}function F3(e){var r=e.memoizedState,a=0;r!==null&&(a=r.retryLane),w1(e,a)}function k3(e,r){var a=0;switch(e.tag){case 13:var l=e.stateNode,d=e.memoizedState;d!==null&&(a=d.retryLane);break;case 19:l=e.stateNode;break;default:throw Error(i(314))}l!==null&&l.delete(r),w1(e,a)}var E1;E1=function(e,r,a){if(e!==null)if(e.memoizedProps!==r.pendingProps||At.current)Et=!0;else{if((e.lanes&a)===0&&(r.flags&128)===0)return Et=!1,v3(e,r,a);Et=(e.flags&131072)!==0}else Et=!1,Me&&(r.flags&1048576)!==0&&rd(r,Fo,r.index);switch(r.lanes=0,r.tag){case 2:var l=r.type;jo(e,r),e=r.pendingProps;var d=W0(r,ut.current);Z0(r,a),d=Bu(null,r,l,e,d,a);var v=Su();return r.flags|=1,typeof d=="object"&&d!==null&&typeof d.render=="function"&&d.$$typeof===void 0?(r.tag=1,r.memoizedState=null,r.updateQueue=null,wt(l)?(v=!0,Bo(r)):v=!1,r.memoizedState=d.state!==null&&d.state!==void 0?d.state:null,yu(r),d.updater=Uo,r.stateNode=d,d._reactInternals=r,_u(r,l,e,a),r=Lu(null,r,l,!0,v,a)):(r.tag=0,Me&&v&&lu(r),pt(null,r,d,a),r=r.child),r;case 16:l=r.elementType;e:{switch(jo(e,r),e=r.pendingProps,d=l._init,l=d(l._payload),r.type=l,d=r.tag=P3(l),e=or(l,e),d){case 0:r=Mu(null,r,l,e,a);break e;case 1:r=Gd(null,r,l,e,a);break e;case 11:r=Wd(null,r,l,e,a);break e;case 14:r=Vd(null,r,l,or(l.type,e),a);break e}throw Error(i(306,l,""))}return r;case 0:return l=r.type,d=r.pendingProps,d=r.elementType===l?d:or(l,d),Mu(e,r,l,d,a);case 1:return l=r.type,d=r.pendingProps,d=r.elementType===l?d:or(l,d),Gd(e,r,l,d,a);case 3:e:{if(Zd(r),e===null)throw Error(i(387));l=r.pendingProps,v=r.memoizedState,d=v.element,fd(e,r),Oo(r,l,null,a);var C=r.memoizedState;if(l=C.element,v.isDehydrated)if(v={element:l,isDehydrated:!1,cache:C.cache,pendingSuspenseBoundaries:C.pendingSuspenseBoundaries,transitions:C.transitions},r.updateQueue.baseState=v,r.memoizedState=v,r.flags&256){d=X0(Error(i(423)),r),r=Yd(e,r,l,a,d);break e}else if(l!==d){d=X0(Error(i(424)),r),r=Yd(e,r,l,a,d);break e}else for(_t=fn(r.stateNode.containerInfo.firstChild),Pt=r,Me=!0,ar=null,a=ud(r,null,l,a),r.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling;else{if(K0(),l===d){r=Tr(e,r,a);break e}pt(e,r,l,a)}r=r.child}return r;case 5:return hd(r),e===null&&fu(r),l=r.type,d=r.pendingProps,v=e!==null?e.memoizedProps:null,C=d.children,ru(l,d)?C=null:v!==null&&ru(l,v)&&(r.flags|=32),$d(e,r),pt(e,r,C,a),r.child;case 6:return e===null&&fu(r),null;case 13:return Xd(e,r,a);case 4:return gu(r,r.stateNode.containerInfo),l=r.pendingProps,e===null?r.child=$0(r,null,l,a):pt(e,r,l,a),r.child;case 11:return l=r.type,d=r.pendingProps,d=r.elementType===l?d:or(l,d),Wd(e,r,l,d,a);case 7:return pt(e,r,r.pendingProps,a),r.child;case 8:return pt(e,r,r.pendingProps.children,a),r.child;case 12:return pt(e,r,r.pendingProps.children,a),r.child;case 10:e:{if(l=r.type._context,d=r.pendingProps,v=r.memoizedProps,C=d.value,_e(Po,l._currentValue),l._currentValue=C,v!==null)if(ir(v.value,C)){if(v.children===d.children&&!At.current){r=Tr(e,r,a);break e}}else for(v=r.child,v!==null&&(v.return=r);v!==null;){var P=v.dependencies;if(P!==null){C=v.child;for(var N=P.firstContext;N!==null;){if(N.context===l){if(v.tag===1){N=Nr(-1,a&-a),N.tag=2;var V=v.updateQueue;if(V!==null){V=V.shared;var te=V.pending;te===null?N.next=N:(N.next=te.next,te.next=N),V.pending=N}}v.lanes|=a,N=v.alternate,N!==null&&(N.lanes|=a),vu(v.return,a,r),P.lanes|=a;break}N=N.next}}else if(v.tag===10)C=v.type===r.type?null:v.child;else if(v.tag===18){if(C=v.return,C===null)throw Error(i(341));C.lanes|=a,P=C.alternate,P!==null&&(P.lanes|=a),vu(C,a,r),C=v.sibling}else C=v.child;if(C!==null)C.return=v;else for(C=v;C!==null;){if(C===r){C=null;break}if(v=C.sibling,v!==null){v.return=C.return,C=v;break}C=C.return}v=C}pt(e,r,d.children,a),r=r.child}return r;case 9:return d=r.type,l=r.pendingProps.children,Z0(r,a),d=zt(d),l=l(d),r.flags|=1,pt(e,r,l,a),r.child;case 14:return l=r.type,d=or(l,r.pendingProps),d=or(l.type,d),Vd(e,r,l,d,a);case 15:return qd(e,r,r.type,r.pendingProps,a);case 17:return l=r.type,d=r.pendingProps,d=r.elementType===l?d:or(l,d),jo(e,r),r.tag=1,wt(l)?(e=!0,Bo(r)):e=!1,Z0(r,a),Nd(r,l,d),_u(r,l,d,a),Lu(null,r,l,!0,e,a);case 19:return e1(e,r,a);case 22:return Kd(e,r,a)}throw Error(i(156,r.tag))};function C1(e,r){return nx(e,r)}function R3(e,r,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=r,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Qt(e,r,a,l){return new R3(e,r,a,l)}function rc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function P3(e){if(typeof e=="function")return rc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===q)return 11;if(e===K)return 14}return 2}function wn(e,r){var a=e.alternate;return a===null?(a=Qt(e.tag,r,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=r,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&14680064,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,r=e.dependencies,a.dependencies=r===null?null:{lanes:r.lanes,firstContext:r.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a}function es(e,r,a,l,d,v){var C=2;if(l=e,typeof e=="function")rc(e)&&(C=1);else if(typeof e=="string")C=5;else e:switch(e){case I:return a0(a.children,d,v,r);case O:C=8,d|=8;break;case Q:return e=Qt(12,a,r,d|2),e.elementType=Q,e.lanes=v,e;case $:return e=Qt(13,a,r,d),e.elementType=$,e.lanes=v,e;case ae:return e=Qt(19,a,r,d),e.elementType=ae,e.lanes=v,e;case ne:return ts(a,d,v,r);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case _:C=10;break e;case T:C=9;break e;case q:C=11;break e;case K:C=14;break e;case Y:C=16,l=null;break e}throw Error(i(130,e==null?e:typeof e,""))}return r=Qt(C,a,r,d),r.elementType=e,r.type=l,r.lanes=v,r}function a0(e,r,a,l){return e=Qt(7,e,l,r),e.lanes=a,e}function ts(e,r,a,l){return e=Qt(22,e,l,r),e.elementType=ne,e.lanes=a,e.stateNode={isHidden:!1},e}function nc(e,r,a){return e=Qt(6,e,null,r),e.lanes=a,e}function ic(e,r,a){return r=Qt(4,e.children!==null?e.children:[],e.key,r),r.lanes=a,r.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},r}function _3(e,r,a,l,d){this.tag=r,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Pl(0),this.expirationTimes=Pl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Pl(0),this.identifierPrefix=l,this.onRecoverableError=d,this.mutableSourceEagerHydrationData=null}function ac(e,r,a,l,d,v,C,P,N){return e=new _3(e,r,a,P,N),r===1?(r=1,v===!0&&(r|=8)):r=0,v=Qt(3,null,null,r),e.current=v,v.stateNode=e,v.memoizedState={element:l,isDehydrated:a,cache:null,transitions:null,pendingSuspenseBoundaries:null},yu(v),e}function I3(e,r,a){var l=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:R,key:l==null?null:""+l,children:e,containerInfo:r,implementation:a}}function B1(e){if(!e)return dn;e=e._reactInternals;e:{if(Kn(e)!==e||e.tag!==1)throw Error(i(170));var r=e;do{switch(r.tag){case 3:r=r.stateNode.context;break e;case 1:if(wt(r.type)){r=r.stateNode.__reactInternalMemoizedMergedChildContext;break e}}r=r.return}while(r!==null);throw Error(i(171))}if(e.tag===1){var a=e.type;if(wt(a))return Jx(e,a,r)}return r}function S1(e,r,a,l,d,v,C,P,N){return e=ac(a,l,!0,e,d,v,C,P,N),e.context=B1(null),a=e.current,l=vt(),d=bn(a),v=Nr(l,d),v.callback=r??null,vn(a,v,d),e.current.lanes=d,ji(e,d,l),Bt(e,l),e}function rs(e,r,a,l){var d=r.current,v=vt(),C=bn(d);return a=B1(a),r.context===null?r.context=a:r.pendingContext=a,r=Nr(v,C),r.payload={element:e},l=l===void 0?null:l,l!==null&&(r.callback=l),e=vn(d,r,C),e!==null&&(ur(e,d,C,v),Io(e,d,C)),C}function ns(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function D1(e,r){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<r?a:r}}function oc(e,r){D1(e,r),(e=e.alternate)&&D1(e,r)}function O3(){return null}var F1=typeof reportError=="function"?reportError:function(e){console.error(e)};function sc(e){this._internalRoot=e}is.prototype.render=sc.prototype.render=function(e){var r=this._internalRoot;if(r===null)throw Error(i(409));rs(e,r,null,null)},is.prototype.unmount=sc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var r=e.containerInfo;r0(function(){rs(null,e,null,null)}),r[_r]=null}};function is(e){this._internalRoot=e}is.prototype.unstable_scheduleHydration=function(e){if(e){var r=fx();e={blockedOn:null,target:e,priority:r};for(var a=0;a<ln.length&&r!==0&&r<ln[a].priority;a++);ln.splice(a,0,e),a===0&&hx(e)}};function lc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function as(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function k1(){}function M3(e,r,a,l,d){if(d){if(typeof l=="function"){var v=l;l=function(){var V=ns(C);v.call(V)}}var C=S1(r,l,e,0,null,!1,!1,"",k1);return e._reactRootContainer=C,e[_r]=C.current,na(e.nodeType===8?e.parentNode:e),r0(),C}for(;d=e.lastChild;)e.removeChild(d);if(typeof l=="function"){var P=l;l=function(){var V=ns(N);P.call(V)}}var N=ac(e,0,!1,null,null,!1,!1,"",k1);return e._reactRootContainer=N,e[_r]=N.current,na(e.nodeType===8?e.parentNode:e),r0(function(){rs(r,N,a,l)}),N}function os(e,r,a,l,d){var v=a._reactRootContainer;if(v){var C=v;if(typeof d=="function"){var P=d;d=function(){var N=ns(C);P.call(N)}}rs(r,C,e,d)}else C=M3(a,r,e,d,l);return ns(C)}ux=function(e){switch(e.tag){case 3:var r=e.stateNode;if(r.current.memoizedState.isDehydrated){var a=Qi(r.pendingLanes);a!==0&&(_l(r,a|1),Bt(r,We()),(Se&6)===0&&(ti=We()+500,hn()))}break;case 13:r0(function(){var l=Lr(e,1);if(l!==null){var d=vt();ur(l,e,1,d)}}),oc(e,1)}},Il=function(e){if(e.tag===13){var r=Lr(e,134217728);if(r!==null){var a=vt();ur(r,e,134217728,a)}oc(e,134217728)}},cx=function(e){if(e.tag===13){var r=bn(e),a=Lr(e,r);if(a!==null){var l=vt();ur(a,e,r,l)}oc(e,r)}},fx=function(){return Re},xx=function(e,r){var a=Re;try{return Re=e,r()}finally{Re=a}},_0=function(e,r,a){switch(r){case"input":if(Jr(e,a),r=a.name,a.type==="radio"&&r!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll("input[name="+JSON.stringify(""+r)+'][type="radio"]'),r=0;r<a.length;r++){var l=a[r];if(l!==e&&l.form===e.form){var d=Eo(l);if(!d)throw Error(i(90));$e(l),Jr(l,d)}}}break;case"textarea":Wn(e,a);break;case"select":r=a.value,r!=null&&Je(e,!!a.multiple,r,!1)}},zi=Ju,eo=r0;var L3={usingClientEntryPoint:!1,Events:[oa,Q0,Eo,Xa,Ja,Ju]},ba={findFiberByHostInstance:$n,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},N3={bundleType:ba.bundleType,version:ba.version,rendererPackageName:ba.rendererPackageName,rendererConfig:ba.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:D.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=tx(e),e===null?null:e.stateNode},findFiberByHostInstance:ba.findFiberByHostInstance||O3,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ss=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ss.isDisabled&&ss.supportsFiber)try{no=ss.inject(N3),yr=ss}catch{}}return St.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L3,St.createPortal=function(e,r){var a=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!lc(r))throw Error(i(200));return I3(e,r,null,a)},St.createRoot=function(e,r){if(!lc(e))throw Error(i(299));var a=!1,l="",d=F1;return r!=null&&(r.unstable_strictMode===!0&&(a=!0),r.identifierPrefix!==void 0&&(l=r.identifierPrefix),r.onRecoverableError!==void 0&&(d=r.onRecoverableError)),r=ac(e,1,!1,null,null,a,!1,l,d),e[_r]=r.current,na(e.nodeType===8?e.parentNode:e),new sc(r)},St.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var r=e._reactInternals;if(r===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=tx(r),e=e===null?null:e.stateNode,e},St.flushSync=function(e){return r0(e)},St.hydrate=function(e,r,a){if(!as(r))throw Error(i(200));return os(null,e,r,!0,a)},St.hydrateRoot=function(e,r,a){if(!lc(e))throw Error(i(405));var l=a!=null&&a.hydratedSources||null,d=!1,v="",C=F1;if(a!=null&&(a.unstable_strictMode===!0&&(d=!0),a.identifierPrefix!==void 0&&(v=a.identifierPrefix),a.onRecoverableError!==void 0&&(C=a.onRecoverableError)),r=S1(r,null,e,1,a??null,d,!1,v,C),e[_r]=r.current,na(e),l)for(e=0;e<l.length;e++)a=l[e],d=a._getVersion,d=d(a._source),r.mutableSourceEagerHydrationData==null?r.mutableSourceEagerHydrationData=[a,d]:r.mutableSourceEagerHydrationData.push(a,d);return new is(r)},St.render=function(e,r,a){if(!as(r))throw Error(i(200));return os(null,e,r,!1,a)},St.unmountComponentAtNode=function(e){if(!as(e))throw Error(i(40));return e._reactRootContainer?(r0(function(){os(null,null,e,!1,function(){e._reactRootContainer=null,e[_r]=null})}),!0):!1},St.unstable_batchedUpdates=Ju,St.unstable_renderSubtreeIntoContainer=function(e,r,a,l){if(!as(a))throw Error(i(200));if(e==null||e._reactInternals===void 0)throw Error(i(38));return os(e,r,a,!1,l)},St.version="18.3.1-next-f1338f8080-20240426",St}var z1;function G3(){if(z1)return xc.exports;z1=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(n){console.error(n)}}return t(),xc.exports=$3(),xc.exports}var H1;function Z3(){if(H1)return us;H1=1;var t=G3();return us.createRoot=t.createRoot,us.hydrateRoot=t.hydrateRoot,us}var Y3=Z3();const I7=yl(Y3);var Fi=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},w0=typeof window>"u"||"Deno"in globalThis;function gt(){}function X3(t,n){return typeof t=="function"?t(n):t}function Pc(t){return typeof t=="number"&&t>=0&&t!==1/0}function kp(t,n){return Math.max(t+(n||0)-Date.now(),0)}function Hn(t,n){return typeof t=="function"?t(n):t}function Zt(t,n){return typeof t=="function"?t(n):t}function U1(t,n){const{type:i="all",exact:o,fetchStatus:s,predicate:u,queryKey:x,stale:c}=t;if(x){if(o){if(n.queryHash!==mf(x,n.options))return!1}else if(!_a(n.queryKey,x))return!1}if(i!=="all"){const h=n.isActive();if(i==="active"&&!h||i==="inactive"&&h)return!1}return!(typeof c=="boolean"&&n.isStale()!==c||s&&s!==n.state.fetchStatus||u&&!u(n))}function Q1(t,n){const{exact:i,status:o,predicate:s,mutationKey:u}=t;if(u){if(!n.options.mutationKey)return!1;if(i){if(E0(n.options.mutationKey)!==E0(u))return!1}else if(!_a(n.options.mutationKey,u))return!1}return!(o&&n.state.status!==o||s&&!s(n))}function mf(t,n){return(n?.queryKeyHashFn||E0)(t)}function E0(t){return JSON.stringify(t,(n,i)=>_c(i)?Object.keys(i).sort().reduce((o,s)=>(o[s]=i[s],o),{}):i)}function _a(t,n){return t===n?!0:typeof t!=typeof n?!1:t&&n&&typeof t=="object"&&typeof n=="object"?Object.keys(n).every(i=>_a(t[i],n[i])):!1}function Rp(t,n){if(t===n)return t;const i=j1(t)&&j1(n);if(i||_c(t)&&_c(n)){const o=i?t:Object.keys(t),s=o.length,u=i?n:Object.keys(n),x=u.length,c=i?[]:{},h=new Set(o);let f=0;for(let p=0;p<x;p++){const m=i?p:u[p];(!i&&h.has(m)||i)&&t[m]===void 0&&n[m]===void 0?(c[m]=void 0,f++):(c[m]=Rp(t[m],n[m]),c[m]===t[m]&&t[m]!==void 0&&f++)}return s===x&&f===s?t:c}return n}function ul(t,n){if(!n||Object.keys(t).length!==Object.keys(n).length)return!1;for(const i in t)if(t[i]!==n[i])return!1;return!0}function j1(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function _c(t){if(!W1(t))return!1;const n=t.constructor;if(n===void 0)return!0;const i=n.prototype;return!(!W1(i)||!i.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function W1(t){return Object.prototype.toString.call(t)==="[object Object]"}function J3(t){return new Promise(n=>{setTimeout(n,t)})}function Ic(t,n,i){return typeof i.structuralSharing=="function"?i.structuralSharing(t,n):i.structuralSharing!==!1?Rp(t,n):n}function e6(t,n,i=0){const o=[...t,n];return i&&o.length>i?o.slice(1):o}function t6(t,n,i=0){const o=[n,...t];return i&&o.length>i?o.slice(0,-1):o}var yf=Symbol();function Pp(t,n){return!t.queryFn&&n?.initialPromise?()=>n.initialPromise:!t.queryFn||t.queryFn===yf?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}function _p(t,n){return typeof t=="function"?t(...n):!!t}var x0,Rn,ui,gp,r6=(gp=class extends Fi{constructor(){super();pe(this,x0);pe(this,Rn);pe(this,ui);le(this,ui,n=>{if(!w0&&window.addEventListener){const i=()=>n();return window.addEventListener("visibilitychange",i,!1),()=>{window.removeEventListener("visibilitychange",i)}}})}onSubscribe(){L(this,Rn)||this.setEventListener(L(this,ui))}onUnsubscribe(){var n;this.hasListeners()||((n=L(this,Rn))==null||n.call(this),le(this,Rn,void 0))}setEventListener(n){var i;le(this,ui,n),(i=L(this,Rn))==null||i.call(this),le(this,Rn,n(o=>{typeof o=="boolean"?this.setFocused(o):this.onFocus()}))}setFocused(n){L(this,x0)!==n&&(le(this,x0,n),this.onFocus())}onFocus(){const n=this.isFocused();this.listeners.forEach(i=>{i(n)})}isFocused(){return typeof L(this,x0)=="boolean"?L(this,x0):globalThis.document?.visibilityState!=="hidden"}},x0=new WeakMap,Rn=new WeakMap,ui=new WeakMap,gp),gf=new r6,ci,Pn,fi,bp,n6=(bp=class extends Fi{constructor(){super();pe(this,ci,!0);pe(this,Pn);pe(this,fi);le(this,fi,n=>{if(!w0&&window.addEventListener){const i=()=>n(!0),o=()=>n(!1);return window.addEventListener("online",i,!1),window.addEventListener("offline",o,!1),()=>{window.removeEventListener("online",i),window.removeEventListener("offline",o)}}})}onSubscribe(){L(this,Pn)||this.setEventListener(L(this,fi))}onUnsubscribe(){var n;this.hasListeners()||((n=L(this,Pn))==null||n.call(this),le(this,Pn,void 0))}setEventListener(n){var i;le(this,fi,n),(i=L(this,Pn))==null||i.call(this),le(this,Pn,n(this.setOnline.bind(this)))}setOnline(n){L(this,ci)!==n&&(le(this,ci,n),this.listeners.forEach(o=>{o(n)}))}isOnline(){return L(this,ci)}},ci=new WeakMap,Pn=new WeakMap,fi=new WeakMap,bp),cl=new n6;function Oc(){let t,n;const i=new Promise((s,u)=>{t=s,n=u});i.status="pending",i.catch(()=>{});function o(s){Object.assign(i,s),delete i.resolve,delete i.reject}return i.resolve=s=>{o({status:"fulfilled",value:s}),t(s)},i.reject=s=>{o({status:"rejected",reason:s}),n(s)},i}function i6(t){return Math.min(1e3*2**t,3e4)}function Ip(t){return(t??"online")==="online"?cl.isOnline():!0}var Op=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function pc(t){return t instanceof Op}function Mp(t){let n=!1,i=0,o=!1,s;const u=Oc(),x=g=>{o||(y(new Op(g)),t.abort?.())},c=()=>{n=!0},h=()=>{n=!1},f=()=>gf.isFocused()&&(t.networkMode==="always"||cl.isOnline())&&t.canRun(),p=()=>Ip(t.networkMode)&&t.canRun(),m=g=>{o||(o=!0,t.onSuccess?.(g),s?.(),u.resolve(g))},y=g=>{o||(o=!0,t.onError?.(g),s?.(),u.reject(g))},A=()=>new Promise(g=>{s=b=>{(o||f())&&g(b)},t.onPause?.()}).then(()=>{s=void 0,o||t.onContinue?.()}),w=()=>{if(o)return;let g;const b=i===0?t.initialPromise:void 0;try{g=b??t.fn()}catch(S){g=Promise.reject(S)}Promise.resolve(g).then(m).catch(S=>{if(o)return;const B=t.retry??(w0?0:3),E=t.retryDelay??i6,D=typeof E=="function"?E(i,S):E,k=B===!0||typeof B=="number"&&i<B||typeof B=="function"&&B(i,S);if(n||!k){y(S);return}i++,t.onFail?.(i,S),J3(D).then(()=>f()?void 0:A()).then(()=>{n?y(S):w()})})};return{promise:u,cancel:x,continue:()=>(s?.(),u),cancelRetry:c,continueRetry:h,canStart:p,start:()=>(p()?w():A().then(w),u)}}var a6=t=>setTimeout(t,0);function o6(){let t=[],n=0,i=c=>{c()},o=c=>{c()},s=a6;const u=c=>{n?t.push(c):s(()=>{i(c)})},x=()=>{const c=t;t=[],c.length&&s(()=>{o(()=>{c.forEach(h=>{i(h)})})})};return{batch:c=>{let h;n++;try{h=c()}finally{n--,n||x()}return h},batchCalls:c=>(...h)=>{u(()=>{c(...h)})},schedule:u,setNotifyFunction:c=>{i=c},setBatchNotifyFunction:c=>{o=c},setScheduler:c=>{s=c}}}var Xe=o6(),d0,Ap,Lp=(Ap=class{constructor(){pe(this,d0)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Pc(this.gcTime)&&le(this,d0,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(w0?1/0:300*1e3))}clearGcTimeout(){L(this,d0)&&(clearTimeout(L(this,d0)),le(this,d0,void 0))}},d0=new WeakMap,Ap),xi,h0,$t,p0,dt,Ha,v0,fr,Qr,wp,s6=(wp=class extends Lp{constructor(n){super();pe(this,fr);pe(this,xi);pe(this,h0);pe(this,$t);pe(this,p0);pe(this,dt);pe(this,Ha);pe(this,v0);le(this,v0,!1),le(this,Ha,n.defaultOptions),this.setOptions(n.options),this.observers=[],le(this,p0,n.client),le(this,$t,L(this,p0).getQueryCache()),this.queryKey=n.queryKey,this.queryHash=n.queryHash,le(this,xi,l6(this.options)),this.state=n.state??L(this,xi),this.scheduleGc()}get meta(){return this.options.meta}get promise(){return L(this,dt)?.promise}setOptions(n){this.options={...L(this,Ha),...n},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&L(this,$t).remove(this)}setData(n,i){const o=Ic(this.state.data,n,this.options);return be(this,fr,Qr).call(this,{data:o,type:"success",dataUpdatedAt:i?.updatedAt,manual:i?.manual}),o}setState(n,i){be(this,fr,Qr).call(this,{type:"setState",state:n,setStateOptions:i})}cancel(n){const i=L(this,dt)?.promise;return L(this,dt)?.cancel(n),i?i.then(gt).catch(gt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(L(this,xi))}isActive(){return this.observers.some(n=>Zt(n.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===yf||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(n=>Hn(n.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(n=>n.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(n=0){return this.state.data===void 0?!0:n==="static"?!1:this.state.isInvalidated?!0:!kp(this.state.dataUpdatedAt,n)}onFocus(){this.observers.find(i=>i.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),L(this,dt)?.continue()}onOnline(){this.observers.find(i=>i.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),L(this,dt)?.continue()}addObserver(n){this.observers.includes(n)||(this.observers.push(n),this.clearGcTimeout(),L(this,$t).notify({type:"observerAdded",query:this,observer:n}))}removeObserver(n){this.observers.includes(n)&&(this.observers=this.observers.filter(i=>i!==n),this.observers.length||(L(this,dt)&&(L(this,v0)?L(this,dt).cancel({revert:!0}):L(this,dt).cancelRetry()),this.scheduleGc()),L(this,$t).notify({type:"observerRemoved",query:this,observer:n}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||be(this,fr,Qr).call(this,{type:"invalidate"})}fetch(n,i){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&i?.cancelRefetch)this.cancel({silent:!0});else if(L(this,dt))return L(this,dt).continueRetry(),L(this,dt).promise}if(n&&this.setOptions(n),!this.options.queryFn){const f=this.observers.find(p=>p.options.queryFn);f&&this.setOptions(f.options)}const o=new AbortController,s=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(le(this,v0,!0),o.signal)})},u=()=>{const f=Pp(this.options,i),m=(()=>{const y={client:L(this,p0),queryKey:this.queryKey,meta:this.meta};return s(y),y})();return le(this,v0,!1),this.options.persister?this.options.persister(f,m,this):f(m)},c=(()=>{const f={fetchOptions:i,options:this.options,queryKey:this.queryKey,client:L(this,p0),state:this.state,fetchFn:u};return s(f),f})();this.options.behavior?.onFetch(c,this),le(this,h0,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==c.fetchOptions?.meta)&&be(this,fr,Qr).call(this,{type:"fetch",meta:c.fetchOptions?.meta});const h=f=>{pc(f)&&f.silent||be(this,fr,Qr).call(this,{type:"error",error:f}),pc(f)||(L(this,$t).config.onError?.(f,this),L(this,$t).config.onSettled?.(this.state.data,f,this)),this.scheduleGc()};return le(this,dt,Mp({initialPromise:i?.initialPromise,fn:c.fetchFn,abort:o.abort.bind(o),onSuccess:f=>{if(f===void 0){h(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(f)}catch(p){h(p);return}L(this,$t).config.onSuccess?.(f,this),L(this,$t).config.onSettled?.(f,this.state.error,this),this.scheduleGc()},onError:h,onFail:(f,p)=>{be(this,fr,Qr).call(this,{type:"failed",failureCount:f,error:p})},onPause:()=>{be(this,fr,Qr).call(this,{type:"pause"})},onContinue:()=>{be(this,fr,Qr).call(this,{type:"continue"})},retry:c.options.retry,retryDelay:c.options.retryDelay,networkMode:c.options.networkMode,canRun:()=>!0})),L(this,dt).start()}},xi=new WeakMap,h0=new WeakMap,$t=new WeakMap,p0=new WeakMap,dt=new WeakMap,Ha=new WeakMap,v0=new WeakMap,fr=new WeakSet,Qr=function(n){const i=o=>{switch(n.type){case"failed":return{...o,fetchFailureCount:n.failureCount,fetchFailureReason:n.error};case"pause":return{...o,fetchStatus:"paused"};case"continue":return{...o,fetchStatus:"fetching"};case"fetch":return{...o,...Np(o.data,this.options),fetchMeta:n.meta??null};case"success":return le(this,h0,void 0),{...o,data:n.data,dataUpdateCount:o.dataUpdateCount+1,dataUpdatedAt:n.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!n.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=n.error;return pc(s)&&s.revert&&L(this,h0)?{...L(this,h0),fetchStatus:"idle"}:{...o,error:s,errorUpdateCount:o.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:o.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...o,isInvalidated:!0};case"setState":return{...o,...n.state}}};this.state=i(this.state),Xe.batch(()=>{this.observers.forEach(o=>{o.onQueryUpdate()}),L(this,$t).notify({query:this,type:"updated",action:n})})},wp);function Np(t,n){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ip(n.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function l6(t){const n=typeof t.initialData=="function"?t.initialData():t.initialData,i=n!==void 0,o=i?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:n,dataUpdateCount:0,dataUpdatedAt:i?o??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}var Br,Ep,u6=(Ep=class extends Fi{constructor(n={}){super();pe(this,Br);this.config=n,le(this,Br,new Map)}build(n,i,o){const s=i.queryKey,u=i.queryHash??mf(s,i);let x=this.get(u);return x||(x=new s6({client:n,queryKey:s,queryHash:u,options:n.defaultQueryOptions(i),state:o,defaultOptions:n.getQueryDefaults(s)}),this.add(x)),x}add(n){L(this,Br).has(n.queryHash)||(L(this,Br).set(n.queryHash,n),this.notify({type:"added",query:n}))}remove(n){const i=L(this,Br).get(n.queryHash);i&&(n.destroy(),i===n&&L(this,Br).delete(n.queryHash),this.notify({type:"removed",query:n}))}clear(){Xe.batch(()=>{this.getAll().forEach(n=>{this.remove(n)})})}get(n){return L(this,Br).get(n)}getAll(){return[...L(this,Br).values()]}find(n){const i={exact:!0,...n};return this.getAll().find(o=>U1(i,o))}findAll(n={}){const i=this.getAll();return Object.keys(n).length>0?i.filter(o=>U1(n,o)):i}notify(n){Xe.batch(()=>{this.listeners.forEach(i=>{i(n)})})}onFocus(){Xe.batch(()=>{this.getAll().forEach(n=>{n.onFocus()})})}onOnline(){Xe.batch(()=>{this.getAll().forEach(n=>{n.onOnline()})})}},Br=new WeakMap,Ep),Sr,mt,m0,Dr,Fn,Cp,c6=(Cp=class extends Lp{constructor(n){super();pe(this,Dr);pe(this,Sr);pe(this,mt);pe(this,m0);this.mutationId=n.mutationId,le(this,mt,n.mutationCache),le(this,Sr,[]),this.state=n.state||Tp(),this.setOptions(n.options),this.scheduleGc()}setOptions(n){this.options=n,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(n){L(this,Sr).includes(n)||(L(this,Sr).push(n),this.clearGcTimeout(),L(this,mt).notify({type:"observerAdded",mutation:this,observer:n}))}removeObserver(n){le(this,Sr,L(this,Sr).filter(i=>i!==n)),this.scheduleGc(),L(this,mt).notify({type:"observerRemoved",mutation:this,observer:n})}optionalRemove(){L(this,Sr).length||(this.state.status==="pending"?this.scheduleGc():L(this,mt).remove(this))}continue(){return L(this,m0)?.continue()??this.execute(this.state.variables)}async execute(n){const i=()=>{be(this,Dr,Fn).call(this,{type:"continue"})};le(this,m0,Mp({fn:()=>this.options.mutationFn?this.options.mutationFn(n):Promise.reject(new Error("No mutationFn found")),onFail:(u,x)=>{be(this,Dr,Fn).call(this,{type:"failed",failureCount:u,error:x})},onPause:()=>{be(this,Dr,Fn).call(this,{type:"pause"})},onContinue:i,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>L(this,mt).canRun(this)}));const o=this.state.status==="pending",s=!L(this,m0).canStart();try{if(o)i();else{be(this,Dr,Fn).call(this,{type:"pending",variables:n,isPaused:s}),await L(this,mt).config.onMutate?.(n,this);const x=await this.options.onMutate?.(n);x!==this.state.context&&be(this,Dr,Fn).call(this,{type:"pending",context:x,variables:n,isPaused:s})}const u=await L(this,m0).start();return await L(this,mt).config.onSuccess?.(u,n,this.state.context,this),await this.options.onSuccess?.(u,n,this.state.context),await L(this,mt).config.onSettled?.(u,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(u,null,n,this.state.context),be(this,Dr,Fn).call(this,{type:"success",data:u}),u}catch(u){try{throw await L(this,mt).config.onError?.(u,n,this.state.context,this),await this.options.onError?.(u,n,this.state.context),await L(this,mt).config.onSettled?.(void 0,u,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,u,n,this.state.context),u}finally{be(this,Dr,Fn).call(this,{type:"error",error:u})}}finally{L(this,mt).runNext(this)}}},Sr=new WeakMap,mt=new WeakMap,m0=new WeakMap,Dr=new WeakSet,Fn=function(n){const i=o=>{switch(n.type){case"failed":return{...o,failureCount:n.failureCount,failureReason:n.error};case"pause":return{...o,isPaused:!0};case"continue":return{...o,isPaused:!1};case"pending":return{...o,context:n.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:n.isPaused,status:"pending",variables:n.variables,submittedAt:Date.now()};case"success":return{...o,data:n.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...o,data:void 0,error:n.error,failureCount:o.failureCount+1,failureReason:n.error,isPaused:!1,status:"error"}}};this.state=i(this.state),Xe.batch(()=>{L(this,Sr).forEach(o=>{o.onMutationUpdate(n)}),L(this,mt).notify({mutation:this,type:"updated",action:n})})},Cp);function Tp(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Wr,xr,Ua,Bp,f6=(Bp=class extends Fi{constructor(n={}){super();pe(this,Wr);pe(this,xr);pe(this,Ua);this.config=n,le(this,Wr,new Set),le(this,xr,new Map),le(this,Ua,0)}build(n,i,o){const s=new c6({mutationCache:this,mutationId:++ls(this,Ua)._,options:n.defaultMutationOptions(i),state:o});return this.add(s),s}add(n){L(this,Wr).add(n);const i=cs(n);if(typeof i=="string"){const o=L(this,xr).get(i);o?o.push(n):L(this,xr).set(i,[n])}this.notify({type:"added",mutation:n})}remove(n){if(L(this,Wr).delete(n)){const i=cs(n);if(typeof i=="string"){const o=L(this,xr).get(i);if(o)if(o.length>1){const s=o.indexOf(n);s!==-1&&o.splice(s,1)}else o[0]===n&&L(this,xr).delete(i)}}this.notify({type:"removed",mutation:n})}canRun(n){const i=cs(n);if(typeof i=="string"){const s=L(this,xr).get(i)?.find(u=>u.state.status==="pending");return!s||s===n}else return!0}runNext(n){const i=cs(n);return typeof i=="string"?L(this,xr).get(i)?.find(s=>s!==n&&s.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){Xe.batch(()=>{L(this,Wr).forEach(n=>{this.notify({type:"removed",mutation:n})}),L(this,Wr).clear(),L(this,xr).clear()})}getAll(){return Array.from(L(this,Wr))}find(n){const i={exact:!0,...n};return this.getAll().find(o=>Q1(i,o))}findAll(n={}){return this.getAll().filter(i=>Q1(n,i))}notify(n){Xe.batch(()=>{this.listeners.forEach(i=>{i(n)})})}resumePausedMutations(){const n=this.getAll().filter(i=>i.state.isPaused);return Xe.batch(()=>Promise.all(n.map(i=>i.continue().catch(gt))))}},Wr=new WeakMap,xr=new WeakMap,Ua=new WeakMap,Bp);function cs(t){return t.options.scope?.id}function V1(t){return{onFetch:(n,i)=>{const o=n.options,s=n.fetchOptions?.meta?.fetchMore?.direction,u=n.state.data?.pages||[],x=n.state.data?.pageParams||[];let c={pages:[],pageParams:[]},h=0;const f=async()=>{let p=!1;const m=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(n.signal.aborted?p=!0:n.signal.addEventListener("abort",()=>{p=!0}),n.signal)})},y=Pp(n.options,n.fetchOptions),A=async(w,g,b)=>{if(p)return Promise.reject();if(g==null&&w.pages.length)return Promise.resolve(w);const B=(()=>{const R={client:n.client,queryKey:n.queryKey,pageParam:g,direction:b?"backward":"forward",meta:n.options.meta};return m(R),R})(),E=await y(B),{maxPages:D}=n.options,k=b?t6:e6;return{pages:k(w.pages,E,D),pageParams:k(w.pageParams,g,D)}};if(s&&u.length){const w=s==="backward",g=w?x6:q1,b={pages:u,pageParams:x},S=g(o,b);c=await A(b,S,w)}else{const w=t??u.length;do{const g=h===0?x[0]??o.initialPageParam:q1(o,c);if(h>0&&g==null)break;c=await A(c,g),h++}while(h<w)}return c};n.options.persister?n.fetchFn=()=>n.options.persister?.(f,{client:n.client,queryKey:n.queryKey,meta:n.options.meta,signal:n.signal},i):n.fetchFn=f}}}function q1(t,{pages:n,pageParams:i}){const o=n.length-1;return n.length>0?t.getNextPageParam(n[o],n,i[o],i):void 0}function x6(t,{pages:n,pageParams:i}){return n.length>0?t.getPreviousPageParam?.(n[0],n,i[0],i):void 0}var je,_n,In,di,hi,On,pi,vi,Sp,O7=(Sp=class{constructor(t={}){pe(this,je);pe(this,_n);pe(this,In);pe(this,di);pe(this,hi);pe(this,On);pe(this,pi);pe(this,vi);le(this,je,t.queryCache||new u6),le(this,_n,t.mutationCache||new f6),le(this,In,t.defaultOptions||{}),le(this,di,new Map),le(this,hi,new Map),le(this,On,0)}mount(){ls(this,On)._++,L(this,On)===1&&(le(this,pi,gf.subscribe(async t=>{t&&(await this.resumePausedMutations(),L(this,je).onFocus())})),le(this,vi,cl.subscribe(async t=>{t&&(await this.resumePausedMutations(),L(this,je).onOnline())})))}unmount(){var t,n;ls(this,On)._--,L(this,On)===0&&((t=L(this,pi))==null||t.call(this),le(this,pi,void 0),(n=L(this,vi))==null||n.call(this),le(this,vi,void 0))}isFetching(t){return L(this,je).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return L(this,_n).findAll({...t,status:"pending"}).length}getQueryData(t){const n=this.defaultQueryOptions({queryKey:t});return L(this,je).get(n.queryHash)?.state.data}ensureQueryData(t){const n=this.defaultQueryOptions(t),i=L(this,je).build(this,n),o=i.state.data;return o===void 0?this.fetchQuery(t):(t.revalidateIfStale&&i.isStaleByTime(Hn(n.staleTime,i))&&this.prefetchQuery(n),Promise.resolve(o))}getQueriesData(t){return L(this,je).findAll(t).map(({queryKey:n,state:i})=>{const o=i.data;return[n,o]})}setQueryData(t,n,i){const o=this.defaultQueryOptions({queryKey:t}),u=L(this,je).get(o.queryHash)?.state.data,x=X3(n,u);if(x!==void 0)return L(this,je).build(this,o).setData(x,{...i,manual:!0})}setQueriesData(t,n,i){return Xe.batch(()=>L(this,je).findAll(t).map(({queryKey:o})=>[o,this.setQueryData(o,n,i)]))}getQueryState(t){const n=this.defaultQueryOptions({queryKey:t});return L(this,je).get(n.queryHash)?.state}removeQueries(t){const n=L(this,je);Xe.batch(()=>{n.findAll(t).forEach(i=>{n.remove(i)})})}resetQueries(t,n){const i=L(this,je);return Xe.batch(()=>(i.findAll(t).forEach(o=>{o.reset()}),this.refetchQueries({type:"active",...t},n)))}cancelQueries(t,n={}){const i={revert:!0,...n},o=Xe.batch(()=>L(this,je).findAll(t).map(s=>s.cancel(i)));return Promise.all(o).then(gt).catch(gt)}invalidateQueries(t,n={}){return Xe.batch(()=>(L(this,je).findAll(t).forEach(i=>{i.invalidate()}),t?.refetchType==="none"?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},n)))}refetchQueries(t,n={}){const i={...n,cancelRefetch:n.cancelRefetch??!0},o=Xe.batch(()=>L(this,je).findAll(t).filter(s=>!s.isDisabled()&&!s.isStatic()).map(s=>{let u=s.fetch(void 0,i);return i.throwOnError||(u=u.catch(gt)),s.state.fetchStatus==="paused"?Promise.resolve():u}));return Promise.all(o).then(gt)}fetchQuery(t){const n=this.defaultQueryOptions(t);n.retry===void 0&&(n.retry=!1);const i=L(this,je).build(this,n);return i.isStaleByTime(Hn(n.staleTime,i))?i.fetch(n):Promise.resolve(i.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(gt).catch(gt)}fetchInfiniteQuery(t){return t.behavior=V1(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(gt).catch(gt)}ensureInfiniteQueryData(t){return t.behavior=V1(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return cl.isOnline()?L(this,_n).resumePausedMutations():Promise.resolve()}getQueryCache(){return L(this,je)}getMutationCache(){return L(this,_n)}getDefaultOptions(){return L(this,In)}setDefaultOptions(t){le(this,In,t)}setQueryDefaults(t,n){L(this,di).set(E0(t),{queryKey:t,defaultOptions:n})}getQueryDefaults(t){const n=[...L(this,di).values()],i={};return n.forEach(o=>{_a(t,o.queryKey)&&Object.assign(i,o.defaultOptions)}),i}setMutationDefaults(t,n){L(this,hi).set(E0(t),{mutationKey:t,defaultOptions:n})}getMutationDefaults(t){const n=[...L(this,hi).values()],i={};return n.forEach(o=>{_a(t,o.mutationKey)&&Object.assign(i,o.defaultOptions)}),i}defaultQueryOptions(t){if(t._defaulted)return t;const n={...L(this,In).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return n.queryHash||(n.queryHash=mf(n.queryKey,n)),n.refetchOnReconnect===void 0&&(n.refetchOnReconnect=n.networkMode!=="always"),n.throwOnError===void 0&&(n.throwOnError=!!n.suspense),!n.networkMode&&n.persister&&(n.networkMode="offlineFirst"),n.queryFn===yf&&(n.enabled=!1),n}defaultMutationOptions(t){return t?._defaulted?t:{...L(this,In).mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){L(this,je).clear(),L(this,_n).clear()}},je=new WeakMap,_n=new WeakMap,In=new WeakMap,di=new WeakMap,hi=new WeakMap,On=new WeakMap,pi=new WeakMap,vi=new WeakMap,Sp),Dt,Ce,Qa,yt,y0,mi,Mn,Ln,ja,yi,gi,g0,b0,Nn,bi,ke,Sa,Mc,Lc,Nc,Tc,zc,Hc,Uc,zp,Dp,d6=(Dp=class extends Fi{constructor(n,i){super();pe(this,ke);pe(this,Dt);pe(this,Ce);pe(this,Qa);pe(this,yt);pe(this,y0);pe(this,mi);pe(this,Mn);pe(this,Ln);pe(this,ja);pe(this,yi);pe(this,gi);pe(this,g0);pe(this,b0);pe(this,Nn);pe(this,bi,new Set);this.options=i,le(this,Dt,n),le(this,Ln,null),le(this,Mn,Oc()),this.options.experimental_prefetchInRender||L(this,Mn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(i)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(L(this,Ce).addObserver(this),K1(L(this,Ce),this.options)?be(this,ke,Sa).call(this):this.updateResult(),be(this,ke,Tc).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Qc(L(this,Ce),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Qc(L(this,Ce),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,be(this,ke,zc).call(this),be(this,ke,Hc).call(this),L(this,Ce).removeObserver(this)}setOptions(n){const i=this.options,o=L(this,Ce);if(this.options=L(this,Dt).defaultQueryOptions(n),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Zt(this.options.enabled,L(this,Ce))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");be(this,ke,Uc).call(this),L(this,Ce).setOptions(this.options),i._defaulted&&!ul(this.options,i)&&L(this,Dt).getQueryCache().notify({type:"observerOptionsUpdated",query:L(this,Ce),observer:this});const s=this.hasListeners();s&&$1(L(this,Ce),o,this.options,i)&&be(this,ke,Sa).call(this),this.updateResult(),s&&(L(this,Ce)!==o||Zt(this.options.enabled,L(this,Ce))!==Zt(i.enabled,L(this,Ce))||Hn(this.options.staleTime,L(this,Ce))!==Hn(i.staleTime,L(this,Ce)))&&be(this,ke,Mc).call(this);const u=be(this,ke,Lc).call(this);s&&(L(this,Ce)!==o||Zt(this.options.enabled,L(this,Ce))!==Zt(i.enabled,L(this,Ce))||u!==L(this,Nn))&&be(this,ke,Nc).call(this,u)}getOptimisticResult(n){const i=L(this,Dt).getQueryCache().build(L(this,Dt),n),o=this.createResult(i,n);return p6(this,o)&&(le(this,yt,o),le(this,mi,this.options),le(this,y0,L(this,Ce).state)),o}getCurrentResult(){return L(this,yt)}trackResult(n,i){return new Proxy(n,{get:(o,s)=>(this.trackProp(s),i?.(s),Reflect.get(o,s))})}trackProp(n){L(this,bi).add(n)}getCurrentQuery(){return L(this,Ce)}refetch({...n}={}){return this.fetch({...n})}fetchOptimistic(n){const i=L(this,Dt).defaultQueryOptions(n),o=L(this,Dt).getQueryCache().build(L(this,Dt),i);return o.fetch().then(()=>this.createResult(o,i))}fetch(n){return be(this,ke,Sa).call(this,{...n,cancelRefetch:n.cancelRefetch??!0}).then(()=>(this.updateResult(),L(this,yt)))}createResult(n,i){const o=L(this,Ce),s=this.options,u=L(this,yt),x=L(this,y0),c=L(this,mi),f=n!==o?n.state:L(this,Qa),{state:p}=n;let m={...p},y=!1,A;if(i._optimisticResults){const Q=this.hasListeners(),_=!Q&&K1(n,i),T=Q&&$1(n,o,i,s);(_||T)&&(m={...m,...Np(p.data,n.options)}),i._optimisticResults==="isRestoring"&&(m.fetchStatus="idle")}let{error:w,errorUpdatedAt:g,status:b}=m;A=m.data;let S=!1;if(i.placeholderData!==void 0&&A===void 0&&b==="pending"){let Q;u?.isPlaceholderData&&i.placeholderData===c?.placeholderData?(Q=u.data,S=!0):Q=typeof i.placeholderData=="function"?i.placeholderData(L(this,gi)?.state.data,L(this,gi)):i.placeholderData,Q!==void 0&&(b="success",A=Ic(u?.data,Q,i),y=!0)}if(i.select&&A!==void 0&&!S)if(u&&A===x?.data&&i.select===L(this,ja))A=L(this,yi);else try{le(this,ja,i.select),A=i.select(A),A=Ic(u?.data,A,i),le(this,yi,A),le(this,Ln,null)}catch(Q){le(this,Ln,Q)}L(this,Ln)&&(w=L(this,Ln),A=L(this,yi),g=Date.now(),b="error");const B=m.fetchStatus==="fetching",E=b==="pending",D=b==="error",k=E&&B,R=A!==void 0,O={status:b,fetchStatus:m.fetchStatus,isPending:E,isSuccess:b==="success",isError:D,isInitialLoading:k,isLoading:k,data:A,dataUpdatedAt:m.dataUpdatedAt,error:w,errorUpdatedAt:g,failureCount:m.fetchFailureCount,failureReason:m.fetchFailureReason,errorUpdateCount:m.errorUpdateCount,isFetched:m.dataUpdateCount>0||m.errorUpdateCount>0,isFetchedAfterMount:m.dataUpdateCount>f.dataUpdateCount||m.errorUpdateCount>f.errorUpdateCount,isFetching:B,isRefetching:B&&!E,isLoadingError:D&&!R,isPaused:m.fetchStatus==="paused",isPlaceholderData:y,isRefetchError:D&&R,isStale:bf(n,i),refetch:this.refetch,promise:L(this,Mn),isEnabled:Zt(i.enabled,n)!==!1};if(this.options.experimental_prefetchInRender){const Q=q=>{O.status==="error"?q.reject(O.error):O.data!==void 0&&q.resolve(O.data)},_=()=>{const q=le(this,Mn,O.promise=Oc());Q(q)},T=L(this,Mn);switch(T.status){case"pending":n.queryHash===o.queryHash&&Q(T);break;case"fulfilled":(O.status==="error"||O.data!==T.value)&&_();break;case"rejected":(O.status!=="error"||O.error!==T.reason)&&_();break}}return O}updateResult(){const n=L(this,yt),i=this.createResult(L(this,Ce),this.options);if(le(this,y0,L(this,Ce).state),le(this,mi,this.options),L(this,y0).data!==void 0&&le(this,gi,L(this,Ce)),ul(i,n))return;le(this,yt,i);const o=()=>{if(!n)return!0;const{notifyOnChangeProps:s}=this.options,u=typeof s=="function"?s():s;if(u==="all"||!u&&!L(this,bi).size)return!0;const x=new Set(u??L(this,bi));return this.options.throwOnError&&x.add("error"),Object.keys(L(this,yt)).some(c=>{const h=c;return L(this,yt)[h]!==n[h]&&x.has(h)})};be(this,ke,zp).call(this,{listeners:o()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&be(this,ke,Tc).call(this)}},Dt=new WeakMap,Ce=new WeakMap,Qa=new WeakMap,yt=new WeakMap,y0=new WeakMap,mi=new WeakMap,Mn=new WeakMap,Ln=new WeakMap,ja=new WeakMap,yi=new WeakMap,gi=new WeakMap,g0=new WeakMap,b0=new WeakMap,Nn=new WeakMap,bi=new WeakMap,ke=new WeakSet,Sa=function(n){be(this,ke,Uc).call(this);let i=L(this,Ce).fetch(this.options,n);return n?.throwOnError||(i=i.catch(gt)),i},Mc=function(){be(this,ke,zc).call(this);const n=Hn(this.options.staleTime,L(this,Ce));if(w0||L(this,yt).isStale||!Pc(n))return;const o=kp(L(this,yt).dataUpdatedAt,n)+1;le(this,g0,setTimeout(()=>{L(this,yt).isStale||this.updateResult()},o))},Lc=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(L(this,Ce)):this.options.refetchInterval)??!1},Nc=function(n){be(this,ke,Hc).call(this),le(this,Nn,n),!(w0||Zt(this.options.enabled,L(this,Ce))===!1||!Pc(L(this,Nn))||L(this,Nn)===0)&&le(this,b0,setInterval(()=>{(this.options.refetchIntervalInBackground||gf.isFocused())&&be(this,ke,Sa).call(this)},L(this,Nn)))},Tc=function(){be(this,ke,Mc).call(this),be(this,ke,Nc).call(this,be(this,ke,Lc).call(this))},zc=function(){L(this,g0)&&(clearTimeout(L(this,g0)),le(this,g0,void 0))},Hc=function(){L(this,b0)&&(clearInterval(L(this,b0)),le(this,b0,void 0))},Uc=function(){const n=L(this,Dt).getQueryCache().build(L(this,Dt),this.options);if(n===L(this,Ce))return;const i=L(this,Ce);le(this,Ce,n),le(this,Qa,n.state),this.hasListeners()&&(i?.removeObserver(this),n.addObserver(this))},zp=function(n){Xe.batch(()=>{n.listeners&&this.listeners.forEach(i=>{i(L(this,yt))}),L(this,Dt).getQueryCache().notify({query:L(this,Ce),type:"observerResultsUpdated"})})},Dp);function h6(t,n){return Zt(n.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&n.retryOnMount===!1)}function K1(t,n){return h6(t,n)||t.state.data!==void 0&&Qc(t,n,n.refetchOnMount)}function Qc(t,n,i){if(Zt(n.enabled,t)!==!1&&Hn(n.staleTime,t)!=="static"){const o=typeof i=="function"?i(t):i;return o==="always"||o!==!1&&bf(t,n)}return!1}function $1(t,n,i,o){return(t!==n||Zt(o.enabled,t)===!1)&&(!i.suspense||t.state.status!=="error")&&bf(t,i)}function bf(t,n){return Zt(n.enabled,t)!==!1&&t.isStaleByTime(Hn(n.staleTime,t))}function p6(t,n){return!ul(t.getCurrentResult(),n)}var Tn,zn,Ft,Vr,$r,gs,jc,Fp,v6=(Fp=class extends Fi{constructor(i,o){super();pe(this,$r);pe(this,Tn);pe(this,zn);pe(this,Ft);pe(this,Vr);le(this,Tn,i),this.setOptions(o),this.bindMethods(),be(this,$r,gs).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(i){const o=this.options;this.options=L(this,Tn).defaultMutationOptions(i),ul(this.options,o)||L(this,Tn).getMutationCache().notify({type:"observerOptionsUpdated",mutation:L(this,Ft),observer:this}),o?.mutationKey&&this.options.mutationKey&&E0(o.mutationKey)!==E0(this.options.mutationKey)?this.reset():L(this,Ft)?.state.status==="pending"&&L(this,Ft).setOptions(this.options)}onUnsubscribe(){this.hasListeners()||L(this,Ft)?.removeObserver(this)}onMutationUpdate(i){be(this,$r,gs).call(this),be(this,$r,jc).call(this,i)}getCurrentResult(){return L(this,zn)}reset(){L(this,Ft)?.removeObserver(this),le(this,Ft,void 0),be(this,$r,gs).call(this),be(this,$r,jc).call(this)}mutate(i,o){return le(this,Vr,o),L(this,Ft)?.removeObserver(this),le(this,Ft,L(this,Tn).getMutationCache().build(L(this,Tn),this.options)),L(this,Ft).addObserver(this),L(this,Ft).execute(i)}},Tn=new WeakMap,zn=new WeakMap,Ft=new WeakMap,Vr=new WeakMap,$r=new WeakSet,gs=function(){const i=L(this,Ft)?.state??Tp();le(this,zn,{...i,isPending:i.status==="pending",isSuccess:i.status==="success",isError:i.status==="error",isIdle:i.status==="idle",mutate:this.mutate,reset:this.reset})},jc=function(i){Xe.batch(()=>{if(L(this,Vr)&&this.hasListeners()){const o=L(this,zn).variables,s=L(this,zn).context;i?.type==="success"?(L(this,Vr).onSuccess?.(i.data,o,s),L(this,Vr).onSettled?.(i.data,null,o,s)):i?.type==="error"&&(L(this,Vr).onError?.(i.error,o,s),L(this,Vr).onSettled?.(void 0,i.error,o,s))}this.listeners.forEach(o=>{o(L(this,zn))})})},Fp),Hp=U.createContext(void 0),Up=t=>{const n=U.useContext(Hp);if(!n)throw new Error("No QueryClient set, use QueryClientProvider to set one");return n},L7=({client:t,children:n})=>(U.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),V3.jsx(Hp.Provider,{value:t,children:n})),Qp=U.createContext(!1),m6=()=>U.useContext(Qp);Qp.Provider;function y6(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var g6=U.createContext(y6()),b6=()=>U.useContext(g6),A6=(t,n)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(n.isReset()||(t.retryOnMount=!1))},w6=t=>{U.useEffect(()=>{t.clearReset()},[t])},E6=({result:t,errorResetBoundary:n,throwOnError:i,query:o,suspense:s})=>t.isError&&!n.isReset()&&!t.isFetching&&o&&(s&&t.data===void 0||_p(i,[t.error,o])),C6=t=>{if(t.suspense){const n=o=>o==="static"?o:Math.max(o??1e3,1e3),i=t.staleTime;t.staleTime=typeof i=="function"?(...o)=>n(i(...o)):n(i),typeof t.gcTime=="number"&&(t.gcTime=Math.max(t.gcTime,1e3))}},B6=(t,n)=>t.isLoading&&t.isFetching&&!n,S6=(t,n)=>t?.suspense&&n.isPending,G1=(t,n,i)=>n.fetchOptimistic(t).catch(()=>{i.clearReset()});function D6(t,n,i){const o=m6(),s=b6(),u=Up(),x=u.defaultQueryOptions(t);u.getDefaultOptions().queries?._experimental_beforeQuery?.(x),x._optimisticResults=o?"isRestoring":"optimistic",C6(x),A6(x,s),w6(s);const c=!u.getQueryCache().get(x.queryHash),[h]=U.useState(()=>new n(u,x)),f=h.getOptimisticResult(x),p=!o&&t.subscribed!==!1;if(U.useSyncExternalStore(U.useCallback(m=>{const y=p?h.subscribe(Xe.batchCalls(m)):gt;return h.updateResult(),y},[h,p]),()=>h.getCurrentResult(),()=>h.getCurrentResult()),U.useEffect(()=>{h.setOptions(x)},[x,h]),S6(x,f))throw G1(x,h,s);if(E6({result:f,errorResetBoundary:s,throwOnError:x.throwOnError,query:u.getQueryCache().get(x.queryHash),suspense:x.suspense}))throw f.error;return u.getDefaultOptions().queries?._experimental_afterQuery?.(x,f),x.experimental_prefetchInRender&&!w0&&B6(f,o)&&(c?G1(x,h,s):u.getQueryCache().get(x.queryHash)?.promise)?.catch(gt).finally(()=>{h.updateResult()}),x.notifyOnChangeProps?f:h.trackResult(f)}function N7(t,n){return D6(t,d6)}function T7(t,n){const i=Up(),[o]=U.useState(()=>new v6(i,t));U.useEffect(()=>{o.setOptions(t)},[o,t]);const s=U.useSyncExternalStore(U.useCallback(x=>o.subscribe(Xe.batchCalls(x)),[o]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),u=U.useCallback((x,c)=>{o.mutate(x,c).catch(gt)},[o]);if(s.error&&_p(o.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:u,mutateAsync:s.mutate}}const F6="1.0.8";let Mt=class Wc extends Error{constructor(n,i={}){const o=i.cause instanceof Wc?i.cause.details:i.cause?.message?i.cause.message:i.details,s=i.cause instanceof Wc&&i.cause.docsPath||i.docsPath,u=[n||"An error occurred.","",...i.metaMessages?[...i.metaMessages,""]:[],...s?[`Docs: https://abitype.dev${s}`]:[],...o?[`Details: ${o}`]:[],`Version: abitype@${F6}`].join(`
`);super(u),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metaMessages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiTypeError"}),i.cause&&(this.cause=i.cause),this.details=o,this.docsPath=s,this.metaMessages=i.metaMessages,this.shortMessage=n}};function Yr(t,n){return t.exec(n)?.groups}const jp=/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/,Wp=/^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/,Vp=/^\(.+?\).*?$/,Z1=/^tuple(?<array>(\[(\d*)\])*)$/;function Vc(t){let n=t.type;if(Z1.test(t.type)&&"components"in t){n="(";const i=t.components.length;for(let s=0;s<i;s++){const u=t.components[s];n+=Vc(u),s<i-1&&(n+=", ")}const o=Yr(Z1,t.type);return n+=`)${o?.array??""}`,Vc({...t,type:n})}return"indexed"in t&&t.indexed&&(n=`${n} indexed`),t.name?`${n} ${t.name}`:n}function wa(t){let n="";const i=t.length;for(let o=0;o<i;o++){const s=t[o];n+=Vc(s),o!==i-1&&(n+=", ")}return n}function qc(t){return t.type==="function"?`function ${t.name}(${wa(t.inputs)})${t.stateMutability&&t.stateMutability!=="nonpayable"?` ${t.stateMutability}`:""}${t.outputs?.length?` returns (${wa(t.outputs)})`:""}`:t.type==="event"?`event ${t.name}(${wa(t.inputs)})`:t.type==="error"?`error ${t.name}(${wa(t.inputs)})`:t.type==="constructor"?`constructor(${wa(t.inputs)})${t.stateMutability==="payable"?" payable":""}`:t.type==="fallback"?`fallback() external${t.stateMutability==="payable"?" payable":""}`:"receive() external payable"}const qp=/^error (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\((?<parameters>.*?)\)$/;function k6(t){return qp.test(t)}function R6(t){return Yr(qp,t)}const Kp=/^event (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\((?<parameters>.*?)\)$/;function P6(t){return Kp.test(t)}function _6(t){return Yr(Kp,t)}const $p=/^function (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\((?<parameters>.*?)\)(?: (?<scope>external|public{1}))?(?: (?<stateMutability>pure|view|nonpayable|payable{1}))?(?: returns\s?\((?<returns>.*?)\))?$/;function I6(t){return $p.test(t)}function O6(t){return Yr($p,t)}const Gp=/^struct (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*) \{(?<properties>.*?)\}$/;function Af(t){return Gp.test(t)}function M6(t){return Yr(Gp,t)}const Zp=/^constructor\((?<parameters>.*?)\)(?:\s(?<stateMutability>payable{1}))?$/;function L6(t){return Zp.test(t)}function N6(t){return Yr(Zp,t)}const Yp=/^fallback\(\) external(?:\s(?<stateMutability>payable{1}))?$/;function T6(t){return Yp.test(t)}function z6(t){return Yr(Yp,t)}const H6=/^receive\(\) external payable$/;function U6(t){return H6.test(t)}const Q6=new Set(["indexed"]),Kc=new Set(["calldata","memory","storage"]);class j6 extends Mt{constructor({signature:n}){super("Failed to parse ABI item.",{details:`parseAbiItem(${JSON.stringify(n,null,2)})`,docsPath:"/api/human#parseabiitem-1"}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"InvalidAbiItemError"})}}class W6 extends Mt{constructor({type:n}){super("Unknown type.",{metaMessages:[`Type "${n}" is not a valid ABI type. Perhaps you forgot to include a struct signature?`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"UnknownTypeError"})}}class V6 extends Mt{constructor({type:n}){super("Unknown type.",{metaMessages:[`Type "${n}" is not a valid ABI type.`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"UnknownSolidityTypeError"})}}class q6 extends Mt{constructor({param:n}){super("Invalid ABI parameter.",{details:n}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"InvalidParameterError"})}}class K6 extends Mt{constructor({param:n,name:i}){super("Invalid ABI parameter.",{details:n,metaMessages:[`"${i}" is a protected Solidity keyword. More info: https://docs.soliditylang.org/en/latest/cheatsheet.html`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"SolidityProtectedKeywordError"})}}class $6 extends Mt{constructor({param:n,type:i,modifier:o}){super("Invalid ABI parameter.",{details:n,metaMessages:[`Modifier "${o}" not allowed${i?` in "${i}" type`:""}.`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"InvalidModifierError"})}}class G6 extends Mt{constructor({param:n,type:i,modifier:o}){super("Invalid ABI parameter.",{details:n,metaMessages:[`Modifier "${o}" not allowed${i?` in "${i}" type`:""}.`,`Data location can only be specified for array, struct, or mapping types, but "${o}" was given.`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"InvalidFunctionModifierError"})}}class Z6 extends Mt{constructor({abiParameter:n}){super("Invalid ABI parameter.",{details:JSON.stringify(n,null,2),metaMessages:["ABI parameter type is invalid."]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"InvalidAbiTypeParameterError"})}}class ki extends Mt{constructor({signature:n,type:i}){super(`Invalid ${i} signature.`,{details:n}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"InvalidSignatureError"})}}class Y6 extends Mt{constructor({signature:n}){super("Unknown signature.",{details:n}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"UnknownSignatureError"})}}class X6 extends Mt{constructor({signature:n}){super("Invalid struct signature.",{details:n,metaMessages:["No properties exist."]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"InvalidStructSignatureError"})}}class J6 extends Mt{constructor({type:n}){super("Circular reference detected.",{metaMessages:[`Struct "${n}" is a circular reference.`]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"CircularReferenceError"})}}class e5 extends Mt{constructor({current:n,depth:i}){super("Unbalanced parentheses.",{metaMessages:[`"${n.trim()}" has too many ${i>0?"opening":"closing"} parentheses.`],details:`Depth "${i}"`}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"InvalidParenthesisError"})}}function t5(t,n,i){let o="";if(i)for(const s of Object.entries(i)){if(!s)continue;let u="";for(const x of s[1])u+=`[${x.type}${x.name?`:${x.name}`:""}]`;o+=`(${s[0]}{${u}})`}return n?`${n}:${t}${o}`:t}const vc=new Map([["address",{type:"address"}],["bool",{type:"bool"}],["bytes",{type:"bytes"}],["bytes32",{type:"bytes32"}],["int",{type:"int256"}],["int256",{type:"int256"}],["string",{type:"string"}],["uint",{type:"uint256"}],["uint8",{type:"uint8"}],["uint16",{type:"uint16"}],["uint24",{type:"uint24"}],["uint32",{type:"uint32"}],["uint64",{type:"uint64"}],["uint96",{type:"uint96"}],["uint112",{type:"uint112"}],["uint160",{type:"uint160"}],["uint192",{type:"uint192"}],["uint256",{type:"uint256"}],["address owner",{type:"address",name:"owner"}],["address to",{type:"address",name:"to"}],["bool approved",{type:"bool",name:"approved"}],["bytes _data",{type:"bytes",name:"_data"}],["bytes data",{type:"bytes",name:"data"}],["bytes signature",{type:"bytes",name:"signature"}],["bytes32 hash",{type:"bytes32",name:"hash"}],["bytes32 r",{type:"bytes32",name:"r"}],["bytes32 root",{type:"bytes32",name:"root"}],["bytes32 s",{type:"bytes32",name:"s"}],["string name",{type:"string",name:"name"}],["string symbol",{type:"string",name:"symbol"}],["string tokenURI",{type:"string",name:"tokenURI"}],["uint tokenId",{type:"uint256",name:"tokenId"}],["uint8 v",{type:"uint8",name:"v"}],["uint256 balance",{type:"uint256",name:"balance"}],["uint256 tokenId",{type:"uint256",name:"tokenId"}],["uint256 value",{type:"uint256",name:"value"}],["event:address indexed from",{type:"address",name:"from",indexed:!0}],["event:address indexed to",{type:"address",name:"to",indexed:!0}],["event:uint indexed tokenId",{type:"uint256",name:"tokenId",indexed:!0}],["event:uint256 indexed tokenId",{type:"uint256",name:"tokenId",indexed:!0}]]);function $c(t,n={}){if(I6(t))return r5(t,n);if(P6(t))return n5(t,n);if(k6(t))return i5(t,n);if(L6(t))return a5(t,n);if(T6(t))return o5(t);if(U6(t))return{type:"receive",stateMutability:"payable"};throw new Y6({signature:t})}function r5(t,n={}){const i=O6(t);if(!i)throw new ki({signature:t,type:"function"});const o=dr(i.parameters),s=[],u=o.length;for(let c=0;c<u;c++)s.push(C0(o[c],{modifiers:Kc,structs:n,type:"function"}));const x=[];if(i.returns){const c=dr(i.returns),h=c.length;for(let f=0;f<h;f++)x.push(C0(c[f],{modifiers:Kc,structs:n,type:"function"}))}return{name:i.name,type:"function",stateMutability:i.stateMutability??"nonpayable",inputs:s,outputs:x}}function n5(t,n={}){const i=_6(t);if(!i)throw new ki({signature:t,type:"event"});const o=dr(i.parameters),s=[],u=o.length;for(let x=0;x<u;x++)s.push(C0(o[x],{modifiers:Q6,structs:n,type:"event"}));return{name:i.name,type:"event",inputs:s}}function i5(t,n={}){const i=R6(t);if(!i)throw new ki({signature:t,type:"error"});const o=dr(i.parameters),s=[],u=o.length;for(let x=0;x<u;x++)s.push(C0(o[x],{structs:n,type:"error"}));return{name:i.name,type:"error",inputs:s}}function a5(t,n={}){const i=N6(t);if(!i)throw new ki({signature:t,type:"constructor"});const o=dr(i.parameters),s=[],u=o.length;for(let x=0;x<u;x++)s.push(C0(o[x],{structs:n,type:"constructor"}));return{type:"constructor",stateMutability:i.stateMutability??"nonpayable",inputs:s}}function o5(t){const n=z6(t);if(!n)throw new ki({signature:t,type:"fallback"});return{type:"fallback",stateMutability:n.stateMutability??"nonpayable"}}const s5=/^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\[\d*?\])+?)?(?:\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/,l5=/^\((?<type>.+?)\)(?<array>(?:\[\d*?\])+?)?(?:\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/,u5=/^u?int$/;function C0(t,n){const i=t5(t,n?.type,n?.structs);if(vc.has(i))return vc.get(i);const o=Vp.test(t),s=Yr(o?l5:s5,t);if(!s)throw new q6({param:t});if(s.name&&f5(s.name))throw new K6({param:t,name:s.name});const u=s.name?{name:s.name}:{},x=s.modifier==="indexed"?{indexed:!0}:{},c=n?.structs??{};let h,f={};if(o){h="tuple";const m=dr(s.type),y=[],A=m.length;for(let w=0;w<A;w++)y.push(C0(m[w],{structs:c}));f={components:y}}else if(s.type in c)h="tuple",f={components:c[s.type]};else if(u5.test(s.type))h=`${s.type}256`;else if(h=s.type,n?.type!=="struct"&&!Xp(h))throw new V6({type:h});if(s.modifier){if(!n?.modifiers?.has?.(s.modifier))throw new $6({param:t,type:n?.type,modifier:s.modifier});if(Kc.has(s.modifier)&&!x5(h,!!s.array))throw new G6({param:t,type:n?.type,modifier:s.modifier})}const p={type:`${h}${s.array??""}`,...u,...x,...f};return vc.set(i,p),p}function dr(t,n=[],i="",o=0){const s=t.trim().length;for(let u=0;u<s;u++){const x=t[u],c=t.slice(u+1);switch(x){case",":return o===0?dr(c,[...n,i.trim()]):dr(c,n,`${i}${x}`,o);case"(":return dr(c,n,`${i}${x}`,o+1);case")":return dr(c,n,`${i}${x}`,o-1);default:return dr(c,n,`${i}${x}`,o)}}if(i==="")return n;if(o!==0)throw new e5({current:i,depth:o});return n.push(i.trim()),n}function Xp(t){return t==="address"||t==="bool"||t==="function"||t==="string"||jp.test(t)||Wp.test(t)}const c5=/^(?:after|alias|anonymous|apply|auto|byte|calldata|case|catch|constant|copyof|default|defined|error|event|external|false|final|function|immutable|implements|in|indexed|inline|internal|let|mapping|match|memory|mutable|null|of|override|partial|private|promise|public|pure|reference|relocatable|return|returns|sizeof|static|storage|struct|super|supports|switch|this|true|try|typedef|typeof|var|view|virtual)$/;function f5(t){return t==="address"||t==="bool"||t==="function"||t==="string"||t==="tuple"||jp.test(t)||Wp.test(t)||c5.test(t)}function x5(t,n){return n||t==="bytes"||t==="string"||t==="tuple"}function Jp(t){const n={},i=t.length;for(let x=0;x<i;x++){const c=t[x];if(!Af(c))continue;const h=M6(c);if(!h)throw new ki({signature:c,type:"struct"});const f=h.properties.split(";"),p=[],m=f.length;for(let y=0;y<m;y++){const w=f[y].trim();if(!w)continue;const g=C0(w,{type:"struct"});p.push(g)}if(!p.length)throw new X6({signature:c});n[h.name]=p}const o={},s=Object.entries(n),u=s.length;for(let x=0;x<u;x++){const[c,h]=s[x];o[c]=e2(h,n)}return o}const d5=/^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\[\d*?\])+?)?$/;function e2(t,n,i=new Set){const o=[],s=t.length;for(let u=0;u<s;u++){const x=t[u];if(Vp.test(x.type))o.push(x);else{const h=Yr(d5,x.type);if(!h?.type)throw new Z6({abiParameter:x});const{array:f,type:p}=h;if(p in n){if(i.has(p))throw new J6({type:p});o.push({...x,type:`tuple${f??""}`,components:e2(n[p]??[],n,new Set([...i,p]))})}else if(Xp(p))o.push(x);else throw new W6({type:p})}}return o}function z7(t){const n=Jp(t),i=[],o=t.length;for(let s=0;s<o;s++){const u=t[s];Af(u)||i.push($c(u,n))}return i}function Y1(t){let n;if(typeof t=="string")n=$c(t);else{const i=Jp(t),o=t.length;for(let s=0;s<o;s++){const u=t[s];if(!Af(u)){n=$c(u,i);break}}}if(!n)throw new j6({signature:t});return n}/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */function h5(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function X1(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function fl(t,...n){if(!h5(t))throw new Error("Uint8Array expected");if(n.length>0&&!n.includes(t.length))throw new Error("Uint8Array expected of length "+n+", got length="+t.length)}function J1(t,n=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(n&&t.finished)throw new Error("Hash#digest() has already been called")}function p5(t,n){fl(t);const i=n.outputLen;if(t.length<i)throw new Error("digestInto() expects output buffer of length at least "+i)}function v5(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function t2(...t){for(let n=0;n<t.length;n++)t[n].fill(0)}const m5=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function y5(t){return t<<24&**********|t<<8&16711680|t>>>8&65280|t>>>24&255}function g5(t){for(let n=0;n<t.length;n++)t[n]=y5(t[n]);return t}const eh=m5?t=>t:g5;function b5(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}function r2(t){return typeof t=="string"&&(t=b5(t)),fl(t),t}let A5=class{};function w5(t){const n=o=>t().update(r2(o)).digest(),i=t();return n.outputLen=i.outputLen,n.blockLen=i.blockLen,n.create=()=>t(),n}const E5="0.1.1";function C5(){return E5}class Ne extends Error{constructor(n,i={}){const o=(()=>{if(i.cause instanceof Ne){if(i.cause.details)return i.cause.details;if(i.cause.shortMessage)return i.cause.shortMessage}return i.cause&&"details"in i.cause&&typeof i.cause.details=="string"?i.cause.details:i.cause?.message?i.cause.message:i.details})(),s=i.cause instanceof Ne&&i.cause.docsPath||i.docsPath,x=`https://oxlib.sh${s??""}`,c=[n||"An error occurred.",...i.metaMessages?["",...i.metaMessages]:[],...o||s?["",o?`Details: ${o}`:void 0,s?`See: ${x}`:void 0]:[]].filter(h=>typeof h=="string").join(`
`);super(c,i.cause?{cause:i.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:`ox@${C5()}`}),this.cause=i.cause,this.details=o,this.docs=x,this.docsPath=s,this.shortMessage=n}walk(n){return n2(this,n)}}function n2(t,n){return n?.(t)?t:t&&typeof t=="object"&&"cause"in t&&t.cause?n2(t.cause,n):n?null:t}const B5="#__bigint";function S5(t,n,i){return JSON.stringify(t,(o,s)=>typeof s=="bigint"?s.toString()+B5:s,i)}function D5(t,n){if(rh(t)>n)throw new N5({givenSize:rh(t),maxSize:n})}const Hr={zero:48,nine:57,A:65,F:70,a:97,f:102};function th(t){if(t>=Hr.zero&&t<=Hr.nine)return t-Hr.zero;if(t>=Hr.A&&t<=Hr.F)return t-(Hr.A-10);if(t>=Hr.a&&t<=Hr.f)return t-(Hr.a-10)}function F5(t,n={}){const{dir:i,size:o=32}=n;if(o===0)return t;if(t.length>o)throw new T5({size:t.length,targetSize:o,type:"Bytes"});const s=new Uint8Array(o);for(let u=0;u<o;u++){const x=i==="right";s[x?u:o-u-1]=t[x?u:t.length-u-1]}return s}function wf(t,n){if(Yt(t)>n)throw new j5({givenSize:Yt(t),maxSize:n})}function k5(t,n){if(typeof n=="number"&&n>0&&n>Yt(t)-1)throw new l2({offset:n,position:"start",size:Yt(t)})}function R5(t,n,i){if(typeof n=="number"&&typeof i=="number"&&Yt(t)!==i-n)throw new l2({offset:i,position:"end",size:Yt(t)})}function i2(t,n={}){const{dir:i,size:o=32}=n;if(o===0)return t;const s=t.replace("0x","");if(s.length>o*2)throw new W5({size:Math.ceil(s.length/2),targetSize:o,type:"Hex"});return`0x${s[i==="right"?"padEnd":"padStart"](o*2,"0")}`}const P5=new TextEncoder;function _5(t){return t instanceof Uint8Array?t:typeof t=="string"?O5(t):I5(t)}function I5(t){return t instanceof Uint8Array?t:new Uint8Array(t)}function O5(t,n={}){const{size:i}=n;let o=t;i&&(wf(t,i),o=S0(t,i));let s=o.slice(2);s.length%2&&(s=`0${s}`);const u=s.length/2,x=new Uint8Array(u);for(let c=0,h=0;c<u;c++){const f=th(s.charCodeAt(h++)),p=th(s.charCodeAt(h++));if(f===void 0||p===void 0)throw new Ne(`Invalid byte sequence ("${s[h-2]}${s[h-1]}" in "${s}").`);x[c]=f*16+p}return x}function M5(t,n={}){const{size:i}=n,o=P5.encode(t);return typeof i=="number"?(D5(o,i),L5(o,i)):o}function L5(t,n){return F5(t,{dir:"right",size:n})}function rh(t){return t.length}let N5=class extends Ne{constructor({givenSize:n,maxSize:i}){super(`Size cannot exceed \`${i}\` bytes. Given size: \`${n}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeOverflowError"})}},T5=class extends Ne{constructor({size:n,targetSize:i,type:o}){super(`${o.charAt(0).toUpperCase()}${o.slice(1).toLowerCase()} size (\`${n}\`) exceeds padding size (\`${i}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeExceedsPaddingSizeError"})}};const z5=new TextEncoder,H5=Array.from({length:256},(t,n)=>n.toString(16).padStart(2,"0"));function U5(t,n={}){const{strict:i=!1}=n;if(!t)throw new nh(t);if(typeof t!="string")throw new nh(t);if(i&&!/^0x[0-9a-fA-F]*$/.test(t))throw new ih(t);if(!t.startsWith("0x"))throw new ih(t)}function Fr(...t){return`0x${t.reduce((n,i)=>n+i.replace("0x",""),"")}`}function a2(t,n={}){const i=`0x${Number(t)}`;return typeof n.size=="number"?(wf(i,n.size),B0(i,n.size)):i}function o2(t,n={}){let i="";for(let s=0;s<t.length;s++)i+=H5[t[s]];const o=`0x${i}`;return typeof n.size=="number"?(wf(o,n.size),S0(o,n.size)):o}function bt(t,n={}){const{signed:i,size:o}=n,s=BigInt(t);let u;o?i?u=(1n<<BigInt(o)*8n-1n)-1n:u=2n**(BigInt(o)*8n)-1n:typeof t=="number"&&(u=BigInt(Number.MAX_SAFE_INTEGER));const x=typeof u=="bigint"&&i?-u-1n:0;if(u&&s>u||s<x){const f=typeof t=="bigint"?"n":"";throw new s2({max:u?`${u}${f}`:void 0,min:`${x}${f}`,signed:i,size:o,value:`${t}${f}`})}const h=`0x${(i&&s<0?(1n<<BigInt(o*8))+BigInt(s):s).toString(16)}`;return o?B0(h,o):h}function Ef(t,n={}){return o2(z5.encode(t),n)}function B0(t,n){return i2(t,{dir:"left",size:n})}function S0(t,n){return i2(t,{dir:"right",size:n})}function Cf(t,n,i,o={}){const{strict:s}=o;k5(t,n);const u=`0x${t.replace("0x","").slice((n??0)*2,(i??t.length)*2)}`;return s&&R5(u,n,i),u}function Yt(t){return Math.ceil((t.length-2)/2)}function Q5(t,n={}){const{strict:i=!1}=n;try{return U5(t,{strict:i}),!0}catch{return!1}}class s2 extends Ne{constructor({max:n,min:i,signed:o,size:s,value:u}){super(`Number \`${u}\` is not in safe${s?` ${s*8}-bit`:""}${o?" signed":" unsigned"} integer range ${n?`(\`${i}\` to \`${n}\`)`:`(above \`${i}\`)`}`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.IntegerOutOfRangeError"})}}class nh extends Ne{constructor(n){super(`Value \`${typeof n=="object"?S5(n):n}\` of type \`${typeof n}\` is an invalid hex type.`,{metaMessages:['Hex types must be represented as `"0x${string}"`.']}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.InvalidHexTypeError"})}}class ih extends Ne{constructor(n){super(`Value \`${n}\` is an invalid hex value.`,{metaMessages:['Hex values must start with `"0x"` and contain only hexadecimal characters (0-9, a-f, A-F).']}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.InvalidHexValueError"})}}class j5 extends Ne{constructor({givenSize:n,maxSize:i}){super(`Size cannot exceed \`${i}\` bytes. Given size: \`${n}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeOverflowError"})}}class l2 extends Ne{constructor({offset:n,position:i,size:o}){super(`Slice ${i==="start"?"starting":"ending"} at offset \`${n}\` is out-of-bounds (size: \`${o}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SliceOffsetOutOfBoundsError"})}}class W5 extends Ne{constructor({size:n,targetSize:i,type:o}){super(`${o.charAt(0).toUpperCase()}${o.slice(1).toLowerCase()} size (\`${n}\`) exceeds padding size (\`${i}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeExceedsPaddingSizeError"})}}function V5(t){return{address:t.address,amount:bt(t.amount),index:bt(t.index),validatorIndex:bt(t.validatorIndex)}}function j7(t){return{...typeof t.baseFeePerGas=="bigint"&&{baseFeePerGas:bt(t.baseFeePerGas)},...typeof t.blobBaseFee=="bigint"&&{blobBaseFee:bt(t.blobBaseFee)},...typeof t.feeRecipient=="string"&&{feeRecipient:t.feeRecipient},...typeof t.gasLimit=="bigint"&&{gasLimit:bt(t.gasLimit)},...typeof t.number=="bigint"&&{number:bt(t.number)},...typeof t.prevRandao=="bigint"&&{prevRandao:bt(t.prevRandao)},...typeof t.time=="bigint"&&{time:bt(t.time)},...t.withdrawals&&{withdrawals:t.withdrawals.map(V5)}}}const fs=BigInt(2**32-1),ah=BigInt(32);function q5(t,n=!1){return n?{h:Number(t&fs),l:Number(t>>ah&fs)}:{h:Number(t>>ah&fs)|0,l:Number(t&fs)|0}}function K5(t,n=!1){const i=t.length;let o=new Uint32Array(i),s=new Uint32Array(i);for(let u=0;u<i;u++){const{h:x,l:c}=q5(t[u],n);[o[u],s[u]]=[x,c]}return[o,s]}const $5=(t,n,i)=>t<<i|n>>>32-i,G5=(t,n,i)=>n<<i|t>>>32-i,Z5=(t,n,i)=>n<<i-32|t>>>64-i,Y5=(t,n,i)=>t<<i-32|n>>>64-i,X5=BigInt(0),Ea=BigInt(1),J5=BigInt(2),ev=BigInt(7),tv=BigInt(256),rv=BigInt(113),u2=[],c2=[],f2=[];for(let t=0,n=Ea,i=1,o=0;t<24;t++){[i,o]=[o,(2*i+3*o)%5],u2.push(2*(5*o+i)),c2.push((t+1)*(t+2)/2%64);let s=X5;for(let u=0;u<7;u++)n=(n<<Ea^(n>>ev)*rv)%tv,n&J5&&(s^=Ea<<(Ea<<BigInt(u))-Ea);f2.push(s)}const x2=K5(f2,!0),nv=x2[0],iv=x2[1],oh=(t,n,i)=>i>32?Z5(t,n,i):$5(t,n,i),sh=(t,n,i)=>i>32?Y5(t,n,i):G5(t,n,i);function av(t,n=24){const i=new Uint32Array(10);for(let o=24-n;o<24;o++){for(let x=0;x<10;x++)i[x]=t[x]^t[x+10]^t[x+20]^t[x+30]^t[x+40];for(let x=0;x<10;x+=2){const c=(x+8)%10,h=(x+2)%10,f=i[h],p=i[h+1],m=oh(f,p,1)^i[c],y=sh(f,p,1)^i[c+1];for(let A=0;A<50;A+=10)t[x+A]^=m,t[x+A+1]^=y}let s=t[2],u=t[3];for(let x=0;x<24;x++){const c=c2[x],h=oh(s,u,c),f=sh(s,u,c),p=u2[x];s=t[p],u=t[p+1],t[p]=h,t[p+1]=f}for(let x=0;x<50;x+=10){for(let c=0;c<10;c++)i[c]=t[x+c];for(let c=0;c<10;c++)t[x+c]^=~i[(c+2)%10]&i[(c+4)%10]}t[0]^=nv[o],t[1]^=iv[o]}t2(i)}let ov=class d2 extends A5{constructor(n,i,o,s=!1,u=24){if(super(),this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,this.enableXOF=!1,this.blockLen=n,this.suffix=i,this.outputLen=o,this.enableXOF=s,this.rounds=u,X1(o),!(0<n&&n<200))throw new Error("only keccak-f1600 function is supported");this.state=new Uint8Array(200),this.state32=v5(this.state)}clone(){return this._cloneInto()}keccak(){eh(this.state32),av(this.state32,this.rounds),eh(this.state32),this.posOut=0,this.pos=0}update(n){J1(this),n=r2(n),fl(n);const{blockLen:i,state:o}=this,s=n.length;for(let u=0;u<s;){const x=Math.min(i-this.pos,s-u);for(let c=0;c<x;c++)o[this.pos++]^=n[u++];this.pos===i&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:n,suffix:i,pos:o,blockLen:s}=this;n[o]^=i,(i&128)!==0&&o===s-1&&this.keccak(),n[s-1]^=128,this.keccak()}writeInto(n){J1(this,!1),fl(n),this.finish();const i=this.state,{blockLen:o}=this;for(let s=0,u=n.length;s<u;){this.posOut>=o&&this.keccak();const x=Math.min(o-this.posOut,u-s);n.set(i.subarray(this.posOut,this.posOut+x),s),this.posOut+=x,s+=x}return n}xofInto(n){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(n)}xof(n){return X1(n),this.xofInto(new Uint8Array(n))}digestInto(n){if(p5(n,this),this.finished)throw new Error("digest() was already called");return this.writeInto(n),this.destroy(),n}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,t2(this.state)}_cloneInto(n){const{blockLen:i,suffix:o,outputLen:s,rounds:u,enableXOF:x}=this;return n||(n=new d2(i,o,s,x,u)),n.state32.set(this.state32),n.pos=this.pos,n.posOut=this.posOut,n.finished=this.finished,n.rounds=u,n.suffix=o,n.outputLen=s,n.enableXOF=x,n.destroyed=this.destroyed,n}};const sv=(t,n,i)=>w5(()=>new ov(n,t,i)),lv=sv(1,136,256/8);function h2(t,n={}){const{as:i=typeof t=="string"?"Hex":"Bytes"}=n,o=lv(_5(t));return i==="Bytes"?o:o2(o)}class uv extends Map{constructor(n){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=n}get(n){const i=super.get(n);return super.has(n)&&i!==void 0&&(this.delete(n),super.set(n,i)),i}set(n,i){if(super.set(n,i),this.maxSize&&this.size>this.maxSize){const o=this.keys().next().value;o&&this.delete(o)}return this}}const cv={checksum:new uv(8192)},mc=cv.checksum,fv=/^0x[a-fA-F0-9]{40}$/;function gl(t,n={}){const{strict:i=!0}=n;if(!fv.test(t))throw new lh({address:t,cause:new dv});if(i){if(t.toLowerCase()===t)return;if(xv(t)!==t)throw new lh({address:t,cause:new hv})}}function xv(t){if(mc.has(t))return mc.get(t);gl(t,{strict:!1});const n=t.substring(2).toLowerCase(),i=h2(M5(n),{as:"Bytes"}),o=n.split("");for(let u=0;u<40;u+=2)i[u>>1]>>4>=8&&o[u]&&(o[u]=o[u].toUpperCase()),(i[u>>1]&15)>=8&&o[u+1]&&(o[u+1]=o[u+1].toUpperCase());const s=`0x${o.join("")}`;return mc.set(t,s),s}function Gc(t,n={}){const{strict:i=!0}=n??{};try{return gl(t,{strict:i}),!0}catch{return!1}}class lh extends Ne{constructor({address:n,cause:i}){super(`Address "${n}" is invalid.`,{cause:i}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidAddressError"})}}class dv extends Ne{constructor(){super("Address is not a 20 byte (40 hexadecimal character) value."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidInputError"})}}class hv extends Ne{constructor(){super("Address does not match its checksum counterpart."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidChecksumError"})}}function Zc(t){let n=!0,i="",o=0,s="",u=!1;for(let x=0;x<t.length;x++){const c=t[x];if(["(",")",","].includes(c)&&(n=!0),c==="("&&o++,c===")"&&o--,!!n){if(o===0){if(c===" "&&["event","function","error",""].includes(s))s="";else if(s+=c,c===")"){u=!0;break}continue}if(c===" "){t[x-1]!==","&&i!==","&&i!==",("&&(i="",n=!1);continue}s+=c,i+=c}}if(!u)throw new Ne("Unable to normalize signature.");return s}function Yc(t,n){const i=typeof t,o=n.type;switch(o){case"address":return Gc(t,{strict:!1});case"bool":return i==="boolean";case"function":return i==="string";case"string":return i==="string";default:return o==="tuple"&&"components"in n?Object.values(n.components).every((s,u)=>Yc(Object.values(t)[u],s)):/^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/.test(o)?i==="number"||i==="bigint":/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/.test(o)?i==="string"||t instanceof Uint8Array:/[a-z]+[1-9]{0,3}(\[[0-9]{0,}\])+$/.test(o)?Array.isArray(t)&&t.every(s=>Yc(s,{...n,type:o.replace(/(\[[0-9]{0,}\])$/,"")})):!1}}function p2(t,n,i){for(const o in t){const s=t[o],u=n[o];if(s.type==="tuple"&&u.type==="tuple"&&"components"in s&&"components"in u)return p2(s.components,u.components,i[o]);const x=[s.type,u.type];if(x.includes("address")&&x.includes("bytes20")?!0:x.includes("address")&&x.includes("string")?Gc(i[o],{strict:!1}):x.includes("address")&&x.includes("bytes")?Gc(i[o],{strict:!1}):!1)return x}}function v2(t,n={}){const{prepare:i=!0}=n,o=Array.isArray(t)||typeof t=="string"?Y1(t):t;return{...o,...i?{hash:oi(o)}:{}}}function pv(t,n,i){const{args:o=[],prepare:s=!0}=i??{},u=Q5(n,{strict:!1}),x=t.filter(f=>u?f.type==="function"||f.type==="error"?m2(f)===Cf(n,0,4):f.type==="event"?oi(f)===n:!1:"name"in f&&f.name===n);if(x.length===0)throw new Xc({name:n});if(x.length===1)return{...x[0],...s?{hash:oi(x[0])}:{}};let c;for(const f of x){if(!("inputs"in f))continue;if(!o||o.length===0){if(!f.inputs||f.inputs.length===0)return{...f,...s?{hash:oi(f)}:{}};continue}if(!f.inputs||f.inputs.length===0||f.inputs.length!==o.length)continue;if(o.every((m,y)=>{const A="inputs"in f&&f.inputs[y];return A?Yc(m,A):!1})){if(c&&"inputs"in c&&c.inputs){const m=p2(f.inputs,c.inputs,o);if(m)throw new mv({abiItem:f,type:m[0]},{abiItem:c,type:m[1]})}c=f}}const h=(()=>{if(c)return c;const[f,...p]=x;return{...f,overloads:p}})();if(!h)throw new Xc({name:n});return{...h,...s?{hash:oi(h)}:{}}}function m2(t){return Cf(oi(t),0,4)}function vv(t){const n=typeof t=="string"?t:qc(t);return Zc(n)}function oi(t){return typeof t!="string"&&"hash"in t&&t.hash?t.hash:h2(Ef(vv(t)))}class mv extends Ne{constructor(n,i){super("Found ambiguous types in overloaded ABI Items.",{metaMessages:[`\`${n.type}\` in \`${Zc(qc(n.abiItem))}\`, and`,`\`${i.type}\` in \`${Zc(qc(i.abiItem))}\``,"","These types encode differently and cannot be distinguished at runtime.","Remove one of the ambiguous items in the ABI."]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.AmbiguityError"})}}class Xc extends Ne{constructor({name:n,data:i,type:o="item"}){const s=n?` with name "${n}"`:i?` with data "${i}"`:"";super(`ABI ${o}${s} not found.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.NotFoundError"})}}const yv=/^(.*)\[([0-9]*)\]$/,gv=/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/,y2=/^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;function bv({checksumAddress:t,parameters:n,values:i}){const o=[];for(let s=0;s<n.length;s++)o.push(Bf({checksumAddress:t,parameter:n[s],value:i[s]}));return o}function Bf({checksumAddress:t=!1,parameter:n,value:i}){const o=n,s=Fv(o.type);if(s){const[u,x]=s;return wv(i,{checksumAddress:t,length:u,parameter:{...o,type:x}})}if(o.type==="tuple")return Dv(i,{checksumAddress:t,parameter:o});if(o.type==="address")return Av(i,{checksum:t});if(o.type==="bool")return Cv(i);if(o.type.startsWith("uint")||o.type.startsWith("int")){const u=o.type.startsWith("int"),[,,x="256"]=y2.exec(o.type)??[];return Bv(i,{signed:u,size:Number(x)})}if(o.type.startsWith("bytes"))return Ev(i,{type:o.type});if(o.type==="string")return Sv(i);throw new w2(o.type)}function Sf(t){let n=0;for(let u=0;u<t.length;u++){const{dynamic:x,encoded:c}=t[u];x?n+=32:n+=Yt(c)}const i=[],o=[];let s=0;for(let u=0;u<t.length;u++){const{dynamic:x,encoded:c}=t[u];x?(i.push(bt(n+s,{size:32})),o.push(c),s+=Yt(c)):i.push(c)}return Fr(...i,...o)}function Av(t,n){const{checksum:i=!1}=n;return gl(t,{strict:i}),{dynamic:!1,encoded:B0(t.toLowerCase())}}function wv(t,n){const{checksumAddress:i,length:o,parameter:s}=n,u=o===null;if(!Array.isArray(t))throw new Rv(t);if(!u&&t.length!==o)throw new kv({expectedLength:o,givenLength:t.length,type:`${s.type}[${o}]`});let x=!1;const c=[];for(let h=0;h<t.length;h++){const f=Bf({checksumAddress:i,parameter:s,value:t[h]});f.dynamic&&(x=!0),c.push(f)}if(u||x){const h=Sf(c);if(u){const f=bt(c.length,{size:32});return{dynamic:!0,encoded:c.length>0?Fr(f,h):f}}if(x)return{dynamic:!0,encoded:h}}return{dynamic:!1,encoded:Fr(...c.map(({encoded:h})=>h))}}function Ev(t,{type:n}){const[,i]=n.split("bytes"),o=Yt(t);if(!i){let s=t;return o%32!==0&&(s=S0(s,Math.ceil((t.length-2)/2/32)*32)),{dynamic:!0,encoded:Fr(B0(bt(o,{size:32})),s)}}if(o!==Number.parseInt(i))throw new b2({expectedSize:Number.parseInt(i),value:t});return{dynamic:!1,encoded:S0(t)}}function Cv(t){if(typeof t!="boolean")throw new Ne(`Invalid boolean value: "${t}" (type: ${typeof t}). Expected: \`true\` or \`false\`.`);return{dynamic:!1,encoded:B0(a2(t))}}function Bv(t,{signed:n,size:i}){if(typeof i=="number"){const o=2n**(BigInt(i)-(n?1n:0n))-1n,s=n?-o-1n:0n;if(t>o||t<s)throw new s2({max:o.toString(),min:s.toString(),signed:n,size:i/8,value:t.toString()})}return{dynamic:!1,encoded:bt(t,{size:32,signed:n})}}function Sv(t){const n=Ef(t),i=Math.ceil(Yt(n)/32),o=[];for(let s=0;s<i;s++)o.push(S0(Cf(n,s*32,(s+1)*32)));return{dynamic:!0,encoded:Fr(S0(bt(Yt(n),{size:32})),...o)}}function Dv(t,n){const{checksumAddress:i,parameter:o}=n;let s=!1;const u=[];for(let x=0;x<o.components.length;x++){const c=o.components[x],h=Array.isArray(t)?x:c.name,f=Bf({checksumAddress:i,parameter:c,value:t[h]});u.push(f),f.dynamic&&(s=!0)}return{dynamic:s,encoded:s?Sf(u):Fr(...u.map(({encoded:x})=>x))}}function Fv(t){const n=t.match(/^(.*)\[(\d+)?\]$/);return n?[n[2]?Number(n[2]):null,n[1]]:void 0}function g2(t,n,i){const{checksumAddress:o=!1}={};if(t.length!==n.length)throw new A2({expectedLength:t.length,givenLength:n.length});const s=bv({checksumAddress:o,parameters:t,values:n}),u=Sf(s);return u.length===0?"0x":u}function Jc(t,n){if(t.length!==n.length)throw new A2({expectedLength:t.length,givenLength:n.length});const i=[];for(let o=0;o<t.length;o++){const s=t[o],u=n[o];i.push(Jc.encode(s,u))}return Fr(...i)}(function(t){function n(i,o,s=!1){if(i==="address"){const h=o;return gl(h),B0(h.toLowerCase(),s?32:0)}if(i==="string")return Ef(o);if(i==="bytes")return o;if(i==="bool")return B0(a2(o),s?32:1);const u=i.match(y2);if(u){const[h,f,p="256"]=u,m=Number.parseInt(p)/8;return bt(o,{size:s?32:m,signed:f==="int"})}const x=i.match(gv);if(x){const[h,f]=x;if(Number.parseInt(f)!==(o.length-2)/2)throw new b2({expectedSize:Number.parseInt(f),value:o});return S0(o,s?32:0)}const c=i.match(yv);if(c&&Array.isArray(o)){const[h,f]=c,p=[];for(let m=0;m<o.length;m++)p.push(n(f,o[m],!0));return p.length===0?"0x":Fr(...p)}throw new w2(i)}t.encode=n})(Jc||(Jc={}));class kv extends Ne{constructor({expectedLength:n,givenLength:i,type:o}){super(`Array length mismatch for type \`${o}\`. Expected: \`${n}\`. Given: \`${i}\`.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.ArrayLengthMismatchError"})}}class b2 extends Ne{constructor({expectedSize:n,value:i}){super(`Size of bytes "${i}" (bytes${Yt(i)}) does not match expected size (bytes${n}).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.BytesSizeMismatchError"})}}class A2 extends Ne{constructor({expectedLength:n,givenLength:i}){super(["ABI encoding parameters/values length mismatch.",`Expected length (parameters): ${n}`,`Given length (values): ${i}`].join(`
`)),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.LengthMismatchError"})}}class Rv extends Ne{constructor(n){super(`Value \`${n}\` is not a valid array.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.InvalidArrayError"})}}class w2 extends Ne{constructor(n){super(`Type \`${n}\` is not a valid ABI Type.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.InvalidTypeError"})}}function W7(t,n){const{bytecode:i,args:o}=n;return Fr(i,t.inputs?.length&&o?.length?g2(t.inputs,o):"0x")}function V7(t){return v2(t)}function q7(t,...n){const{overloads:i}=t,o=i?Pv([t,...i],t.name,{args:n[0]}):t,s=_v(o),u=n.length>0?g2(o.inputs,n[0]):void 0;return u?Fr(s,u):s}function K7(t,n={}){return v2(t,n)}function Pv(t,n,i){const o=pv(t,n,i);if(o.type!=="function")throw new Xc({name:n,type:"function"});return o}function _v(t){return m2(t)}function Iv(t){if(typeof window>"u")return;const n=i=>t(i.detail);return window.addEventListener("eip6963:announceProvider",n),window.dispatchEvent(new CustomEvent("eip6963:requestProvider")),()=>window.removeEventListener("eip6963:announceProvider",n)}function $7(){const t=new Set;let n=[];const i=()=>Iv(s=>{n.some(({info:u})=>u.uuid===s.info.uuid)||(n=[...n,s],t.forEach(u=>u(n,{added:[s]})))});let o=i();return{_listeners(){return t},clear(){t.forEach(s=>s([],{removed:[...n]})),n=[]},destroy(){this.clear(),t.clear(),o?.()},findProvider({rdns:s}){return n.find(u=>u.info.rdns===s)},getProviders(){return n},reset(){this.clear(),o?.(),o=i()},subscribe(s,{emitImmediately:u}={}){return t.add(s),u&&s(n,{added:n}),()=>t.delete(s)}}}const Ov=t=>(n,i,o)=>{const s=o.subscribe;return o.subscribe=(x,c,h)=>{let f=x;if(c){const p=h?.equalityFn||Object.is;let m=x(o.getState());f=y=>{const A=x(y);if(!p(m,A)){const w=m;c(m=A,w)}},h?.fireImmediately&&c(m,m)}return s(f)},t(n,i,o)},G7=Ov;function Mv(t,n){let i;try{i=t()}catch{return}return{getItem:s=>{var u;const x=h=>h===null?null:JSON.parse(h,void 0),c=(u=i.getItem(s))!=null?u:null;return c instanceof Promise?c.then(x):x(c)},setItem:(s,u)=>i.setItem(s,JSON.stringify(u,void 0)),removeItem:s=>i.removeItem(s)}}const ef=t=>n=>{try{const i=t(n);return i instanceof Promise?i:{then(o){return ef(o)(i)},catch(o){return this}}}catch(i){return{then(o){return this},catch(o){return ef(o)(i)}}}},Lv=(t,n)=>(i,o,s)=>{let u={storage:Mv(()=>localStorage),partialize:g=>g,version:0,merge:(g,b)=>({...b,...g}),...n},x=!1;const c=new Set,h=new Set;let f=u.storage;if(!f)return t((...g)=>{console.warn(`[zustand persist middleware] Unable to update item '${u.name}', the given storage is currently unavailable.`),i(...g)},o,s);const p=()=>{const g=u.partialize({...o()});return f.setItem(u.name,{state:g,version:u.version})},m=s.setState;s.setState=(g,b)=>{m(g,b),p()};const y=t((...g)=>{i(...g),p()},o,s);s.getInitialState=()=>y;let A;const w=()=>{var g,b;if(!f)return;x=!1,c.forEach(B=>{var E;return B((E=o())!=null?E:y)});const S=((b=u.onRehydrateStorage)==null?void 0:b.call(u,(g=o())!=null?g:y))||void 0;return ef(f.getItem.bind(f))(u.name).then(B=>{if(B)if(typeof B.version=="number"&&B.version!==u.version){if(u.migrate)return[!0,u.migrate(B.state,B.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,B.state];return[!1,void 0]}).then(B=>{var E;const[D,k]=B;if(A=u.merge(k,(E=o())!=null?E:y),i(A,!0),D)return p()}).then(()=>{S?.(A,void 0),A=o(),x=!0,h.forEach(B=>B(A))}).catch(B=>{S?.(void 0,B)})};return s.persist={setOptions:g=>{u={...u,...g},g.storage&&(f=g.storage)},clearStorage:()=>{f?.removeItem(u.name)},getOptions:()=>u,rehydrate:()=>w(),hasHydrated:()=>x,onHydrate:g=>(c.add(g),()=>{c.delete(g)}),onFinishHydration:g=>(h.add(g),()=>{h.delete(g)})},u.skipHydration||w(),A||y},Z7=Lv,uh=t=>{let n;const i=new Set,o=(f,p)=>{const m=typeof f=="function"?f(n):f;if(!Object.is(m,n)){const y=n;n=p??(typeof m!="object"||m===null)?m:Object.assign({},n,m),i.forEach(A=>A(n,y))}},s=()=>n,c={setState:o,getState:s,getInitialState:()=>h,subscribe:f=>(i.add(f),()=>i.delete(f))},h=n=t(o,s,c);return c},Y7=t=>t?uh(t):uh;var yc={exports:{}},ch;function Nv(){return ch||(ch=1,function(t){var n=Object.prototype.hasOwnProperty,i="~";function o(){}Object.create&&(o.prototype=Object.create(null),new o().__proto__||(i=!1));function s(h,f,p){this.fn=h,this.context=f,this.once=p||!1}function u(h,f,p,m,y){if(typeof p!="function")throw new TypeError("The listener must be a function");var A=new s(p,m||h,y),w=i?i+f:f;return h._events[w]?h._events[w].fn?h._events[w]=[h._events[w],A]:h._events[w].push(A):(h._events[w]=A,h._eventsCount++),h}function x(h,f){--h._eventsCount===0?h._events=new o:delete h._events[f]}function c(){this._events=new o,this._eventsCount=0}c.prototype.eventNames=function(){var f=[],p,m;if(this._eventsCount===0)return f;for(m in p=this._events)n.call(p,m)&&f.push(i?m.slice(1):m);return Object.getOwnPropertySymbols?f.concat(Object.getOwnPropertySymbols(p)):f},c.prototype.listeners=function(f){var p=i?i+f:f,m=this._events[p];if(!m)return[];if(m.fn)return[m.fn];for(var y=0,A=m.length,w=new Array(A);y<A;y++)w[y]=m[y].fn;return w},c.prototype.listenerCount=function(f){var p=i?i+f:f,m=this._events[p];return m?m.fn?1:m.length:0},c.prototype.emit=function(f,p,m,y,A,w){var g=i?i+f:f;if(!this._events[g])return!1;var b=this._events[g],S=arguments.length,B,E;if(b.fn){switch(b.once&&this.removeListener(f,b.fn,void 0,!0),S){case 1:return b.fn.call(b.context),!0;case 2:return b.fn.call(b.context,p),!0;case 3:return b.fn.call(b.context,p,m),!0;case 4:return b.fn.call(b.context,p,m,y),!0;case 5:return b.fn.call(b.context,p,m,y,A),!0;case 6:return b.fn.call(b.context,p,m,y,A,w),!0}for(E=1,B=new Array(S-1);E<S;E++)B[E-1]=arguments[E];b.fn.apply(b.context,B)}else{var D=b.length,k;for(E=0;E<D;E++)switch(b[E].once&&this.removeListener(f,b[E].fn,void 0,!0),S){case 1:b[E].fn.call(b[E].context);break;case 2:b[E].fn.call(b[E].context,p);break;case 3:b[E].fn.call(b[E].context,p,m);break;case 4:b[E].fn.call(b[E].context,p,m,y);break;default:if(!B)for(k=1,B=new Array(S-1);k<S;k++)B[k-1]=arguments[k];b[E].fn.apply(b[E].context,B)}}return!0},c.prototype.on=function(f,p,m){return u(this,f,p,m,!1)},c.prototype.once=function(f,p,m){return u(this,f,p,m,!0)},c.prototype.removeListener=function(f,p,m,y){var A=i?i+f:f;if(!this._events[A])return this;if(!p)return x(this,A),this;var w=this._events[A];if(w.fn)w.fn===p&&(!y||w.once)&&(!m||w.context===m)&&x(this,A);else{for(var g=0,b=[],S=w.length;g<S;g++)(w[g].fn!==p||y&&!w[g].once||m&&w[g].context!==m)&&b.push(w[g]);b.length?this._events[A]=b.length===1?b[0]:b:x(this,A)}return this},c.prototype.removeAllListeners=function(f){var p;return f?(p=i?i+f:f,this._events[p]&&x(this,p)):(this._events=new o,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=i,c.EventEmitter=c,t.exports=c}(yc)),yc.exports}var Tv=Nv();const X7=yl(Tv);var gc={exports:{}},bc={},Ac={exports:{}},wc={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fh;function zv(){if(fh)return wc;fh=1;var t=Wa();function n(m,y){return m===y&&(m!==0||1/m===1/y)||m!==m&&y!==y}var i=typeof Object.is=="function"?Object.is:n,o=t.useState,s=t.useEffect,u=t.useLayoutEffect,x=t.useDebugValue;function c(m,y){var A=y(),w=o({inst:{value:A,getSnapshot:y}}),g=w[0].inst,b=w[1];return u(function(){g.value=A,g.getSnapshot=y,h(g)&&b({inst:g})},[m,A,y]),s(function(){return h(g)&&b({inst:g}),m(function(){h(g)&&b({inst:g})})},[m]),x(A),A}function h(m){var y=m.getSnapshot;m=m.value;try{var A=y();return!i(m,A)}catch{return!0}}function f(m,y){return y()}var p=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?f:c;return wc.useSyncExternalStore=t.useSyncExternalStore!==void 0?t.useSyncExternalStore:p,wc}var xh;function Hv(){return xh||(xh=1,Ac.exports=zv()),Ac.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dh;function Uv(){if(dh)return bc;dh=1;var t=Wa(),n=Hv();function i(f,p){return f===p&&(f!==0||1/f===1/p)||f!==f&&p!==p}var o=typeof Object.is=="function"?Object.is:i,s=n.useSyncExternalStore,u=t.useRef,x=t.useEffect,c=t.useMemo,h=t.useDebugValue;return bc.useSyncExternalStoreWithSelector=function(f,p,m,y,A){var w=u(null);if(w.current===null){var g={hasValue:!1,value:null};w.current=g}else g=w.current;w=c(function(){function S(R){if(!B){if(B=!0,E=R,R=y(R),A!==void 0&&g.hasValue){var I=g.value;if(A(I,R))return D=I}return D=R}if(I=D,o(E,R))return I;var O=y(R);return A!==void 0&&A(I,O)?(E=R,I):(E=R,D=O)}var B=!1,E,D,k=m===void 0?null:m;return[function(){return S(p())},k===null?void 0:function(){return S(k())}]},[p,m,y,A]);var b=s(f,w[0],w[1]);return x(function(){g.hasValue=!0,g.value=b},[b]),h(b),b},bc}var hh;function Qv(){return hh||(hh=1,gc.exports=Uv()),gc.exports}var J7=Qv();/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var ph="popstate";function jv(t={}){function n(o,s){let{pathname:u,search:x,hash:c}=o.location;return tf("",{pathname:u,search:x,hash:c},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function i(o,s){return typeof s=="string"?s:Ia(s)}return Vv(n,i,null,t)}function Le(t,n){if(t===!1||t===null||typeof t>"u")throw new Error(n)}function Xt(t,n){if(!t){typeof console<"u"&&console.warn(n);try{throw new Error(n)}catch{}}}function Wv(){return Math.random().toString(36).substring(2,10)}function vh(t,n){return{usr:t.state,key:t.key,idx:n}}function tf(t,n,i=null,o){return{pathname:typeof t=="string"?t:t.pathname,search:"",hash:"",...typeof n=="string"?Ri(n):n,state:i,key:n&&n.key||o||Wv()}}function Ia({pathname:t="/",search:n="",hash:i=""}){return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),i&&i!=="#"&&(t+=i.charAt(0)==="#"?i:"#"+i),t}function Ri(t){let n={};if(t){let i=t.indexOf("#");i>=0&&(n.hash=t.substring(i),t=t.substring(0,i));let o=t.indexOf("?");o>=0&&(n.search=t.substring(o),t=t.substring(0,o)),t&&(n.pathname=t)}return n}function Vv(t,n,i,o={}){let{window:s=document.defaultView,v5Compat:u=!1}=o,x=s.history,c="POP",h=null,f=p();f==null&&(f=0,x.replaceState({...x.state,idx:f},""));function p(){return(x.state||{idx:null}).idx}function m(){c="POP";let b=p(),S=b==null?null:b-f;f=b,h&&h({action:c,location:g.location,delta:S})}function y(b,S){c="PUSH";let B=tf(g.location,b,S);f=p()+1;let E=vh(B,f),D=g.createHref(B);try{x.pushState(E,"",D)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;s.location.assign(D)}u&&h&&h({action:c,location:g.location,delta:1})}function A(b,S){c="REPLACE";let B=tf(g.location,b,S);f=p();let E=vh(B,f),D=g.createHref(B);x.replaceState(E,"",D),u&&h&&h({action:c,location:g.location,delta:0})}function w(b){return qv(b)}let g={get action(){return c},get location(){return t(s,x)},listen(b){if(h)throw new Error("A history only accepts one active listener");return s.addEventListener(ph,m),h=b,()=>{s.removeEventListener(ph,m),h=null}},createHref(b){return n(s,b)},createURL:w,encodeLocation(b){let S=w(b);return{pathname:S.pathname,search:S.search,hash:S.hash}},push:y,replace:A,go(b){return x.go(b)}};return g}function qv(t,n=!1){let i="http://localhost";typeof window<"u"&&(i=window.location.origin!=="null"?window.location.origin:window.location.href),Le(i,"No window.location.(origin|href) available to create URL");let o=typeof t=="string"?t:Ia(t);return o=o.replace(/ $/,"%20"),!n&&o.startsWith("//")&&(o=i+o),new URL(o,i)}function E2(t,n,i="/"){return Kv(t,n,i,!1)}function Kv(t,n,i,o){let s=typeof n=="string"?Ri(n):n,u=Gr(s.pathname||"/",i);if(u==null)return null;let x=C2(t);$v(x);let c=null;for(let h=0;c==null&&h<x.length;++h){let f=am(u);c=nm(x[h],f,o)}return c}function C2(t,n=[],i=[],o=""){let s=(u,x,c)=>{let h={relativePath:c===void 0?u.path||"":c,caseSensitive:u.caseSensitive===!0,childrenIndex:x,route:u};h.relativePath.startsWith("/")&&(Le(h.relativePath.startsWith(o),`Absolute route path "${h.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),h.relativePath=h.relativePath.slice(o.length));let f=Kr([o,h.relativePath]),p=i.concat(h);u.children&&u.children.length>0&&(Le(u.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${f}".`),C2(u.children,n,p,f)),!(u.path==null&&!u.index)&&n.push({path:f,score:tm(f,u.index),routesMeta:p})};return t.forEach((u,x)=>{if(u.path===""||!u.path?.includes("?"))s(u,x);else for(let c of B2(u.path))s(u,x,c)}),n}function B2(t){let n=t.split("/");if(n.length===0)return[];let[i,...o]=n,s=i.endsWith("?"),u=i.replace(/\?$/,"");if(o.length===0)return s?[u,""]:[u];let x=B2(o.join("/")),c=[];return c.push(...x.map(h=>h===""?u:[u,h].join("/"))),s&&c.push(...x),c.map(h=>t.startsWith("/")&&h===""?"/":h)}function $v(t){t.sort((n,i)=>n.score!==i.score?i.score-n.score:rm(n.routesMeta.map(o=>o.childrenIndex),i.routesMeta.map(o=>o.childrenIndex)))}var Gv=/^:[\w-]+$/,Zv=3,Yv=2,Xv=1,Jv=10,em=-2,mh=t=>t==="*";function tm(t,n){let i=t.split("/"),o=i.length;return i.some(mh)&&(o+=em),n&&(o+=Yv),i.filter(s=>!mh(s)).reduce((s,u)=>s+(Gv.test(u)?Zv:u===""?Xv:Jv),o)}function rm(t,n){return t.length===n.length&&t.slice(0,-1).every((o,s)=>o===n[s])?t[t.length-1]-n[n.length-1]:0}function nm(t,n,i=!1){let{routesMeta:o}=t,s={},u="/",x=[];for(let c=0;c<o.length;++c){let h=o[c],f=c===o.length-1,p=u==="/"?n:n.slice(u.length)||"/",m=xl({path:h.relativePath,caseSensitive:h.caseSensitive,end:f},p),y=h.route;if(!m&&f&&i&&!o[o.length-1].route.index&&(m=xl({path:h.relativePath,caseSensitive:h.caseSensitive,end:!1},p)),!m)return null;Object.assign(s,m.params),x.push({params:s,pathname:Kr([u,m.pathname]),pathnameBase:um(Kr([u,m.pathnameBase])),route:y}),m.pathnameBase!=="/"&&(u=Kr([u,m.pathnameBase]))}return x}function xl(t,n){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[i,o]=im(t.path,t.caseSensitive,t.end),s=n.match(i);if(!s)return null;let u=s[0],x=u.replace(/(.)\/+$/,"$1"),c=s.slice(1);return{params:o.reduce((f,{paramName:p,isOptional:m},y)=>{if(p==="*"){let w=c[y]||"";x=u.slice(0,u.length-w.length).replace(/(.)\/+$/,"$1")}const A=c[y];return m&&!A?f[p]=void 0:f[p]=(A||"").replace(/%2F/g,"/"),f},{}),pathname:u,pathnameBase:x,pattern:t}}function im(t,n=!1,i=!0){Xt(t==="*"||!t.endsWith("*")||t.endsWith("/*"),`Route path "${t}" will be treated as if it were "${t.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${t.replace(/\*$/,"/*")}".`);let o=[],s="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(x,c,h)=>(o.push({paramName:c,isOptional:h!=null}),h?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(o.push({paramName:"*"}),s+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):i?s+="\\/*$":t!==""&&t!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,n?void 0:"i"),o]}function am(t){try{return t.split("/").map(n=>decodeURIComponent(n).replace(/\//g,"%2F")).join("/")}catch(n){return Xt(!1,`The URL path "${t}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${n}).`),t}}function Gr(t,n){if(n==="/")return t;if(!t.toLowerCase().startsWith(n.toLowerCase()))return null;let i=n.endsWith("/")?n.length-1:n.length,o=t.charAt(i);return o&&o!=="/"?null:t.slice(i)||"/"}function om(t,n="/"){let{pathname:i,search:o="",hash:s=""}=typeof t=="string"?Ri(t):t;return{pathname:i?i.startsWith("/")?i:sm(i,n):n,search:cm(o),hash:fm(s)}}function sm(t,n){let i=n.replace(/\/+$/,"").split("/");return t.split("/").forEach(s=>{s===".."?i.length>1&&i.pop():s!=="."&&i.push(s)}),i.length>1?i.join("/"):"/"}function Ec(t,n,i,o){return`Cannot include a '${t}' character in a manually specified \`to.${n}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${i}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function lm(t){return t.filter((n,i)=>i===0||n.route.path&&n.route.path.length>0)}function Df(t){let n=lm(t);return n.map((i,o)=>o===n.length-1?i.pathname:i.pathnameBase)}function Ff(t,n,i,o=!1){let s;typeof t=="string"?s=Ri(t):(s={...t},Le(!s.pathname||!s.pathname.includes("?"),Ec("?","pathname","search",s)),Le(!s.pathname||!s.pathname.includes("#"),Ec("#","pathname","hash",s)),Le(!s.search||!s.search.includes("#"),Ec("#","search","hash",s)));let u=t===""||s.pathname==="",x=u?"/":s.pathname,c;if(x==null)c=i;else{let m=n.length-1;if(!o&&x.startsWith("..")){let y=x.split("/");for(;y[0]==="..";)y.shift(),m-=1;s.pathname=y.join("/")}c=m>=0?n[m]:"/"}let h=om(s,c),f=x&&x!=="/"&&x.endsWith("/"),p=(u||x===".")&&i.endsWith("/");return!h.pathname.endsWith("/")&&(f||p)&&(h.pathname+="/"),h}var Kr=t=>t.join("/").replace(/\/\/+/g,"/"),um=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),cm=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,fm=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function xm(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}var S2=["POST","PUT","PATCH","DELETE"];new Set(S2);var dm=["GET",...S2];new Set(dm);var Pi=U.createContext(null);Pi.displayName="DataRouter";var bl=U.createContext(null);bl.displayName="DataRouterState";U.createContext(!1);var D2=U.createContext({isTransitioning:!1});D2.displayName="ViewTransition";var hm=U.createContext(new Map);hm.displayName="Fetchers";var pm=U.createContext(null);pm.displayName="Await";var hr=U.createContext(null);hr.displayName="Navigation";var Va=U.createContext(null);Va.displayName="Location";var pr=U.createContext({outlet:null,matches:[],isDataRoute:!1});pr.displayName="Route";var kf=U.createContext(null);kf.displayName="RouteError";function vm(t,{relative:n}={}){Le(_i(),"useHref() may be used only in the context of a <Router> component.");let{basename:i,navigator:o}=U.useContext(hr),{hash:s,pathname:u,search:x}=qa(t,{relative:n}),c=u;return i!=="/"&&(c=u==="/"?i:Kr([i,u])),o.createHref({pathname:c,search:x,hash:s})}function _i(){return U.useContext(Va)!=null}function Xr(){return Le(_i(),"useLocation() may be used only in the context of a <Router> component."),U.useContext(Va).location}var F2="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function k2(t){U.useContext(hr).static||U.useLayoutEffect(t)}function Rf(){let{isDataRoute:t}=U.useContext(pr);return t?km():mm()}function mm(){Le(_i(),"useNavigate() may be used only in the context of a <Router> component.");let t=U.useContext(Pi),{basename:n,navigator:i}=U.useContext(hr),{matches:o}=U.useContext(pr),{pathname:s}=Xr(),u=JSON.stringify(Df(o)),x=U.useRef(!1);return k2(()=>{x.current=!0}),U.useCallback((h,f={})=>{if(Xt(x.current,F2),!x.current)return;if(typeof h=="number"){i.go(h);return}let p=Ff(h,JSON.parse(u),s,f.relative==="path");t==null&&n!=="/"&&(p.pathname=p.pathname==="/"?n:Kr([n,p.pathname])),(f.replace?i.replace:i.push)(p,f.state,f)},[n,i,u,s,t])}U.createContext(null);function eA(){let{matches:t}=U.useContext(pr),n=t[t.length-1];return n?n.params:{}}function qa(t,{relative:n}={}){let{matches:i}=U.useContext(pr),{pathname:o}=Xr(),s=JSON.stringify(Df(i));return U.useMemo(()=>Ff(t,JSON.parse(s),o,n==="path"),[t,s,o,n])}function ym(t,n){return R2(t,n)}function R2(t,n,i,o){Le(_i(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=U.useContext(hr),{matches:u}=U.useContext(pr),x=u[u.length-1],c=x?x.params:{},h=x?x.pathname:"/",f=x?x.pathnameBase:"/",p=x&&x.route;{let S=p&&p.path||"";P2(h,!p||S.endsWith("*")||S.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${h}" (under <Route path="${S}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${S}"> to <Route path="${S==="/"?"*":`${S}/*`}">.`)}let m=Xr(),y;if(n){let S=typeof n=="string"?Ri(n):n;Le(f==="/"||S.pathname?.startsWith(f),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${f}" but pathname "${S.pathname}" was given in the \`location\` prop.`),y=S}else y=m;let A=y.pathname||"/",w=A;if(f!=="/"){let S=f.replace(/^\//,"").split("/");w="/"+A.replace(/^\//,"").split("/").slice(S.length).join("/")}let g=E2(t,{pathname:w});Xt(p||g!=null,`No routes matched location "${y.pathname}${y.search}${y.hash}" `),Xt(g==null||g[g.length-1].route.element!==void 0||g[g.length-1].route.Component!==void 0||g[g.length-1].route.lazy!==void 0,`Matched leaf route at location "${y.pathname}${y.search}${y.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let b=Em(g&&g.map(S=>Object.assign({},S,{params:Object.assign({},c,S.params),pathname:Kr([f,s.encodeLocation?s.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?f:Kr([f,s.encodeLocation?s.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),u,i,o);return n&&b?U.createElement(Va.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...y},navigationType:"POP"}},b):b}function gm(){let t=Fm(),n=xm(t)?`${t.status} ${t.statusText}`:t instanceof Error?t.message:JSON.stringify(t),i=t instanceof Error?t.stack:null,o="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:o},u={padding:"2px 4px",backgroundColor:o},x=null;return console.error("Error handled by React Router default ErrorBoundary:",t),x=U.createElement(U.Fragment,null,U.createElement("p",null,"💿 Hey developer 👋"),U.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",U.createElement("code",{style:u},"ErrorBoundary")," or"," ",U.createElement("code",{style:u},"errorElement")," prop on your route.")),U.createElement(U.Fragment,null,U.createElement("h2",null,"Unexpected Application Error!"),U.createElement("h3",{style:{fontStyle:"italic"}},n),i?U.createElement("pre",{style:s},i):null,x)}var bm=U.createElement(gm,null),Am=class extends U.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?U.createElement(pr.Provider,{value:this.props.routeContext},U.createElement(kf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function wm({routeContext:t,match:n,children:i}){let o=U.useContext(Pi);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),U.createElement(pr.Provider,{value:t},i)}function Em(t,n=[],i=null,o=null){if(t==null){if(!i)return null;if(i.errors)t=i.matches;else if(n.length===0&&!i.initialized&&i.matches.length>0)t=i.matches;else return null}let s=t,u=i?.errors;if(u!=null){let h=s.findIndex(f=>f.route.id&&u?.[f.route.id]!==void 0);Le(h>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(u).join(",")}`),s=s.slice(0,Math.min(s.length,h+1))}let x=!1,c=-1;if(i)for(let h=0;h<s.length;h++){let f=s[h];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(c=h),f.route.id){let{loaderData:p,errors:m}=i,y=f.route.loader&&!p.hasOwnProperty(f.route.id)&&(!m||m[f.route.id]===void 0);if(f.route.lazy||y){x=!0,c>=0?s=s.slice(0,c+1):s=[s[0]];break}}}return s.reduceRight((h,f,p)=>{let m,y=!1,A=null,w=null;i&&(m=u&&f.route.id?u[f.route.id]:void 0,A=f.route.errorElement||bm,x&&(c<0&&p===0?(P2("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),y=!0,w=null):c===p&&(y=!0,w=f.route.hydrateFallbackElement||null)));let g=n.concat(s.slice(0,p+1)),b=()=>{let S;return m?S=A:y?S=w:f.route.Component?S=U.createElement(f.route.Component,null):f.route.element?S=f.route.element:S=h,U.createElement(wm,{match:f,routeContext:{outlet:h,matches:g,isDataRoute:i!=null},children:S})};return i&&(f.route.ErrorBoundary||f.route.errorElement||p===0)?U.createElement(Am,{location:i.location,revalidation:i.revalidation,component:A,error:m,children:b(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):b()},null)}function Pf(t){return`${t} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Cm(t){let n=U.useContext(Pi);return Le(n,Pf(t)),n}function Bm(t){let n=U.useContext(bl);return Le(n,Pf(t)),n}function Sm(t){let n=U.useContext(pr);return Le(n,Pf(t)),n}function _f(t){let n=Sm(t),i=n.matches[n.matches.length-1];return Le(i.route.id,`${t} can only be used on routes that contain a unique "id"`),i.route.id}function Dm(){return _f("useRouteId")}function Fm(){let t=U.useContext(kf),n=Bm("useRouteError"),i=_f("useRouteError");return t!==void 0?t:n.errors?.[i]}function km(){let{router:t}=Cm("useNavigate"),n=_f("useNavigate"),i=U.useRef(!1);return k2(()=>{i.current=!0}),U.useCallback(async(s,u={})=>{Xt(i.current,F2),i.current&&(typeof s=="number"?t.navigate(s):await t.navigate(s,{fromRouteId:n,...u}))},[t,n])}var yh={};function P2(t,n,i){!n&&!yh[t]&&(yh[t]=!0,Xt(!1,i))}U.memo(Rm);function Rm({routes:t,future:n,state:i}){return R2(t,void 0,i,n)}function tA({to:t,replace:n,state:i,relative:o}){Le(_i(),"<Navigate> may be used only in the context of a <Router> component.");let{static:s}=U.useContext(hr);Xt(!s,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:u}=U.useContext(pr),{pathname:x}=Xr(),c=Rf(),h=Ff(t,Df(u),x,o==="path"),f=JSON.stringify(h);return U.useEffect(()=>{c(JSON.parse(f),{replace:n,state:i,relative:o})},[c,f,o,n,i]),null}function Pm(t){Le(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function _m({basename:t="/",children:n=null,location:i,navigationType:o="POP",navigator:s,static:u=!1}){Le(!_i(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let x=t.replace(/^\/*/,"/"),c=U.useMemo(()=>({basename:x,navigator:s,static:u,future:{}}),[x,s,u]);typeof i=="string"&&(i=Ri(i));let{pathname:h="/",search:f="",hash:p="",state:m=null,key:y="default"}=i,A=U.useMemo(()=>{let w=Gr(h,x);return w==null?null:{location:{pathname:w,search:f,hash:p,state:m,key:y},navigationType:o}},[x,h,f,p,m,y,o]);return Xt(A!=null,`<Router basename="${x}"> is not able to match the URL "${h}${f}${p}" because it does not start with the basename, so the <Router> won't render anything.`),A==null?null:U.createElement(hr.Provider,{value:c},U.createElement(Va.Provider,{children:n,value:A}))}function rA({children:t,location:n}){return ym(rf(t),n)}function rf(t,n=[]){let i=[];return U.Children.forEach(t,(o,s)=>{if(!U.isValidElement(o))return;let u=[...n,s];if(o.type===U.Fragment){i.push.apply(i,rf(o.props.children,u));return}Le(o.type===Pm,`[${typeof o.type=="string"?o.type:o.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Le(!o.props.index||!o.props.children,"An index route cannot have child routes.");let x={id:o.props.id||u.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,hydrateFallbackElement:o.props.hydrateFallbackElement,HydrateFallback:o.props.HydrateFallback,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.hasErrorBoundary===!0||o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(x.children=rf(o.props.children,u)),i.push(x)}),i}var bs="get",As="application/x-www-form-urlencoded";function Al(t){return t!=null&&typeof t.tagName=="string"}function Im(t){return Al(t)&&t.tagName.toLowerCase()==="button"}function Om(t){return Al(t)&&t.tagName.toLowerCase()==="form"}function Mm(t){return Al(t)&&t.tagName.toLowerCase()==="input"}function Lm(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function Nm(t,n){return t.button===0&&(!n||n==="_self")&&!Lm(t)}function nf(t=""){return new URLSearchParams(typeof t=="string"||Array.isArray(t)||t instanceof URLSearchParams?t:Object.keys(t).reduce((n,i)=>{let o=t[i];return n.concat(Array.isArray(o)?o.map(s=>[i,s]):[[i,o]])},[]))}function Tm(t,n){let i=nf(t);return n&&n.forEach((o,s)=>{i.has(s)||n.getAll(s).forEach(u=>{i.append(s,u)})}),i}var xs=null;function zm(){if(xs===null)try{new FormData(document.createElement("form"),0),xs=!1}catch{xs=!0}return xs}var Hm=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Cc(t){return t!=null&&!Hm.has(t)?(Xt(!1,`"${t}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${As}"`),null):t}function Um(t,n){let i,o,s,u,x;if(Om(t)){let c=t.getAttribute("action");o=c?Gr(c,n):null,i=t.getAttribute("method")||bs,s=Cc(t.getAttribute("enctype"))||As,u=new FormData(t)}else if(Im(t)||Mm(t)&&(t.type==="submit"||t.type==="image")){let c=t.form;if(c==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let h=t.getAttribute("formaction")||c.getAttribute("action");if(o=h?Gr(h,n):null,i=t.getAttribute("formmethod")||c.getAttribute("method")||bs,s=Cc(t.getAttribute("formenctype"))||Cc(c.getAttribute("enctype"))||As,u=new FormData(c,t),!zm()){let{name:f,type:p,value:m}=t;if(p==="image"){let y=f?`${f}.`:"";u.append(`${y}x`,"0"),u.append(`${y}y`,"0")}else f&&u.append(f,m)}}else{if(Al(t))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');i=bs,o=null,s=As,x=t}return u&&s==="text/plain"&&(x=u,u=void 0),{action:o,method:i.toLowerCase(),encType:s,formData:u,body:x}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function If(t,n){if(t===!1||t===null||typeof t>"u")throw new Error(n)}function Qm(t,n,i){let o=typeof t=="string"?new URL(t,typeof window>"u"?"server://singlefetch/":window.location.origin):t;return o.pathname==="/"?o.pathname=`_root.${i}`:n&&Gr(o.pathname,n)==="/"?o.pathname=`${n.replace(/\/$/,"")}/_root.${i}`:o.pathname=`${o.pathname.replace(/\/$/,"")}.${i}`,o}async function jm(t,n){if(t.id in n)return n[t.id];try{let i=await import(t.module);return n[t.id]=i,i}catch(i){return console.error(`Error loading route module \`${t.module}\`, reloading page...`),console.error(i),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Wm(t){return t==null?!1:t.href==null?t.rel==="preload"&&typeof t.imageSrcSet=="string"&&typeof t.imageSizes=="string":typeof t.rel=="string"&&typeof t.href=="string"}async function Vm(t,n,i){let o=await Promise.all(t.map(async s=>{let u=n.routes[s.route.id];if(u){let x=await jm(u,i);return x.links?x.links():[]}return[]}));return Gm(o.flat(1).filter(Wm).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function gh(t,n,i,o,s,u){let x=(h,f)=>i[f]?h.route.id!==i[f].route.id:!0,c=(h,f)=>i[f].pathname!==h.pathname||i[f].route.path?.endsWith("*")&&i[f].params["*"]!==h.params["*"];return u==="assets"?n.filter((h,f)=>x(h,f)||c(h,f)):u==="data"?n.filter((h,f)=>{let p=o.routes[h.route.id];if(!p||!p.hasLoader)return!1;if(x(h,f)||c(h,f))return!0;if(h.route.shouldRevalidate){let m=h.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:i[0]?.params||{},nextUrl:new URL(t,window.origin),nextParams:h.params,defaultShouldRevalidate:!0});if(typeof m=="boolean")return m}return!0}):[]}function qm(t,n,{includeHydrateFallback:i}={}){return Km(t.map(o=>{let s=n.routes[o.route.id];if(!s)return[];let u=[s.module];return s.clientActionModule&&(u=u.concat(s.clientActionModule)),s.clientLoaderModule&&(u=u.concat(s.clientLoaderModule)),i&&s.hydrateFallbackModule&&(u=u.concat(s.hydrateFallbackModule)),s.imports&&(u=u.concat(s.imports)),u}).flat(1))}function Km(t){return[...new Set(t)]}function $m(t){let n={},i=Object.keys(t).sort();for(let o of i)n[o]=t[o];return n}function Gm(t,n){let i=new Set;return new Set(n),t.reduce((o,s)=>{let u=JSON.stringify($m(s));return i.has(u)||(i.add(u),o.push({key:u,link:s})),o},[])}function _2(){let t=U.useContext(Pi);return If(t,"You must render this element inside a <DataRouterContext.Provider> element"),t}function Zm(){let t=U.useContext(bl);return If(t,"You must render this element inside a <DataRouterStateContext.Provider> element"),t}var Of=U.createContext(void 0);Of.displayName="FrameworkContext";function I2(){let t=U.useContext(Of);return If(t,"You must render this element inside a <HydratedRouter> element"),t}function Ym(t,n){let i=U.useContext(Of),[o,s]=U.useState(!1),[u,x]=U.useState(!1),{onFocus:c,onBlur:h,onMouseEnter:f,onMouseLeave:p,onTouchStart:m}=n,y=U.useRef(null);U.useEffect(()=>{if(t==="render"&&x(!0),t==="viewport"){let g=S=>{S.forEach(B=>{x(B.isIntersecting)})},b=new IntersectionObserver(g,{threshold:.5});return y.current&&b.observe(y.current),()=>{b.disconnect()}}},[t]),U.useEffect(()=>{if(o){let g=setTimeout(()=>{x(!0)},100);return()=>{clearTimeout(g)}}},[o]);let A=()=>{s(!0)},w=()=>{s(!1),x(!1)};return i?t!=="intent"?[u,y,{}]:[u,y,{onFocus:Ca(c,A),onBlur:Ca(h,w),onMouseEnter:Ca(f,A),onMouseLeave:Ca(p,w),onTouchStart:Ca(m,A)}]:[!1,y,{}]}function Ca(t,n){return i=>{t&&t(i),i.defaultPrevented||n(i)}}function Xm({page:t,...n}){let{router:i}=_2(),o=U.useMemo(()=>E2(i.routes,t,i.basename),[i.routes,t,i.basename]);return o?U.createElement(ey,{page:t,matches:o,...n}):null}function Jm(t){let{manifest:n,routeModules:i}=I2(),[o,s]=U.useState([]);return U.useEffect(()=>{let u=!1;return Vm(t,n,i).then(x=>{u||s(x)}),()=>{u=!0}},[t,n,i]),o}function ey({page:t,matches:n,...i}){let o=Xr(),{manifest:s,routeModules:u}=I2(),{basename:x}=_2(),{loaderData:c,matches:h}=Zm(),f=U.useMemo(()=>gh(t,n,h,s,o,"data"),[t,n,h,s,o]),p=U.useMemo(()=>gh(t,n,h,s,o,"assets"),[t,n,h,s,o]),m=U.useMemo(()=>{if(t===o.pathname+o.search+o.hash)return[];let w=new Set,g=!1;if(n.forEach(S=>{let B=s.routes[S.route.id];!B||!B.hasLoader||(!f.some(E=>E.route.id===S.route.id)&&S.route.id in c&&u[S.route.id]?.shouldRevalidate||B.hasClientLoader?g=!0:w.add(S.route.id))}),w.size===0)return[];let b=Qm(t,x,"data");return g&&w.size>0&&b.searchParams.set("_routes",n.filter(S=>w.has(S.route.id)).map(S=>S.route.id).join(",")),[b.pathname+b.search]},[x,c,o,s,f,n,t,u]),y=U.useMemo(()=>qm(p,s),[p,s]),A=Jm(p);return U.createElement(U.Fragment,null,m.map(w=>U.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...i})),y.map(w=>U.createElement("link",{key:w,rel:"modulepreload",href:w,...i})),A.map(({key:w,link:g})=>U.createElement("link",{key:w,...g})))}function ty(...t){return n=>{t.forEach(i=>{typeof i=="function"?i(n):i!=null&&(i.current=n)})}}var O2=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{O2&&(window.__reactRouterVersion="7.7.1")}catch{}function nA({basename:t,children:n,window:i}){let o=U.useRef();o.current==null&&(o.current=jv({window:i,v5Compat:!0}));let s=o.current,[u,x]=U.useState({action:s.action,location:s.location}),c=U.useCallback(h=>{U.startTransition(()=>x(h))},[x]);return U.useLayoutEffect(()=>s.listen(c),[s,c]),U.createElement(_m,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s})}var M2=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,L2=U.forwardRef(function({onClick:n,discover:i="render",prefetch:o="none",relative:s,reloadDocument:u,replace:x,state:c,target:h,to:f,preventScrollReset:p,viewTransition:m,...y},A){let{basename:w}=U.useContext(hr),g=typeof f=="string"&&M2.test(f),b,S=!1;if(typeof f=="string"&&g&&(b=f,O2))try{let Q=new URL(window.location.href),_=f.startsWith("//")?new URL(Q.protocol+f):new URL(f),T=Gr(_.pathname,w);_.origin===Q.origin&&T!=null?f=T+_.search+_.hash:S=!0}catch{Xt(!1,`<Link to="${f}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let B=vm(f,{relative:s}),[E,D,k]=Ym(o,y),R=ay(f,{replace:x,state:c,target:h,preventScrollReset:p,relative:s,viewTransition:m});function I(Q){n&&n(Q),Q.defaultPrevented||R(Q)}let O=U.createElement("a",{...y,...k,href:b||B,onClick:S||u?n:I,ref:ty(A,D),target:h,"data-discover":!g&&i==="render"?"true":void 0});return E&&!g?U.createElement(U.Fragment,null,O,U.createElement(Xm,{page:B})):O});L2.displayName="Link";var ry=U.forwardRef(function({"aria-current":n="page",caseSensitive:i=!1,className:o="",end:s=!1,style:u,to:x,viewTransition:c,children:h,...f},p){let m=qa(x,{relative:f.relative}),y=Xr(),A=U.useContext(bl),{navigator:w,basename:g}=U.useContext(hr),b=A!=null&&cy(m)&&c===!0,S=w.encodeLocation?w.encodeLocation(m).pathname:m.pathname,B=y.pathname,E=A&&A.navigation&&A.navigation.location?A.navigation.location.pathname:null;i||(B=B.toLowerCase(),E=E?E.toLowerCase():null,S=S.toLowerCase()),E&&g&&(E=Gr(E,g)||E);const D=S!=="/"&&S.endsWith("/")?S.length-1:S.length;let k=B===S||!s&&B.startsWith(S)&&B.charAt(D)==="/",R=E!=null&&(E===S||!s&&E.startsWith(S)&&E.charAt(S.length)==="/"),I={isActive:k,isPending:R,isTransitioning:b},O=k?n:void 0,Q;typeof o=="function"?Q=o(I):Q=[o,k?"active":null,R?"pending":null,b?"transitioning":null].filter(Boolean).join(" ");let _=typeof u=="function"?u(I):u;return U.createElement(L2,{...f,"aria-current":O,className:Q,ref:p,style:_,to:x,viewTransition:c},typeof h=="function"?h(I):h)});ry.displayName="NavLink";var ny=U.forwardRef(({discover:t="render",fetcherKey:n,navigate:i,reloadDocument:o,replace:s,state:u,method:x=bs,action:c,onSubmit:h,relative:f,preventScrollReset:p,viewTransition:m,...y},A)=>{let w=ly(),g=uy(c,{relative:f}),b=x.toLowerCase()==="get"?"get":"post",S=typeof c=="string"&&M2.test(c),B=E=>{if(h&&h(E),E.defaultPrevented)return;E.preventDefault();let D=E.nativeEvent.submitter,k=D?.getAttribute("formmethod")||x;w(D||E.currentTarget,{fetcherKey:n,method:k,navigate:i,replace:s,state:u,relative:f,preventScrollReset:p,viewTransition:m})};return U.createElement("form",{ref:A,method:b,action:g,onSubmit:o?h:B,...y,"data-discover":!S&&t==="render"?"true":void 0})});ny.displayName="Form";function iy(t){return`${t} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function N2(t){let n=U.useContext(Pi);return Le(n,iy(t)),n}function ay(t,{target:n,replace:i,state:o,preventScrollReset:s,relative:u,viewTransition:x}={}){let c=Rf(),h=Xr(),f=qa(t,{relative:u});return U.useCallback(p=>{if(Nm(p,n)){p.preventDefault();let m=i!==void 0?i:Ia(h)===Ia(f);c(t,{replace:m,state:o,preventScrollReset:s,relative:u,viewTransition:x})}},[h,c,f,i,o,n,t,s,u,x])}function iA(t){Xt(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let n=U.useRef(nf(t)),i=U.useRef(!1),o=Xr(),s=U.useMemo(()=>Tm(o.search,i.current?null:n.current),[o.search]),u=Rf(),x=U.useCallback((c,h)=>{const f=nf(typeof c=="function"?c(new URLSearchParams(s)):c);i.current=!0,u("?"+f,h)},[u,s]);return[s,x]}var oy=0,sy=()=>`__${String(++oy)}__`;function ly(){let{router:t}=N2("useSubmit"),{basename:n}=U.useContext(hr),i=Dm();return U.useCallback(async(o,s={})=>{let{action:u,method:x,encType:c,formData:h,body:f}=Um(o,n);if(s.navigate===!1){let p=s.fetcherKey||sy();await t.fetch(p,i,s.action||u,{preventScrollReset:s.preventScrollReset,formData:h,body:f,formMethod:s.method||x,formEncType:s.encType||c,flushSync:s.flushSync})}else await t.navigate(s.action||u,{preventScrollReset:s.preventScrollReset,formData:h,body:f,formMethod:s.method||x,formEncType:s.encType||c,replace:s.replace,state:s.state,fromRouteId:i,flushSync:s.flushSync,viewTransition:s.viewTransition})},[t,n,i])}function uy(t,{relative:n}={}){let{basename:i}=U.useContext(hr),o=U.useContext(pr);Le(o,"useFormAction must be used inside a RouteContext");let[s]=o.matches.slice(-1),u={...qa(t||".",{relative:n})},x=Xr();if(t==null){u.search=x.search;let c=new URLSearchParams(u.search),h=c.getAll("index");if(h.some(p=>p==="")){c.delete("index"),h.filter(m=>m).forEach(m=>c.append("index",m));let p=c.toString();u.search=p?`?${p}`:""}}return(!t||t===".")&&s.route.index&&(u.search=u.search?u.search.replace(/^\?/,"?index&"):"?index"),i!=="/"&&(u.pathname=u.pathname==="/"?i:Kr([i,u.pathname])),Ia(u)}function cy(t,{relative:n}={}){let i=U.useContext(D2);Le(i!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=N2("useViewTransitionState"),s=qa(t,{relative:n});if(!i.isTransitioning)return!1;let u=Gr(i.currentLocation.pathname,o)||i.currentLocation.pathname,x=Gr(i.nextLocation.pathname,o)||i.nextLocation.pathname;return xl(s.pathname,x)!=null||xl(s.pathname,u)!=null}function Ot(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`Wrong positive integer: ${t}`)}function Mf(t,...n){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");if(n.length>0&&!n.includes(t.length))throw new Error(`Expected Uint8Array of length ${n}, not of length=${t.length}`)}function T2(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Ot(t.outputLen),Ot(t.blockLen)}function Ai(t,n=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(n&&t.finished)throw new Error("Hash#digest() has already been called")}function z2(t,n){Mf(t);const i=n.outputLen;if(t.length<i)throw new Error(`digestInto() expects output buffer of length at least ${i}`)}const Bc=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const H2=t=>t instanceof Uint8Array,ws=t=>new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4)),Es=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),Er=(t,n)=>t<<32-n|t>>>n,fy=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;if(!fy)throw new Error("Non little-endian hardware is not supported");const xy=async()=>{};async function bh(t,n,i){let o=Date.now();for(let s=0;s<t;s++){i(s);const u=Date.now()-o;u>=0&&u<n||(await xy(),o+=u)}}function dy(t){if(typeof t!="string")throw new Error(`utf8ToBytes expected string, got ${typeof t}`);return new Uint8Array(new TextEncoder().encode(t))}function wi(t){if(typeof t=="string"&&(t=dy(t)),!H2(t))throw new Error(`expected Uint8Array, got ${typeof t}`);return t}function hy(...t){const n=new Uint8Array(t.reduce((o,s)=>o+s.length,0));let i=0;return t.forEach(o=>{if(!H2(o))throw new Error("Uint8Array expected");n.set(o,i),i+=o.length}),n}class Lf{clone(){return this._cloneInto()}}const py={}.toString;function U2(t,n){if(n!==void 0&&py.call(n)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(t,n)}function wl(t){const n=o=>t().update(wi(o)).digest(),i=t();return n.outputLen=i.outputLen,n.blockLen=i.blockLen,n.create=()=>t(),n}function vy(t=32){if(Bc&&typeof Bc.getRandomValues=="function")return Bc.getRandomValues(new Uint8Array(t));throw new Error("crypto.getRandomValues must be defined")}class Q2 extends Lf{constructor(n,i){super(),this.finished=!1,this.destroyed=!1,T2(n);const o=wi(i);if(this.iHash=n.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const s=this.blockLen,u=new Uint8Array(s);u.set(o.length>s?n.create().update(o).digest():o);for(let x=0;x<u.length;x++)u[x]^=54;this.iHash.update(u),this.oHash=n.create();for(let x=0;x<u.length;x++)u[x]^=106;this.oHash.update(u),u.fill(0)}update(n){return Ai(this),this.iHash.update(n),this}digestInto(n){Ai(this),Mf(n,this.outputLen),this.finished=!0,this.iHash.digestInto(n),this.oHash.update(n),this.oHash.digestInto(n),this.destroy()}digest(){const n=new Uint8Array(this.oHash.outputLen);return this.digestInto(n),n}_cloneInto(n){n||(n=Object.create(Object.getPrototypeOf(this),{}));const{oHash:i,iHash:o,finished:s,destroyed:u,blockLen:x,outputLen:c}=this;return n=n,n.finished=s,n.destroyed=u,n.blockLen=x,n.outputLen=c,n.oHash=i._cloneInto(n.oHash),n.iHash=o._cloneInto(n.iHash),n}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Nf=(t,n,i)=>new Q2(t,n).update(i).digest();Nf.create=(t,n)=>new Q2(t,n);function my(t,n,i,o){T2(t);const s=U2({dkLen:32,asyncTick:10},o),{c:u,dkLen:x,asyncTick:c}=s;if(Ot(u),Ot(x),Ot(c),u<1)throw new Error("PBKDF2: iterations (c) should be >= 1");const h=wi(n),f=wi(i),p=new Uint8Array(x),m=Nf.create(t,h),y=m._cloneInto().update(f);return{c:u,dkLen:x,asyncTick:c,DK:p,PRF:m,PRFSalt:y}}function yy(t,n,i,o,s){return t.destroy(),n.destroy(),o&&o.destroy(),s.fill(0),i}function j2(t,n,i,o){const{c:s,dkLen:u,DK:x,PRF:c,PRFSalt:h}=my(t,n,i,o);let f;const p=new Uint8Array(4),m=Es(p),y=new Uint8Array(c.outputLen);for(let A=1,w=0;w<u;A++,w+=c.outputLen){const g=x.subarray(w,w+c.outputLen);m.setInt32(0,A,!1),(f=h._cloneInto(f)).update(p).digestInto(y),g.set(y.subarray(0,g.length));for(let b=1;b<s;b++){c._cloneInto(f).update(y).digestInto(y);for(let S=0;S<g.length;S++)g[S]^=y[S]}}return yy(c,h,x,f,y)}function gy(t,n,i,o){if(typeof t.setBigUint64=="function")return t.setBigUint64(n,i,o);const s=BigInt(32),u=BigInt(4294967295),x=Number(i>>s&u),c=Number(i&u),h=o?4:0,f=o?0:4;t.setUint32(n+h,x,o),t.setUint32(n+f,c,o)}class Tf extends Lf{constructor(n,i,o,s){super(),this.blockLen=n,this.outputLen=i,this.padOffset=o,this.isLE=s,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(n),this.view=Es(this.buffer)}update(n){Ai(this);const{view:i,buffer:o,blockLen:s}=this;n=wi(n);const u=n.length;for(let x=0;x<u;){const c=Math.min(s-this.pos,u-x);if(c===s){const h=Es(n);for(;s<=u-x;x+=s)this.process(h,x);continue}o.set(n.subarray(x,x+c),this.pos),this.pos+=c,x+=c,this.pos===s&&(this.process(i,0),this.pos=0)}return this.length+=n.length,this.roundClean(),this}digestInto(n){Ai(this),z2(n,this),this.finished=!0;const{buffer:i,view:o,blockLen:s,isLE:u}=this;let{pos:x}=this;i[x++]=128,this.buffer.subarray(x).fill(0),this.padOffset>s-x&&(this.process(o,0),x=0);for(let m=x;m<s;m++)i[m]=0;gy(o,s-8,BigInt(this.length*8),u),this.process(o,0);const c=Es(n),h=this.outputLen;if(h%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const f=h/4,p=this.get();if(f>p.length)throw new Error("_sha2: outputLen bigger than state");for(let m=0;m<f;m++)c.setUint32(4*m,p[m],u)}digest(){const{buffer:n,outputLen:i}=this;this.digestInto(n);const o=n.slice(0,i);return this.destroy(),o}_cloneInto(n){n||(n=new this.constructor),n.set(...this.get());const{blockLen:i,buffer:o,length:s,finished:u,destroyed:x,pos:c}=this;return n.length=s,n.pos=c,n.finished=u,n.destroyed=x,s%i&&n.buffer.set(o),n}}const by=(t,n,i)=>t&n^~t&i,Ay=(t,n,i)=>t&n^t&i^n&i,wy=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Cn=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Bn=new Uint32Array(64);class Ey extends Tf{constructor(){super(64,32,8,!1),this.A=Cn[0]|0,this.B=Cn[1]|0,this.C=Cn[2]|0,this.D=Cn[3]|0,this.E=Cn[4]|0,this.F=Cn[5]|0,this.G=Cn[6]|0,this.H=Cn[7]|0}get(){const{A:n,B:i,C:o,D:s,E:u,F:x,G:c,H:h}=this;return[n,i,o,s,u,x,c,h]}set(n,i,o,s,u,x,c,h){this.A=n|0,this.B=i|0,this.C=o|0,this.D=s|0,this.E=u|0,this.F=x|0,this.G=c|0,this.H=h|0}process(n,i){for(let m=0;m<16;m++,i+=4)Bn[m]=n.getUint32(i,!1);for(let m=16;m<64;m++){const y=Bn[m-15],A=Bn[m-2],w=Er(y,7)^Er(y,18)^y>>>3,g=Er(A,17)^Er(A,19)^A>>>10;Bn[m]=g+Bn[m-7]+w+Bn[m-16]|0}let{A:o,B:s,C:u,D:x,E:c,F:h,G:f,H:p}=this;for(let m=0;m<64;m++){const y=Er(c,6)^Er(c,11)^Er(c,25),A=p+y+by(c,h,f)+wy[m]+Bn[m]|0,g=(Er(o,2)^Er(o,13)^Er(o,22))+Ay(o,s,u)|0;p=f,f=h,h=c,c=x+A|0,x=u,u=s,s=o,o=A+g|0}o=o+this.A|0,s=s+this.B|0,u=u+this.C|0,x=x+this.D|0,c=c+this.E|0,h=h+this.F|0,f=f+this.G|0,p=p+this.H|0,this.set(o,s,u,x,c,h,f,p)}roundClean(){Bn.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const zf=wl(()=>new Ey),ds=BigInt(2**32-1),af=BigInt(32);function W2(t,n=!1){return n?{h:Number(t&ds),l:Number(t>>af&ds)}:{h:Number(t>>af&ds)|0,l:Number(t&ds)|0}}function V2(t,n=!1){let i=new Uint32Array(t.length),o=new Uint32Array(t.length);for(let s=0;s<t.length;s++){const{h:u,l:x}=W2(t[s],n);[i[s],o[s]]=[u,x]}return[i,o]}const Cy=(t,n)=>BigInt(t>>>0)<<af|BigInt(n>>>0),By=(t,n,i)=>t>>>i,Sy=(t,n,i)=>t<<32-i|n>>>i,Dy=(t,n,i)=>t>>>i|n<<32-i,Fy=(t,n,i)=>t<<32-i|n>>>i,ky=(t,n,i)=>t<<64-i|n>>>i-32,Ry=(t,n,i)=>t>>>i-32|n<<64-i,Py=(t,n)=>n,_y=(t,n)=>t,q2=(t,n,i)=>t<<i|n>>>32-i,K2=(t,n,i)=>n<<i|t>>>32-i,$2=(t,n,i)=>n<<i-32|t>>>64-i,G2=(t,n,i)=>t<<i-32|n>>>64-i;function Iy(t,n,i,o){const s=(n>>>0)+(o>>>0);return{h:t+i+(s/2**32|0)|0,l:s|0}}const Oy=(t,n,i)=>(t>>>0)+(n>>>0)+(i>>>0),My=(t,n,i,o)=>n+i+o+(t/2**32|0)|0,Ly=(t,n,i,o)=>(t>>>0)+(n>>>0)+(i>>>0)+(o>>>0),Ny=(t,n,i,o,s)=>n+i+o+s+(t/2**32|0)|0,Ty=(t,n,i,o,s)=>(t>>>0)+(n>>>0)+(i>>>0)+(o>>>0)+(s>>>0),zy=(t,n,i,o,s,u)=>n+i+o+s+u+(t/2**32|0)|0,Ae={fromBig:W2,split:V2,toBig:Cy,shrSH:By,shrSL:Sy,rotrSH:Dy,rotrSL:Fy,rotrBH:ky,rotrBL:Ry,rotr32H:Py,rotr32L:_y,rotlSH:q2,rotlSL:K2,rotlBH:$2,rotlBL:G2,add:Iy,add3L:Oy,add3H:My,add4L:Ly,add4H:Ny,add5H:zy,add5L:Ty},[Hy,Uy]=Ae.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(t=>BigInt(t))),Sn=new Uint32Array(80),Dn=new Uint32Array(80);class Qy extends Tf{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:n,Al:i,Bh:o,Bl:s,Ch:u,Cl:x,Dh:c,Dl:h,Eh:f,El:p,Fh:m,Fl:y,Gh:A,Gl:w,Hh:g,Hl:b}=this;return[n,i,o,s,u,x,c,h,f,p,m,y,A,w,g,b]}set(n,i,o,s,u,x,c,h,f,p,m,y,A,w,g,b){this.Ah=n|0,this.Al=i|0,this.Bh=o|0,this.Bl=s|0,this.Ch=u|0,this.Cl=x|0,this.Dh=c|0,this.Dl=h|0,this.Eh=f|0,this.El=p|0,this.Fh=m|0,this.Fl=y|0,this.Gh=A|0,this.Gl=w|0,this.Hh=g|0,this.Hl=b|0}process(n,i){for(let E=0;E<16;E++,i+=4)Sn[E]=n.getUint32(i),Dn[E]=n.getUint32(i+=4);for(let E=16;E<80;E++){const D=Sn[E-15]|0,k=Dn[E-15]|0,R=Ae.rotrSH(D,k,1)^Ae.rotrSH(D,k,8)^Ae.shrSH(D,k,7),I=Ae.rotrSL(D,k,1)^Ae.rotrSL(D,k,8)^Ae.shrSL(D,k,7),O=Sn[E-2]|0,Q=Dn[E-2]|0,_=Ae.rotrSH(O,Q,19)^Ae.rotrBH(O,Q,61)^Ae.shrSH(O,Q,6),T=Ae.rotrSL(O,Q,19)^Ae.rotrBL(O,Q,61)^Ae.shrSL(O,Q,6),q=Ae.add4L(I,T,Dn[E-7],Dn[E-16]),$=Ae.add4H(q,R,_,Sn[E-7],Sn[E-16]);Sn[E]=$|0,Dn[E]=q|0}let{Ah:o,Al:s,Bh:u,Bl:x,Ch:c,Cl:h,Dh:f,Dl:p,Eh:m,El:y,Fh:A,Fl:w,Gh:g,Gl:b,Hh:S,Hl:B}=this;for(let E=0;E<80;E++){const D=Ae.rotrSH(m,y,14)^Ae.rotrSH(m,y,18)^Ae.rotrBH(m,y,41),k=Ae.rotrSL(m,y,14)^Ae.rotrSL(m,y,18)^Ae.rotrBL(m,y,41),R=m&A^~m&g,I=y&w^~y&b,O=Ae.add5L(B,k,I,Uy[E],Dn[E]),Q=Ae.add5H(O,S,D,R,Hy[E],Sn[E]),_=O|0,T=Ae.rotrSH(o,s,28)^Ae.rotrBH(o,s,34)^Ae.rotrBH(o,s,39),q=Ae.rotrSL(o,s,28)^Ae.rotrBL(o,s,34)^Ae.rotrBL(o,s,39),$=o&u^o&c^u&c,ae=s&x^s&h^x&h;S=g|0,B=b|0,g=A|0,b=w|0,A=m|0,w=y|0,{h:m,l:y}=Ae.add(f|0,p|0,Q|0,_|0),f=c|0,p=h|0,c=u|0,h=x|0,u=o|0,x=s|0;const K=Ae.add3L(_,q,ae);o=Ae.add3H(K,Q,T,$),s=K|0}({h:o,l:s}=Ae.add(this.Ah|0,this.Al|0,o|0,s|0)),{h:u,l:x}=Ae.add(this.Bh|0,this.Bl|0,u|0,x|0),{h:c,l:h}=Ae.add(this.Ch|0,this.Cl|0,c|0,h|0),{h:f,l:p}=Ae.add(this.Dh|0,this.Dl|0,f|0,p|0),{h:m,l:y}=Ae.add(this.Eh|0,this.El|0,m|0,y|0),{h:A,l:w}=Ae.add(this.Fh|0,this.Fl|0,A|0,w|0),{h:g,l:b}=Ae.add(this.Gh|0,this.Gl|0,g|0,b|0),{h:S,l:B}=Ae.add(this.Hh|0,this.Hl|0,S|0,B|0),this.set(o,s,u,x,c,h,f,p,m,y,A,w,g,b,S,B)}roundClean(){Sn.fill(0),Dn.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}const aA=wl(()=>new Qy),[Z2,Y2,X2]=[[],[],[]],jy=BigInt(0),Ba=BigInt(1),Wy=BigInt(2),Vy=BigInt(7),qy=BigInt(256),Ky=BigInt(113);for(let t=0,n=Ba,i=1,o=0;t<24;t++){[i,o]=[o,(2*i+3*o)%5],Z2.push(2*(5*o+i)),Y2.push((t+1)*(t+2)/2%64);let s=jy;for(let u=0;u<7;u++)n=(n<<Ba^(n>>Vy)*Ky)%qy,n&Wy&&(s^=Ba<<(Ba<<BigInt(u))-Ba);X2.push(s)}const[$y,Gy]=V2(X2,!0),Ah=(t,n,i)=>i>32?$2(t,n,i):q2(t,n,i),wh=(t,n,i)=>i>32?G2(t,n,i):K2(t,n,i);function Zy(t,n=24){const i=new Uint32Array(10);for(let o=24-n;o<24;o++){for(let x=0;x<10;x++)i[x]=t[x]^t[x+10]^t[x+20]^t[x+30]^t[x+40];for(let x=0;x<10;x+=2){const c=(x+8)%10,h=(x+2)%10,f=i[h],p=i[h+1],m=Ah(f,p,1)^i[c],y=wh(f,p,1)^i[c+1];for(let A=0;A<50;A+=10)t[x+A]^=m,t[x+A+1]^=y}let s=t[2],u=t[3];for(let x=0;x<24;x++){const c=Y2[x],h=Ah(s,u,c),f=wh(s,u,c),p=Z2[x];s=t[p],u=t[p+1],t[p]=h,t[p+1]=f}for(let x=0;x<50;x+=10){for(let c=0;c<10;c++)i[c]=t[x+c];for(let c=0;c<10;c++)t[x+c]^=~i[(c+2)%10]&i[(c+4)%10]}t[0]^=$y[o],t[1]^=Gy[o]}i.fill(0)}class Hf extends Lf{constructor(n,i,o,s=!1,u=24){if(super(),this.blockLen=n,this.suffix=i,this.outputLen=o,this.enableXOF=s,this.rounds=u,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,Ot(o),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=ws(this.state)}keccak(){Zy(this.state32,this.rounds),this.posOut=0,this.pos=0}update(n){Ai(this);const{blockLen:i,state:o}=this;n=wi(n);const s=n.length;for(let u=0;u<s;){const x=Math.min(i-this.pos,s-u);for(let c=0;c<x;c++)o[this.pos++]^=n[u++];this.pos===i&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:n,suffix:i,pos:o,blockLen:s}=this;n[o]^=i,(i&128)!==0&&o===s-1&&this.keccak(),n[s-1]^=128,this.keccak()}writeInto(n){Ai(this,!1),Mf(n),this.finish();const i=this.state,{blockLen:o}=this;for(let s=0,u=n.length;s<u;){this.posOut>=o&&this.keccak();const x=Math.min(o-this.posOut,u-s);n.set(i.subarray(this.posOut,this.posOut+x),s),this.posOut+=x,s+=x}return n}xofInto(n){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(n)}xof(n){return Ot(n),this.xofInto(new Uint8Array(n))}digestInto(n){if(z2(n,this),this.finished)throw new Error("digest() was already called");return this.writeInto(n),this.destroy(),n}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(n){const{blockLen:i,suffix:o,outputLen:s,rounds:u,enableXOF:x}=this;return n||(n=new Hf(i,o,s,x,u)),n.state32.set(this.state32),n.pos=this.pos,n.posOut=this.posOut,n.finished=this.finished,n.rounds=u,n.suffix=o,n.outputLen=s,n.enableXOF=x,n.destroyed=this.destroyed,n}}const Yy=(t,n,i)=>wl(()=>new Hf(n,t,i)),oA=Yy(1,136,256/8),Xy=new Uint8Array([7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8]),J2=Uint8Array.from({length:16},(t,n)=>n),Jy=J2.map(t=>(9*t+5)%16);let Uf=[J2],Qf=[Jy];for(let t=0;t<4;t++)for(let n of[Uf,Qf])n.push(n[t].map(i=>Xy[i]));const e4=[[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8],[12,13,11,15,6,9,9,7,12,15,11,13,7,8,7,7],[13,15,14,11,7,7,6,8,13,14,13,12,5,5,6,9],[14,11,12,14,8,6,5,5,15,12,15,14,9,9,8,6],[15,12,13,13,9,5,8,6,14,11,12,11,8,6,5,5]].map(t=>new Uint8Array(t)),e9=Uf.map((t,n)=>t.map(i=>e4[n][i])),t9=Qf.map((t,n)=>t.map(i=>e4[n][i])),r9=new Uint32Array([0,1518500249,1859775393,2400959708,2840853838]),n9=new Uint32Array([1352829926,1548603684,1836072691,2053994217,0]),hs=(t,n)=>t<<n|t>>>32-n;function Eh(t,n,i,o){return t===0?n^i^o:t===1?n&i|~n&o:t===2?(n|~i)^o:t===3?n&o|i&~o:n^(i|~o)}const ps=new Uint32Array(16);class i9 extends Tf{constructor(){super(64,20,8,!0),this.h0=1732584193,this.h1=-271733879,this.h2=-1732584194,this.h3=271733878,this.h4=-1009589776}get(){const{h0:n,h1:i,h2:o,h3:s,h4:u}=this;return[n,i,o,s,u]}set(n,i,o,s,u){this.h0=n|0,this.h1=i|0,this.h2=o|0,this.h3=s|0,this.h4=u|0}process(n,i){for(let A=0;A<16;A++,i+=4)ps[A]=n.getUint32(i,!0);let o=this.h0|0,s=o,u=this.h1|0,x=u,c=this.h2|0,h=c,f=this.h3|0,p=f,m=this.h4|0,y=m;for(let A=0;A<5;A++){const w=4-A,g=r9[A],b=n9[A],S=Uf[A],B=Qf[A],E=e9[A],D=t9[A];for(let k=0;k<16;k++){const R=hs(o+Eh(A,u,c,f)+ps[S[k]]+g,E[k])+m|0;o=m,m=f,f=hs(c,10)|0,c=u,u=R}for(let k=0;k<16;k++){const R=hs(s+Eh(w,x,h,p)+ps[B[k]]+b,D[k])+y|0;s=y,y=p,p=hs(h,10)|0,h=x,x=R}}this.set(this.h1+c+p|0,this.h2+f+y|0,this.h3+m+s|0,this.h4+o+x|0,this.h0+u+h|0)}roundClean(){ps.fill(0)}destroy(){this.destroyed=!0,this.buffer.fill(0),this.set(0,0,0,0,0)}}const sA=wl(()=>new i9),Fe=(t,n)=>t<<n|t>>>32-n;function Ch(t,n,i,o,s,u){let x=t[n++]^i[o++],c=t[n++]^i[o++],h=t[n++]^i[o++],f=t[n++]^i[o++],p=t[n++]^i[o++],m=t[n++]^i[o++],y=t[n++]^i[o++],A=t[n++]^i[o++],w=t[n++]^i[o++],g=t[n++]^i[o++],b=t[n++]^i[o++],S=t[n++]^i[o++],B=t[n++]^i[o++],E=t[n++]^i[o++],D=t[n++]^i[o++],k=t[n++]^i[o++],R=x,I=c,O=h,Q=f,_=p,T=m,q=y,$=A,ae=w,K=g,Y=b,ne=S,Z=B,J=E,X=D,F=k;for(let M=0;M<8;M+=2)_^=Fe(R+Z|0,7),ae^=Fe(_+R|0,9),Z^=Fe(ae+_|0,13),R^=Fe(Z+ae|0,18),K^=Fe(T+I|0,7),J^=Fe(K+T|0,9),I^=Fe(J+K|0,13),T^=Fe(I+J|0,18),X^=Fe(Y+q|0,7),O^=Fe(X+Y|0,9),q^=Fe(O+X|0,13),Y^=Fe(q+O|0,18),Q^=Fe(F+ne|0,7),$^=Fe(Q+F|0,9),ne^=Fe($+Q|0,13),F^=Fe(ne+$|0,18),I^=Fe(R+Q|0,7),O^=Fe(I+R|0,9),Q^=Fe(O+I|0,13),R^=Fe(Q+O|0,18),q^=Fe(T+_|0,7),$^=Fe(q+T|0,9),_^=Fe($+q|0,13),T^=Fe(_+$|0,18),ne^=Fe(Y+K|0,7),ae^=Fe(ne+Y|0,9),K^=Fe(ae+ne|0,13),Y^=Fe(K+ae|0,18),Z^=Fe(F+X|0,7),J^=Fe(Z+F|0,9),X^=Fe(J+Z|0,13),F^=Fe(X+J|0,18);s[u++]=x+R|0,s[u++]=c+I|0,s[u++]=h+O|0,s[u++]=f+Q|0,s[u++]=p+_|0,s[u++]=m+T|0,s[u++]=y+q|0,s[u++]=A+$|0,s[u++]=w+ae|0,s[u++]=g+K|0,s[u++]=b+Y|0,s[u++]=S+ne|0,s[u++]=B+Z|0,s[u++]=E+J|0,s[u++]=D+X|0,s[u++]=k+F|0}function si(t,n,i,o,s){let u=o+0,x=o+16*s;for(let c=0;c<16;c++)i[x+c]=t[n+(2*s-1)*16+c];for(let c=0;c<s;c++,u+=16,n+=16)Ch(i,x,t,n,i,u),c>0&&(x+=16),Ch(i,u,t,n+=16,i,x)}function t4(t,n,i){const o=U2({dkLen:32,asyncTick:10,maxmem:1073742848},i),{N:s,r:u,p:x,dkLen:c,asyncTick:h,maxmem:f,onProgress:p}=o;if(Ot(s),Ot(u),Ot(x),Ot(c),Ot(h),Ot(f),p!==void 0&&typeof p!="function")throw new Error("progressCb should be function");const m=128*u,y=m/4;if(s<=1||(s&s-1)!==0||s>=2**(m/8)||s>2**32)throw new Error("Scrypt: N must be larger than 1, a power of 2, less than 2^(128 * r / 8) and less than 2^32");if(x<0||x>(2**32-1)*32/m)throw new Error("Scrypt: p must be a positive integer less than or equal to ((2^32 - 1) * 32) / (128 * r)");if(c<0||c>(2**32-1)*32)throw new Error("Scrypt: dkLen should be positive integer less than or equal to (2^32 - 1) * 32");const A=m*(s+x);if(A>f)throw new Error(`Scrypt: parameters too large, ${A} (128 * r * (N + p)) > ${f} (maxmem)`);const w=j2(zf,t,n,{c:1,dkLen:m*x}),g=ws(w),b=ws(new Uint8Array(m*s)),S=ws(new Uint8Array(m));let B=()=>{};if(p){const E=2*s*x,D=Math.max(Math.floor(E/1e4),1);let k=0;B=()=>{k++,p&&(!(k%D)||k===E)&&p(k/E)}}return{N:s,r:u,p:x,dkLen:c,blockSize32:y,V:b,B32:g,B:w,tmp:S,blockMixCb:B,asyncTick:h}}function r4(t,n,i,o,s){const u=j2(zf,t,i,{c:1,dkLen:n});return i.fill(0),o.fill(0),s.fill(0),u}function lA(t,n,i){const{N:o,r:s,p:u,dkLen:x,blockSize32:c,V:h,B32:f,B:p,tmp:m,blockMixCb:y}=t4(t,n,i);for(let A=0;A<u;A++){const w=c*A;for(let g=0;g<c;g++)h[g]=f[w+g];for(let g=0,b=0;g<o-1;g++)si(h,b,h,b+=c,s),y();si(h,(o-1)*c,f,w,s),y();for(let g=0;g<o;g++){const b=f[w+c-16]%o;for(let S=0;S<c;S++)m[S]=f[w+S]^h[b*c+S];si(m,0,f,w,s),y()}}return r4(t,x,p,h,m)}async function uA(t,n,i){const{N:o,r:s,p:u,dkLen:x,blockSize32:c,V:h,B32:f,B:p,tmp:m,blockMixCb:y,asyncTick:A}=t4(t,n,i);for(let w=0;w<u;w++){const g=c*w;for(let S=0;S<c;S++)h[S]=f[g+S];let b=0;await bh(o-1,A,()=>{si(h,b,h,b+=c,s),y()}),si(h,(o-1)*c,f,g,s),y(),await bh(o,A,()=>{const S=f[g+c-16]%o;for(let B=0;B<c;B++)m[B]=f[g+B]^h[S*c+B];si(m,0,f,g,s),y()})}return r4(t,x,p,h,m)}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const n4=BigInt(0),El=BigInt(1),a9=BigInt(2),Cl=t=>t instanceof Uint8Array,o9=Array.from({length:256},(t,n)=>n.toString(16).padStart(2,"0"));function Ei(t){if(!Cl(t))throw new Error("Uint8Array expected");let n="";for(let i=0;i<t.length;i++)n+=o9[t[i]];return n}function i4(t){const n=t.toString(16);return n.length&1?`0${n}`:n}function jf(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return BigInt(t===""?"0":`0x${t}`)}function Ci(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const n=t.length;if(n%2)throw new Error("padded hex string expected, got unpadded hex of length "+n);const i=new Uint8Array(n/2);for(let o=0;o<i.length;o++){const s=o*2,u=t.slice(s,s+2),x=Number.parseInt(u,16);if(Number.isNaN(x)||x<0)throw new Error("Invalid byte sequence");i[o]=x}return i}function A0(t){return jf(Ei(t))}function Wf(t){if(!Cl(t))throw new Error("Uint8Array expected");return jf(Ei(Uint8Array.from(t).reverse()))}function Bi(t,n){return Ci(t.toString(16).padStart(n*2,"0"))}function Vf(t,n){return Bi(t,n).reverse()}function s9(t){return Ci(i4(t))}function cr(t,n,i){let o;if(typeof n=="string")try{o=Ci(n)}catch(u){throw new Error(`${t} must be valid hex string, got "${n}". Cause: ${u}`)}else if(Cl(n))o=Uint8Array.from(n);else throw new Error(`${t} must be hex string or Uint8Array`);const s=o.length;if(typeof i=="number"&&s!==i)throw new Error(`${t} expected ${i} bytes, got ${s}`);return o}function Oa(...t){const n=new Uint8Array(t.reduce((o,s)=>o+s.length,0));let i=0;return t.forEach(o=>{if(!Cl(o))throw new Error("Uint8Array expected");n.set(o,i),i+=o.length}),n}function l9(t,n){if(t.length!==n.length)return!1;for(let i=0;i<t.length;i++)if(t[i]!==n[i])return!1;return!0}function u9(t){if(typeof t!="string")throw new Error(`utf8ToBytes expected string, got ${typeof t}`);return new Uint8Array(new TextEncoder().encode(t))}function c9(t){let n;for(n=0;t>n4;t>>=El,n+=1);return n}function f9(t,n){return t>>BigInt(n)&El}const x9=(t,n,i)=>t|(i?El:n4)<<BigInt(n),qf=t=>(a9<<BigInt(t-1))-El,Sc=t=>new Uint8Array(t),Bh=t=>Uint8Array.from(t);function a4(t,n,i){if(typeof t!="number"||t<2)throw new Error("hashLen must be a number");if(typeof n!="number"||n<2)throw new Error("qByteLen must be a number");if(typeof i!="function")throw new Error("hmacFn must be a function");let o=Sc(t),s=Sc(t),u=0;const x=()=>{o.fill(1),s.fill(0),u=0},c=(...m)=>i(s,o,...m),h=(m=Sc())=>{s=c(Bh([0]),m),o=c(),m.length!==0&&(s=c(Bh([1]),m),o=c())},f=()=>{if(u++>=1e3)throw new Error("drbg: tried 1000 values");let m=0;const y=[];for(;m<n;){o=c();const A=o.slice();y.push(A),m+=o.length}return Oa(...y)};return(m,y)=>{x(),h(m);let A;for(;!(A=y(f()));)h();return x(),A}}const d9={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||t instanceof Uint8Array,isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,n)=>n.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function Ka(t,n,i={}){const o=(s,u,x)=>{const c=d9[u];if(typeof c!="function")throw new Error(`Invalid validator "${u}", expected function`);const h=t[s];if(!(x&&h===void 0)&&!c(h,t))throw new Error(`Invalid param ${String(s)}=${h} (${typeof h}), expected ${u}`)};for(const[s,u]of Object.entries(n))o(s,u,!1);for(const[s,u]of Object.entries(i))o(s,u,!0);return t}const h9=Object.freeze(Object.defineProperty({__proto__:null,bitGet:f9,bitLen:c9,bitMask:qf,bitSet:x9,bytesToHex:Ei,bytesToNumberBE:A0,bytesToNumberLE:Wf,concatBytes:Oa,createHmacDrbg:a4,ensureBytes:cr,equalBytes:l9,hexToBytes:Ci,hexToNumber:jf,numberToBytesBE:Bi,numberToBytesLE:Vf,numberToHexUnpadded:i4,numberToVarBytesBE:s9,utf8ToBytes:u9,validateObject:Ka},Symbol.toStringTag,{value:"Module"}));/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const nt=BigInt(0),qe=BigInt(1),l0=BigInt(2),p9=BigInt(3),of=BigInt(4),Sh=BigInt(5),Dh=BigInt(8);BigInt(9);BigInt(16);function kt(t,n){const i=t%n;return i>=nt?i:n+i}function v9(t,n,i){if(i<=nt||n<nt)throw new Error("Expected power/modulo > 0");if(i===qe)return nt;let o=qe;for(;n>nt;)n&qe&&(o=o*t%i),t=t*t%i,n>>=qe;return o}function jt(t,n,i){let o=t;for(;n-- >nt;)o*=o,o%=i;return o}function sf(t,n){if(t===nt||n<=nt)throw new Error(`invert: expected positive integers, got n=${t} mod=${n}`);let i=kt(t,n),o=n,s=nt,u=qe;for(;i!==nt;){const c=o/i,h=o%i,f=s-u*c;o=i,i=h,s=u,u=f}if(o!==qe)throw new Error("invert: does not exist");return kt(s,n)}function m9(t){const n=(t-qe)/l0;let i,o,s;for(i=t-qe,o=0;i%l0===nt;i/=l0,o++);for(s=l0;s<t&&v9(s,n,t)!==t-qe;s++);if(o===1){const x=(t+qe)/of;return function(h,f){const p=h.pow(f,x);if(!h.eql(h.sqr(p),f))throw new Error("Cannot find square root");return p}}const u=(i+qe)/l0;return function(c,h){if(c.pow(h,n)===c.neg(c.ONE))throw new Error("Cannot find square root");let f=o,p=c.pow(c.mul(c.ONE,s),i),m=c.pow(h,u),y=c.pow(h,i);for(;!c.eql(y,c.ONE);){if(c.eql(y,c.ZERO))return c.ZERO;let A=1;for(let g=c.sqr(y);A<f&&!c.eql(g,c.ONE);A++)g=c.sqr(g);const w=c.pow(p,qe<<BigInt(f-A-1));p=c.sqr(w),m=c.mul(m,w),y=c.mul(y,p),f=A}return m}}function y9(t){if(t%of===p9){const n=(t+qe)/of;return function(o,s){const u=o.pow(s,n);if(!o.eql(o.sqr(u),s))throw new Error("Cannot find square root");return u}}if(t%Dh===Sh){const n=(t-Sh)/Dh;return function(o,s){const u=o.mul(s,l0),x=o.pow(u,n),c=o.mul(s,x),h=o.mul(o.mul(c,l0),x),f=o.mul(c,o.sub(h,o.ONE));if(!o.eql(o.sqr(f),s))throw new Error("Cannot find square root");return f}}return m9(t)}const g9=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function b9(t){const n={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},i=g9.reduce((o,s)=>(o[s]="function",o),n);return Ka(t,i)}function A9(t,n,i){if(i<nt)throw new Error("Expected power > 0");if(i===nt)return t.ONE;if(i===qe)return n;let o=t.ONE,s=n;for(;i>nt;)i&qe&&(o=t.mul(o,s)),s=t.sqr(s),i>>=qe;return o}function w9(t,n){const i=new Array(n.length),o=n.reduce((u,x,c)=>t.is0(x)?u:(i[c]=u,t.mul(u,x)),t.ONE),s=t.inv(o);return n.reduceRight((u,x,c)=>t.is0(x)?u:(i[c]=t.mul(u,i[c]),t.mul(u,x)),s),i}function o4(t,n){const i=n!==void 0?n:t.toString(2).length,o=Math.ceil(i/8);return{nBitLength:i,nByteLength:o}}function E9(t,n,i=!1,o={}){if(t<=nt)throw new Error(`Expected Field ORDER > 0, got ${t}`);const{nBitLength:s,nByteLength:u}=o4(t,n);if(u>2048)throw new Error("Field lengths over 2048 bytes are not supported");const x=y9(t),c=Object.freeze({ORDER:t,BITS:s,BYTES:u,MASK:qf(s),ZERO:nt,ONE:qe,create:h=>kt(h,t),isValid:h=>{if(typeof h!="bigint")throw new Error(`Invalid field element: expected bigint, got ${typeof h}`);return nt<=h&&h<t},is0:h=>h===nt,isOdd:h=>(h&qe)===qe,neg:h=>kt(-h,t),eql:(h,f)=>h===f,sqr:h=>kt(h*h,t),add:(h,f)=>kt(h+f,t),sub:(h,f)=>kt(h-f,t),mul:(h,f)=>kt(h*f,t),pow:(h,f)=>A9(c,h,f),div:(h,f)=>kt(h*sf(f,t),t),sqrN:h=>h*h,addN:(h,f)=>h+f,subN:(h,f)=>h-f,mulN:(h,f)=>h*f,inv:h=>sf(h,t),sqrt:o.sqrt||(h=>x(c,h)),invertBatch:h=>w9(c,h),cmov:(h,f,p)=>p?f:h,toBytes:h=>i?Vf(h,u):Bi(h,u),fromBytes:h=>{if(h.length!==u)throw new Error(`Fp.fromBytes: expected ${u}, got ${h.length}`);return i?Wf(h):A0(h)}});return Object.freeze(c)}function s4(t){if(typeof t!="bigint")throw new Error("field order must be bigint");const n=t.toString(2).length;return Math.ceil(n/8)}function l4(t){const n=s4(t);return n+Math.ceil(n/2)}function C9(t,n,i=!1){const o=t.length,s=s4(n),u=l4(n);if(o<16||o<u||o>1024)throw new Error(`expected ${u}-1024 bytes of input, got ${o}`);const x=i?A0(t):Wf(t),c=kt(x,n-qe)+qe;return i?Vf(c,s):Bi(c,s)}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const B9=BigInt(0),Dc=BigInt(1);function S9(t,n){const i=(s,u)=>{const x=u.negate();return s?x:u},o=s=>{const u=Math.ceil(n/s)+1,x=2**(s-1);return{windows:u,windowSize:x}};return{constTimeNegate:i,unsafeLadder(s,u){let x=t.ZERO,c=s;for(;u>B9;)u&Dc&&(x=x.add(c)),c=c.double(),u>>=Dc;return x},precomputeWindow(s,u){const{windows:x,windowSize:c}=o(u),h=[];let f=s,p=f;for(let m=0;m<x;m++){p=f,h.push(p);for(let y=1;y<c;y++)p=p.add(f),h.push(p);f=p.double()}return h},wNAF(s,u,x){const{windows:c,windowSize:h}=o(s);let f=t.ZERO,p=t.BASE;const m=BigInt(2**s-1),y=2**s,A=BigInt(s);for(let w=0;w<c;w++){const g=w*h;let b=Number(x&m);x>>=A,b>h&&(b-=y,x+=Dc);const S=g,B=g+Math.abs(b)-1,E=w%2!==0,D=b<0;b===0?p=p.add(i(E,u[S])):f=f.add(i(D,u[B]))}return{p:f,f:p}},wNAFCached(s,u,x,c){const h=s._WINDOW_SIZE||1;let f=u.get(s);return f||(f=this.precomputeWindow(s,h),h!==1&&u.set(s,c(f))),this.wNAF(h,f,x)}}}function u4(t){return b9(t.Fp),Ka(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...o4(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function D9(t){const n=u4(t);Ka(n,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:i,Fp:o,a:s}=n;if(i){if(!o.eql(s,o.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if(typeof i!="object"||typeof i.beta!="bigint"||typeof i.splitScalar!="function")throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...n})}const{bytesToNumberBE:F9,hexToBytes:k9}=h9,u0={Err:class extends Error{constructor(n=""){super(n)}},_parseInt(t){const{Err:n}=u0;if(t.length<2||t[0]!==2)throw new n("Invalid signature integer tag");const i=t[1],o=t.subarray(2,i+2);if(!i||o.length!==i)throw new n("Invalid signature integer: wrong length");if(o[0]&128)throw new n("Invalid signature integer: negative");if(o[0]===0&&!(o[1]&128))throw new n("Invalid signature integer: unnecessary leading zero");return{d:F9(o),l:t.subarray(i+2)}},toSig(t){const{Err:n}=u0,i=typeof t=="string"?k9(t):t;if(!(i instanceof Uint8Array))throw new Error("ui8a expected");let o=i.length;if(o<2||i[0]!=48)throw new n("Invalid signature tag");if(i[1]!==o-2)throw new n("Invalid signature: incorrect length");const{d:s,l:u}=u0._parseInt(i.subarray(2)),{d:x,l:c}=u0._parseInt(u);if(c.length)throw new n("Invalid signature: left bytes after parsing");return{r:s,s:x}},hexFromSig(t){const n=f=>Number.parseInt(f[0],16)&8?"00"+f:f,i=f=>{const p=f.toString(16);return p.length&1?`0${p}`:p},o=n(i(t.s)),s=n(i(t.r)),u=o.length/2,x=s.length/2,c=i(u),h=i(x);return`30${i(x+u+4)}02${h}${s}02${c}${o}`}},qr=BigInt(0),Gt=BigInt(1);BigInt(2);const Fh=BigInt(3);BigInt(4);function R9(t){const n=D9(t),{Fp:i}=n,o=n.toBytes||((w,g,b)=>{const S=g.toAffine();return Oa(Uint8Array.from([4]),i.toBytes(S.x),i.toBytes(S.y))}),s=n.fromBytes||(w=>{const g=w.subarray(1),b=i.fromBytes(g.subarray(0,i.BYTES)),S=i.fromBytes(g.subarray(i.BYTES,2*i.BYTES));return{x:b,y:S}});function u(w){const{a:g,b}=n,S=i.sqr(w),B=i.mul(S,w);return i.add(i.add(B,i.mul(w,g)),b)}if(!i.eql(i.sqr(n.Gy),u(n.Gx)))throw new Error("bad generator point: equation left != right");function x(w){return typeof w=="bigint"&&qr<w&&w<n.n}function c(w){if(!x(w))throw new Error("Expected valid bigint: 0 < bigint < curve.n")}function h(w){const{allowedPrivateKeyLengths:g,nByteLength:b,wrapPrivateKey:S,n:B}=n;if(g&&typeof w!="bigint"){if(w instanceof Uint8Array&&(w=Ei(w)),typeof w!="string"||!g.includes(w.length))throw new Error("Invalid key");w=w.padStart(b*2,"0")}let E;try{E=typeof w=="bigint"?w:A0(cr("private key",w,b))}catch{throw new Error(`private key must be ${b} bytes, hex or bigint, not ${typeof w}`)}return S&&(E=kt(E,B)),c(E),E}const f=new Map;function p(w){if(!(w instanceof m))throw new Error("ProjectivePoint expected")}class m{constructor(g,b,S){if(this.px=g,this.py=b,this.pz=S,g==null||!i.isValid(g))throw new Error("x required");if(b==null||!i.isValid(b))throw new Error("y required");if(S==null||!i.isValid(S))throw new Error("z required")}static fromAffine(g){const{x:b,y:S}=g||{};if(!g||!i.isValid(b)||!i.isValid(S))throw new Error("invalid affine point");if(g instanceof m)throw new Error("projective point not allowed");const B=E=>i.eql(E,i.ZERO);return B(b)&&B(S)?m.ZERO:new m(b,S,i.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(g){const b=i.invertBatch(g.map(S=>S.pz));return g.map((S,B)=>S.toAffine(b[B])).map(m.fromAffine)}static fromHex(g){const b=m.fromAffine(s(cr("pointHex",g)));return b.assertValidity(),b}static fromPrivateKey(g){return m.BASE.multiply(h(g))}_setWindowSize(g){this._WINDOW_SIZE=g,f.delete(this)}assertValidity(){if(this.is0()){if(n.allowInfinityPoint&&!i.is0(this.py))return;throw new Error("bad point: ZERO")}const{x:g,y:b}=this.toAffine();if(!i.isValid(g)||!i.isValid(b))throw new Error("bad point: x or y not FE");const S=i.sqr(b),B=u(g);if(!i.eql(S,B))throw new Error("bad point: equation left != right");if(!this.isTorsionFree())throw new Error("bad point: not in prime-order subgroup")}hasEvenY(){const{y:g}=this.toAffine();if(i.isOdd)return!i.isOdd(g);throw new Error("Field doesn't support isOdd")}equals(g){p(g);const{px:b,py:S,pz:B}=this,{px:E,py:D,pz:k}=g,R=i.eql(i.mul(b,k),i.mul(E,B)),I=i.eql(i.mul(S,k),i.mul(D,B));return R&&I}negate(){return new m(this.px,i.neg(this.py),this.pz)}double(){const{a:g,b}=n,S=i.mul(b,Fh),{px:B,py:E,pz:D}=this;let k=i.ZERO,R=i.ZERO,I=i.ZERO,O=i.mul(B,B),Q=i.mul(E,E),_=i.mul(D,D),T=i.mul(B,E);return T=i.add(T,T),I=i.mul(B,D),I=i.add(I,I),k=i.mul(g,I),R=i.mul(S,_),R=i.add(k,R),k=i.sub(Q,R),R=i.add(Q,R),R=i.mul(k,R),k=i.mul(T,k),I=i.mul(S,I),_=i.mul(g,_),T=i.sub(O,_),T=i.mul(g,T),T=i.add(T,I),I=i.add(O,O),O=i.add(I,O),O=i.add(O,_),O=i.mul(O,T),R=i.add(R,O),_=i.mul(E,D),_=i.add(_,_),O=i.mul(_,T),k=i.sub(k,O),I=i.mul(_,Q),I=i.add(I,I),I=i.add(I,I),new m(k,R,I)}add(g){p(g);const{px:b,py:S,pz:B}=this,{px:E,py:D,pz:k}=g;let R=i.ZERO,I=i.ZERO,O=i.ZERO;const Q=n.a,_=i.mul(n.b,Fh);let T=i.mul(b,E),q=i.mul(S,D),$=i.mul(B,k),ae=i.add(b,S),K=i.add(E,D);ae=i.mul(ae,K),K=i.add(T,q),ae=i.sub(ae,K),K=i.add(b,B);let Y=i.add(E,k);return K=i.mul(K,Y),Y=i.add(T,$),K=i.sub(K,Y),Y=i.add(S,B),R=i.add(D,k),Y=i.mul(Y,R),R=i.add(q,$),Y=i.sub(Y,R),O=i.mul(Q,K),R=i.mul(_,$),O=i.add(R,O),R=i.sub(q,O),O=i.add(q,O),I=i.mul(R,O),q=i.add(T,T),q=i.add(q,T),$=i.mul(Q,$),K=i.mul(_,K),q=i.add(q,$),$=i.sub(T,$),$=i.mul(Q,$),K=i.add(K,$),T=i.mul(q,K),I=i.add(I,T),T=i.mul(Y,K),R=i.mul(ae,R),R=i.sub(R,T),T=i.mul(ae,q),O=i.mul(Y,O),O=i.add(O,T),new m(R,I,O)}subtract(g){return this.add(g.negate())}is0(){return this.equals(m.ZERO)}wNAF(g){return A.wNAFCached(this,f,g,b=>{const S=i.invertBatch(b.map(B=>B.pz));return b.map((B,E)=>B.toAffine(S[E])).map(m.fromAffine)})}multiplyUnsafe(g){const b=m.ZERO;if(g===qr)return b;if(c(g),g===Gt)return this;const{endo:S}=n;if(!S)return A.unsafeLadder(this,g);let{k1neg:B,k1:E,k2neg:D,k2:k}=S.splitScalar(g),R=b,I=b,O=this;for(;E>qr||k>qr;)E&Gt&&(R=R.add(O)),k&Gt&&(I=I.add(O)),O=O.double(),E>>=Gt,k>>=Gt;return B&&(R=R.negate()),D&&(I=I.negate()),I=new m(i.mul(I.px,S.beta),I.py,I.pz),R.add(I)}multiply(g){c(g);let b=g,S,B;const{endo:E}=n;if(E){const{k1neg:D,k1:k,k2neg:R,k2:I}=E.splitScalar(b);let{p:O,f:Q}=this.wNAF(k),{p:_,f:T}=this.wNAF(I);O=A.constTimeNegate(D,O),_=A.constTimeNegate(R,_),_=new m(i.mul(_.px,E.beta),_.py,_.pz),S=O.add(_),B=Q.add(T)}else{const{p:D,f:k}=this.wNAF(b);S=D,B=k}return m.normalizeZ([S,B])[0]}multiplyAndAddUnsafe(g,b,S){const B=m.BASE,E=(k,R)=>R===qr||R===Gt||!k.equals(B)?k.multiplyUnsafe(R):k.multiply(R),D=E(this,b).add(E(g,S));return D.is0()?void 0:D}toAffine(g){const{px:b,py:S,pz:B}=this,E=this.is0();g==null&&(g=E?i.ONE:i.inv(B));const D=i.mul(b,g),k=i.mul(S,g),R=i.mul(B,g);if(E)return{x:i.ZERO,y:i.ZERO};if(!i.eql(R,i.ONE))throw new Error("invZ was invalid");return{x:D,y:k}}isTorsionFree(){const{h:g,isTorsionFree:b}=n;if(g===Gt)return!0;if(b)return b(m,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:g,clearCofactor:b}=n;return g===Gt?this:b?b(m,this):this.multiplyUnsafe(n.h)}toRawBytes(g=!0){return this.assertValidity(),o(m,this,g)}toHex(g=!0){return Ei(this.toRawBytes(g))}}m.BASE=new m(n.Gx,n.Gy,i.ONE),m.ZERO=new m(i.ZERO,i.ONE,i.ZERO);const y=n.nBitLength,A=S9(m,n.endo?Math.ceil(y/2):y);return{CURVE:n,ProjectivePoint:m,normPrivateKeyToScalar:h,weierstrassEquation:u,isWithinCurveOrder:x}}function P9(t){const n=u4(t);return Ka(n,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...n})}function _9(t){const n=P9(t),{Fp:i,n:o}=n,s=i.BYTES+1,u=2*i.BYTES+1;function x(K){return qr<K&&K<i.ORDER}function c(K){return kt(K,o)}function h(K){return sf(K,o)}const{ProjectivePoint:f,normPrivateKeyToScalar:p,weierstrassEquation:m,isWithinCurveOrder:y}=R9({...n,toBytes(K,Y,ne){const Z=Y.toAffine(),J=i.toBytes(Z.x),X=Oa;return ne?X(Uint8Array.from([Y.hasEvenY()?2:3]),J):X(Uint8Array.from([4]),J,i.toBytes(Z.y))},fromBytes(K){const Y=K.length,ne=K[0],Z=K.subarray(1);if(Y===s&&(ne===2||ne===3)){const J=A0(Z);if(!x(J))throw new Error("Point is not on curve");const X=m(J);let F=i.sqrt(X);const M=(F&Gt)===Gt;return(ne&1)===1!==M&&(F=i.neg(F)),{x:J,y:F}}else if(Y===u&&ne===4){const J=i.fromBytes(Z.subarray(0,i.BYTES)),X=i.fromBytes(Z.subarray(i.BYTES,2*i.BYTES));return{x:J,y:X}}else throw new Error(`Point of length ${Y} was invalid. Expected ${s} compressed bytes or ${u} uncompressed bytes`)}}),A=K=>Ei(Bi(K,n.nByteLength));function w(K){const Y=o>>Gt;return K>Y}function g(K){return w(K)?c(-K):K}const b=(K,Y,ne)=>A0(K.slice(Y,ne));class S{constructor(Y,ne,Z){this.r=Y,this.s=ne,this.recovery=Z,this.assertValidity()}static fromCompact(Y){const ne=n.nByteLength;return Y=cr("compactSignature",Y,ne*2),new S(b(Y,0,ne),b(Y,ne,2*ne))}static fromDER(Y){const{r:ne,s:Z}=u0.toSig(cr("DER",Y));return new S(ne,Z)}assertValidity(){if(!y(this.r))throw new Error("r must be 0 < r < CURVE.n");if(!y(this.s))throw new Error("s must be 0 < s < CURVE.n")}addRecoveryBit(Y){return new S(this.r,this.s,Y)}recoverPublicKey(Y){const{r:ne,s:Z,recovery:J}=this,X=I(cr("msgHash",Y));if(J==null||![0,1,2,3].includes(J))throw new Error("recovery id invalid");const F=J===2||J===3?ne+n.n:ne;if(F>=i.ORDER)throw new Error("recovery id 2 or 3 invalid");const M=(J&1)===0?"02":"03",G=f.fromHex(M+A(F)),W=h(F),fe=c(-X*W),ce=c(Z*W),ye=f.BASE.multiplyAndAddUnsafe(G,fe,ce);if(!ye)throw new Error("point at infinify");return ye.assertValidity(),ye}hasHighS(){return w(this.s)}normalizeS(){return this.hasHighS()?new S(this.r,c(-this.s),this.recovery):this}toDERRawBytes(){return Ci(this.toDERHex())}toDERHex(){return u0.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return Ci(this.toCompactHex())}toCompactHex(){return A(this.r)+A(this.s)}}const B={isValidPrivateKey(K){try{return p(K),!0}catch{return!1}},normPrivateKeyToScalar:p,randomPrivateKey:()=>{const K=l4(n.n);return C9(n.randomBytes(K),n.n)},precompute(K=8,Y=f.BASE){return Y._setWindowSize(K),Y.multiply(BigInt(3)),Y}};function E(K,Y=!0){return f.fromPrivateKey(K).toRawBytes(Y)}function D(K){const Y=K instanceof Uint8Array,ne=typeof K=="string",Z=(Y||ne)&&K.length;return Y?Z===s||Z===u:ne?Z===2*s||Z===2*u:K instanceof f}function k(K,Y,ne=!0){if(D(K))throw new Error("first arg must be private key");if(!D(Y))throw new Error("second arg must be public key");return f.fromHex(Y).multiply(p(K)).toRawBytes(ne)}const R=n.bits2int||function(K){const Y=A0(K),ne=K.length*8-n.nBitLength;return ne>0?Y>>BigInt(ne):Y},I=n.bits2int_modN||function(K){return c(R(K))},O=qf(n.nBitLength);function Q(K){if(typeof K!="bigint")throw new Error("bigint expected");if(!(qr<=K&&K<O))throw new Error(`bigint expected < 2^${n.nBitLength}`);return Bi(K,n.nByteLength)}function _(K,Y,ne=T){if(["recovered","canonical"].some(we=>we in ne))throw new Error("sign() legacy options not supported");const{hash:Z,randomBytes:J}=n;let{lowS:X,prehash:F,extraEntropy:M}=ne;X==null&&(X=!0),K=cr("msgHash",K),F&&(K=cr("prehashed msgHash",Z(K)));const G=I(K),W=p(Y),fe=[Q(W),Q(G)];if(M!=null){const we=M===!0?J(i.BYTES):M;fe.push(cr("extraEntropy",we))}const ce=Oa(...fe),ye=G;function oe(we){const Pe=R(we);if(!y(Pe))return;const Jt=h(Pe),$e=f.BASE.multiply(Pe).toAffine(),Te=c($e.x);if(Te===qr)return;const Ue=c(Jt*c(ye+Te*W));if(Ue===qr)return;let vr=($e.x===Te?0:2)|Number($e.y&Gt),er=Ue;return X&&w(Ue)&&(er=g(Ue),vr^=1),new S(Te,er,vr)}return{seed:ce,k2sig:oe}}const T={lowS:n.lowS,prehash:!1},q={lowS:n.lowS,prehash:!1};function $(K,Y,ne=T){const{seed:Z,k2sig:J}=_(K,Y,ne),X=n;return a4(X.hash.outputLen,X.nByteLength,X.hmac)(Z,J)}f.BASE._setWindowSize(8);function ae(K,Y,ne,Z=q){const J=K;if(Y=cr("msgHash",Y),ne=cr("publicKey",ne),"strict"in Z)throw new Error("options.strict was renamed to lowS");const{lowS:X,prehash:F}=Z;let M,G;try{if(typeof J=="string"||J instanceof Uint8Array)try{M=S.fromDER(J)}catch($e){if(!($e instanceof u0.Err))throw $e;M=S.fromCompact(J)}else if(typeof J=="object"&&typeof J.r=="bigint"&&typeof J.s=="bigint"){const{r:$e,s:Te}=J;M=new S($e,Te)}else throw new Error("PARSE");G=f.fromHex(ne)}catch($e){if($e.message==="PARSE")throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(X&&M.hasHighS())return!1;F&&(Y=n.hash(Y));const{r:W,s:fe}=M,ce=I(Y),ye=h(fe),oe=c(ce*ye),we=c(W*ye),Pe=f.BASE.multiplyAndAddUnsafe(G,oe,we)?.toAffine();return Pe?c(Pe.x)===W:!1}return{CURVE:n,getPublicKey:E,getSharedSecret:k,sign:$,verify:ae,ProjectivePoint:f,Signature:S,utils:B}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function I9(t){return{hash:t,hmac:(n,...i)=>Nf(t,n,hy(...i)),randomBytes:vy}}function O9(t,n){const i=o=>_9({...t,...I9(o)});return Object.freeze({...i(n),create:i})}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const c4=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),kh=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),M9=BigInt(1),lf=BigInt(2),Rh=(t,n)=>(t+n/lf)/n;function L9(t){const n=c4,i=BigInt(3),o=BigInt(6),s=BigInt(11),u=BigInt(22),x=BigInt(23),c=BigInt(44),h=BigInt(88),f=t*t*t%n,p=f*f*t%n,m=jt(p,i,n)*p%n,y=jt(m,i,n)*p%n,A=jt(y,lf,n)*f%n,w=jt(A,s,n)*A%n,g=jt(w,u,n)*w%n,b=jt(g,c,n)*g%n,S=jt(b,h,n)*b%n,B=jt(S,c,n)*g%n,E=jt(B,i,n)*p%n,D=jt(E,x,n)*w%n,k=jt(D,o,n)*f%n,R=jt(k,lf,n);if(!uf.eql(uf.sqr(R),t))throw new Error("Cannot find square root");return R}const uf=E9(c4,void 0,void 0,{sqrt:L9}),N9=O9({a:BigInt(0),b:BigInt(7),Fp:uf,n:kh,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:t=>{const n=kh,i=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),o=-M9*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),s=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),u=i,x=BigInt("0x100000000000000000000000000000000"),c=Rh(u*t,n),h=Rh(-o*t,n);let f=kt(t-c*i-h*s,n),p=kt(-c*o-h*u,n);const m=f>x,y=p>x;if(m&&(f=n-f),y&&(p=n-p),f>x||p>x)throw new Error("splitScalar: Endomorphism failed, k="+t);return{k1neg:m,k1:f,k2neg:y,k2:p}}}},zf);BigInt(0);N9.ProjectivePoint;var T9="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";const Ph=new Map([[8217,"apostrophe"],[8260,"fraction slash"],[12539,"middle dot"]]),_h=4;function z9(t){let n=0;function i(){return t[n++]<<8|t[n++]}let o=i(),s=1,u=[0,1];for(let k=1;k<o;k++)u.push(s+=i());let x=i(),c=n;n+=x;let h=0,f=0;function p(){return h==0&&(f=f<<8|t[n++],h=8),f>>--h&1}const m=31,y=2**m,A=y>>>1,w=A>>1,g=y-1;let b=0;for(let k=0;k<m;k++)b=b<<1|p();let S=[],B=0,E=y;for(;;){let k=Math.floor(((b-B+1)*s-1)/E),R=0,I=o;for(;I-R>1;){let _=R+I>>>1;k<u[_]?I=_:R=_}if(R==0)break;S.push(R);let O=B+Math.floor(E*u[R]/s),Q=B+Math.floor(E*u[R+1]/s)-1;for(;((O^Q)&A)==0;)b=b<<1&g|p(),O=O<<1&g,Q=Q<<1&g|1;for(;O&~Q&w;)b=b&A|b<<1&g>>>1|p(),O=O<<1^A,Q=(Q^A)<<1|A|1;B=O,E=1+Q-O}let D=o-4;return S.map(k=>{switch(k-D){case 3:return D+65792+(t[c++]<<16|t[c++]<<8|t[c++]);case 2:return D+256+(t[c++]<<8|t[c++]);case 1:return D+t[c++];default:return k-1}})}function H9(t){let n=0;return()=>t[n++]}function f4(t){return H9(z9(U9(t)))}function U9(t){let n=[];[..."ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"].forEach((s,u)=>n[s.charCodeAt(0)]=u);let i=t.length,o=new Uint8Array(6*i>>3);for(let s=0,u=0,x=0,c=0;s<i;s++)c=c<<6|n[t.charCodeAt(s)],x+=6,x>=8&&(o[u++]=c>>(x-=8));return o}function Q9(t){return t&1?~t>>1:t>>1}function j9(t,n){let i=Array(t);for(let o=0,s=0;o<t;o++)i[o]=s+=Q9(n());return i}function Ma(t,n=0){let i=[];for(;;){let o=t(),s=t();if(!s)break;n+=o;for(let u=0;u<s;u++)i.push(n+u);n+=s+1}return i}function x4(t){return La(()=>{let n=Ma(t);if(n.length)return n})}function d4(t){let n=[];for(;;){let i=t();if(i==0)break;n.push(W9(i,t))}for(;;){let i=t()-1;if(i<0)break;n.push(V9(i,t))}return n.flat()}function La(t){let n=[];for(;;){let i=t(n.length);if(!i)break;n.push(i)}return n}function h4(t,n,i){let o=Array(t).fill().map(()=>[]);for(let s=0;s<n;s++)j9(t,i).forEach((u,x)=>o[x].push(u));return o}function W9(t,n){let i=1+n(),o=n(),s=La(n);return h4(s.length,1+t,n).flatMap((x,c)=>{let[h,...f]=x;return Array(s[c]).fill().map((p,m)=>{let y=m*o;return[h+m*i,f.map(A=>A+y)]})})}function V9(t,n){let i=1+n();return h4(i,1+t,n).map(s=>[s[0],s.slice(1)])}function q9(t){let n=[],i=Ma(t);return s(o([]),[]),n;function o(u){let x=t(),c=La(()=>{let h=Ma(t).map(f=>i[f]);if(h.length)return o(h)});return{S:x,B:c,Q:u}}function s({S:u,B:x},c,h){if(!(u&4&&h===c[c.length-1])){u&2&&(h=c[c.length-1]),u&1&&n.push(c);for(let f of x)for(let p of f.Q)s(f,[...c,p],h)}}}function K9(t){return t.toString(16).toUpperCase().padStart(2,"0")}function p4(t){return`{${K9(t)}}`}function $9(t){let n=[];for(let i=0,o=t.length;i<o;){let s=t.codePointAt(i);i+=s<65536?1:2,n.push(s)}return n}function Si(t){let i=t.length;if(i<4096)return String.fromCodePoint(...t);let o=[];for(let s=0;s<i;)o.push(String.fromCodePoint(...t.slice(s,s+=4096)));return o.join("")}function G9(t,n){let i=t.length,o=i-n.length;for(let s=0;o==0&&s<i;s++)o=t[s]-n[s];return o}var Z9="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";const Na=44032,dl=4352,hl=4449,pl=4519,v4=19,m4=21,Di=28,vl=m4*Di,Y9=v4*vl,X9=Na+Y9,J9=dl+v4,eg=hl+m4,tg=pl+Di;function Da(t){return t>>24&255}function y4(t){return t&16777215}let cf,Ih,ff,Cs;function rg(){let t=f4(Z9);cf=new Map(x4(t).flatMap((n,i)=>n.map(o=>[o,i+1<<24]))),Ih=new Set(Ma(t)),ff=new Map,Cs=new Map;for(let[n,i]of d4(t)){if(!Ih.has(n)&&i.length==2){let[o,s]=i,u=Cs.get(o);u||(u=new Map,Cs.set(o,u)),u.set(s,n)}ff.set(n,i.reverse())}}function g4(t){return t>=Na&&t<X9}function ng(t,n){if(t>=dl&&t<J9&&n>=hl&&n<eg)return Na+(t-dl)*vl+(n-hl)*Di;if(g4(t)&&n>pl&&n<tg&&(t-Na)%Di==0)return t+(n-pl);{let i=Cs.get(t);return i&&(i=i.get(n),i)?i:-1}}function b4(t){cf||rg();let n=[],i=[],o=!1;function s(u){let x=cf.get(u);x&&(o=!0,u|=x),n.push(u)}for(let u of t)for(;;){if(u<128)n.push(u);else if(g4(u)){let x=u-Na,c=x/vl|0,h=x%vl/Di|0,f=x%Di;s(dl+c),s(hl+h),f>0&&s(pl+f)}else{let x=ff.get(u);x?i.push(...x):s(u)}if(!i.length)break;u=i.pop()}if(o&&n.length>1){let u=Da(n[0]);for(let x=1;x<n.length;x++){let c=Da(n[x]);if(c==0||u<=c){u=c;continue}let h=x-1;for(;;){let f=n[h+1];if(n[h+1]=n[h],n[h]=f,!h||(u=Da(n[--h]),u<=c))break}u=Da(n[x])}}return n}function ig(t){let n=[],i=[],o=-1,s=0;for(let u of t){let x=Da(u),c=y4(u);if(o==-1)x==0?o=c:n.push(c);else if(s>0&&s>=x)x==0?(n.push(o,...i),i.length=0,o=c):i.push(c),s=x;else{let h=ng(o,c);h>=0?o=h:s==0&&x==0?(n.push(o),o=c):(i.push(c),s=x)}}return o>=0&&n.push(o,...i),n}function A4(t){return b4(t).map(y4)}function ag(t){return ig(b4(t))}const Oh=45,w4=".",E4=65039,C4=1,ml=t=>Array.from(t);function Ta(t,n){return t.P.has(n)||t.Q.has(n)}class og extends Array{get is_emoji(){return!0}}let xf,B4,c0,df,S4,li,Fc,ai,o0,Mh,hf;function Kf(){if(xf)return;let t=f4(T9);const n=()=>Ma(t),i=()=>new Set(n()),o=(p,m)=>m.forEach(y=>p.add(y));xf=new Map(d4(t)),B4=i(),c0=n(),df=new Set(n().map(p=>c0[p])),c0=new Set(c0),S4=i(),i();let s=x4(t),u=t();const x=()=>{let p=new Set;return n().forEach(m=>o(p,s[m])),o(p,n()),p};li=La(p=>{let m=La(t).map(y=>y+96);if(m.length){let y=p>=u;m[0]-=32,m=Si(m),y&&(m=`Restricted[${m}]`);let A=x(),w=x(),g=!t();return{N:m,P:A,Q:w,M:g,R:y}}}),Fc=i(),ai=new Map;let c=n().concat(ml(Fc)).sort((p,m)=>p-m);c.forEach((p,m)=>{let y=t(),A=c[m]=y?c[m-y]:{V:[],M:new Map};A.V.push(p),Fc.has(p)||ai.set(p,A)});for(let{V:p,M:m}of new Set(ai.values())){let y=[];for(let w of p){let g=li.filter(S=>Ta(S,w)),b=y.find(({G:S})=>g.some(B=>S.has(B)));b||(b={G:new Set,V:[]},y.push(b)),b.V.push(w),o(b.G,g)}let A=y.flatMap(w=>ml(w.G));for(let{G:w,V:g}of y){let b=new Set(A.filter(S=>!w.has(S)));for(let S of g)m.set(S,b)}}o0=new Set;let h=new Set;const f=p=>o0.has(p)?h.add(p):o0.add(p);for(let p of li){for(let m of p.P)f(m);for(let m of p.Q)f(m)}for(let p of o0)!ai.has(p)&&!h.has(p)&&ai.set(p,C4);o(o0,A4(o0)),Mh=q9(t).map(p=>og.from(p)).sort(G9),hf=new Map;for(let p of Mh){let m=[hf];for(let y of p){let A=m.map(w=>{let g=w.get(y);return g||(g=new Map,w.set(y,g)),g});y===E4?m.push(...A):m=A}for(let y of m)y.V=p}}function $f(t){return(D4(t)?"":`${Gf(Bl([t]))} `)+p4(t)}function Gf(t){return`"${t}"‎`}function sg(t){if(t.length>=4&&t[2]==Oh&&t[3]==Oh)throw new Error(`invalid label extension: "${Si(t.slice(0,4))}"`)}function lg(t){for(let i=t.lastIndexOf(95);i>0;)if(t[--i]!==95)throw new Error("underscore allowed only at start")}function ug(t){let n=t[0],i=Ph.get(n);if(i)throw Ra(`leading ${i}`);let o=t.length,s=-1;for(let u=1;u<o;u++){n=t[u];let x=Ph.get(n);if(x){if(s==u)throw Ra(`${i} + ${x}`);s=u+1,i=x}}if(s==o)throw Ra(`trailing ${i}`)}function Bl(t,n=1/0,i=p4){let o=[];cg(t[0])&&o.push("◌"),t.length>n&&(n>>=1,t=[...t.slice(0,n),8230,...t.slice(-n)]);let s=0,u=t.length;for(let x=0;x<u;x++){let c=t[x];D4(c)&&(o.push(Si(t.slice(s,x))),o.push(i(c)),s=x+1)}return o.push(Si(t.slice(s,u))),o.join("")}function cg(t){return Kf(),c0.has(t)}function D4(t){return Kf(),S4.has(t)}function fA(t){return hg(fg(t,ag,mg))}function fg(t,n,i){if(!t)return[];Kf();let o=0;return t.split(w4).map(s=>{let u=$9(s),x={input:u,offset:o};o+=u.length+1;try{let c=x.tokens=vg(u,n,i),h=c.length,f;if(!h)throw new Error("empty label");let p=x.output=c.flat();if(lg(p),!(x.emoji=h>1||c[0].is_emoji)&&p.every(y=>y<128))sg(p),f="ASCII";else{let y=c.flatMap(A=>A.is_emoji?[]:A);if(!y.length)f="Emoji";else{if(c0.has(p[0]))throw Ra("leading combining mark");for(let g=1;g<h;g++){let b=c[g];if(!b.is_emoji&&c0.has(b[0]))throw Ra(`emoji + combining mark: "${Si(c[g-1])} + ${Bl([b[0]])}"`)}ug(p);let A=ml(new Set(y)),[w]=dg(A);pg(w,y),xg(w,A),f=w.N}}x.type=f}catch(c){x.error=c}return x})}function xg(t,n){let i,o=[];for(let s of n){let u=ai.get(s);if(u===C4)return;if(u){let x=u.M.get(s);if(i=i?i.filter(c=>x.has(c)):ml(x),!i.length)return}else o.push(s)}if(i){for(let s of i)if(o.every(u=>Ta(s,u)))throw new Error(`whole-script confusable: ${t.N}/${s.N}`)}}function dg(t){let n=li;for(let i of t){let o=n.filter(s=>Ta(s,i));if(!o.length)throw li.some(s=>Ta(s,i))?k4(n[0],i):F4(i);if(n=o,o.length==1)break}return n}function hg(t){return t.map(({input:n,error:i,output:o})=>{if(i){let s=i.message;throw new Error(t.length==1?s:`Invalid label ${Gf(Bl(n,63))}: ${s}`)}return Si(o)}).join(w4)}function F4(t){return new Error(`disallowed character: ${$f(t)}`)}function k4(t,n){let i=$f(n),o=li.find(s=>s.P.has(n));return o&&(i=`${o.N} ${i}`),new Error(`illegal mixture: ${t.N} + ${i}`)}function Ra(t){return new Error(`illegal placement: ${t}`)}function pg(t,n){for(let i of n)if(!Ta(t,i))throw k4(t,i);if(t.M){let i=A4(n);for(let o=1,s=i.length;o<s;o++)if(df.has(i[o])){let u=o+1;for(let x;u<s&&df.has(x=i[u]);u++)for(let c=o;c<u;c++)if(i[c]==x)throw new Error(`duplicate non-spacing marks: ${$f(x)}`);if(u-o>_h)throw new Error(`excessive non-spacing marks: ${Gf(Bl(i.slice(o-1,u)))} (${u-o}/${_h})`);o=u}}}function vg(t,n,i){let o=[],s=[];for(t=t.slice().reverse();t.length;){let u=yg(t);if(u)s.length&&(o.push(n(s)),s=[]),o.push(i(u));else{let x=t.pop();if(o0.has(x))s.push(x);else{let c=xf.get(x);if(c)s.push(...c);else if(!B4.has(x))throw F4(x)}}}return s.length&&o.push(n(s)),o}function mg(t){return t.filter(n=>n!=E4)}function yg(t,n){let i=hf,o,s=t.length;for(;s&&(i=i.get(t[--s]),!!i);){let{V:u}=i;u&&(o=u,t.length=s)}return o}/*! MIT License. Copyright 2015-2022 Richard Moore <<EMAIL>>. See LICENSE.txt. */var st=function(t,n,i,o){if(i==="a"&&!o)throw new TypeError("Private accessor was defined without a getter");if(typeof n=="function"?t!==n||!o:!n.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return i==="m"?o:i==="a"?o.call(t):o?o.value:n.get(t)},kc=function(t,n,i,o,s){if(o==="m")throw new TypeError("Private method is not writable");if(o==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof n=="function"?t!==n||!s:!n.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return o==="a"?s.call(t,i):s?s.value=i:n.set(t,i),i},Bs,qt,Cr;const gg={16:10,24:12,32:14},bg=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],Wt=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],vs=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],Ag=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],wg=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],Eg=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],Cg=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],Bg=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],Sg=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],Dg=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],Fg=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],kg=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],Rg=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],Pg=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],_g=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925];function Rc(t){const n=[];for(let i=0;i<t.length;i+=4)n.push(t[i]<<24|t[i+1]<<16|t[i+2]<<8|t[i+3]);return n}class Zf{get key(){return st(this,Bs,"f").slice()}constructor(n){if(Bs.set(this,void 0),qt.set(this,void 0),Cr.set(this,void 0),!(this instanceof Zf))throw Error("AES must be instanitated with `new`");kc(this,Bs,new Uint8Array(n),"f");const i=gg[this.key.length];if(i==null)throw new TypeError("invalid key size (must be 16, 24 or 32 bytes)");kc(this,Cr,[],"f"),kc(this,qt,[],"f");for(let p=0;p<=i;p++)st(this,Cr,"f").push([0,0,0,0]),st(this,qt,"f").push([0,0,0,0]);const o=(i+1)*4,s=this.key.length/4,u=Rc(this.key);let x;for(let p=0;p<s;p++)x=p>>2,st(this,Cr,"f")[x][p%4]=u[p],st(this,qt,"f")[i-x][p%4]=u[p];let c=0,h=s,f;for(;h<o;){if(f=u[s-1],u[0]^=Wt[f>>16&255]<<24^Wt[f>>8&255]<<16^Wt[f&255]<<8^Wt[f>>24&255]^bg[c]<<24,c+=1,s!=8)for(let A=1;A<s;A++)u[A]^=u[A-1];else{for(let A=1;A<s/2;A++)u[A]^=u[A-1];f=u[s/2-1],u[s/2]^=Wt[f&255]^Wt[f>>8&255]<<8^Wt[f>>16&255]<<16^Wt[f>>24&255]<<24;for(let A=s/2+1;A<s;A++)u[A]^=u[A-1]}let p=0,m,y;for(;p<s&&h<o;)m=h>>2,y=h%4,st(this,Cr,"f")[m][y]=u[p],st(this,qt,"f")[i-m][y]=u[p++],h++}for(let p=1;p<i;p++)for(let m=0;m<4;m++)f=st(this,qt,"f")[p][m],st(this,qt,"f")[p][m]=kg[f>>24&255]^Rg[f>>16&255]^Pg[f>>8&255]^_g[f&255]}encrypt(n){if(n.length!=16)throw new TypeError("invalid plaintext size (must be 16 bytes)");const i=st(this,Cr,"f").length-1,o=[0,0,0,0];let s=Rc(n);for(let c=0;c<4;c++)s[c]^=st(this,Cr,"f")[0][c];for(let c=1;c<i;c++){for(let h=0;h<4;h++)o[h]=Ag[s[h]>>24&255]^wg[s[(h+1)%4]>>16&255]^Eg[s[(h+2)%4]>>8&255]^Cg[s[(h+3)%4]&255]^st(this,Cr,"f")[c][h];s=o.slice()}const u=new Uint8Array(16);let x=0;for(let c=0;c<4;c++)x=st(this,Cr,"f")[i][c],u[4*c]=(Wt[s[c]>>24&255]^x>>24)&255,u[4*c+1]=(Wt[s[(c+1)%4]>>16&255]^x>>16)&255,u[4*c+2]=(Wt[s[(c+2)%4]>>8&255]^x>>8)&255,u[4*c+3]=(Wt[s[(c+3)%4]&255]^x)&255;return u}decrypt(n){if(n.length!=16)throw new TypeError("invalid ciphertext size (must be 16 bytes)");const i=st(this,qt,"f").length-1,o=[0,0,0,0];let s=Rc(n);for(let c=0;c<4;c++)s[c]^=st(this,qt,"f")[0][c];for(let c=1;c<i;c++){for(let h=0;h<4;h++)o[h]=Bg[s[h]>>24&255]^Sg[s[(h+3)%4]>>16&255]^Dg[s[(h+2)%4]>>8&255]^Fg[s[(h+1)%4]&255]^st(this,qt,"f")[c][h];s=o.slice()}const u=new Uint8Array(16);let x=0;for(let c=0;c<4;c++)x=st(this,qt,"f")[i][c],u[4*c]=(vs[s[c]>>24&255]^x>>24)&255,u[4*c+1]=(vs[s[(c+3)%4]>>16&255]^x>>16)&255,u[4*c+2]=(vs[s[(c+2)%4]>>8&255]^x>>8)&255,u[4*c+3]=(vs[s[(c+1)%4]&255]^x)&255;return u}}Bs=new WeakMap,qt=new WeakMap,Cr=new WeakMap;class R4{constructor(n,i,o){if(o&&!(this instanceof o))throw new Error(`${n} must be instantiated with "new"`);Object.defineProperties(this,{aes:{enumerable:!0,value:new Zf(i)},name:{enumerable:!0,value:n}})}}var ms=function(t,n,i,o,s){if(o==="m")throw new TypeError("Private method is not writable");if(o==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof n=="function"?t!==n||!s:!n.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return o==="a"?s.call(t,i):s?s.value=i:n.set(t,i),i},ni=function(t,n,i,o){if(i==="a"&&!o)throw new TypeError("Private accessor was defined without a getter");if(typeof n=="function"?t!==n||!o:!n.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return i==="m"?o:i==="a"?o.call(t):o?o.value:n.get(t)},Fa,jr;class P4 extends R4{constructor(n,i){if(super("ECC",n,P4),Fa.set(this,void 0),jr.set(this,void 0),i){if(i.length%16)throw new TypeError("invalid iv size (must be 16 bytes)");ms(this,Fa,new Uint8Array(i),"f")}else ms(this,Fa,new Uint8Array(16),"f");ms(this,jr,this.iv,"f")}get iv(){return new Uint8Array(ni(this,Fa,"f"))}encrypt(n){if(n.length%16)throw new TypeError("invalid plaintext size (must be multiple of 16 bytes)");const i=new Uint8Array(n.length);for(let o=0;o<n.length;o+=16){for(let s=0;s<16;s++)ni(this,jr,"f")[s]^=n[o+s];ms(this,jr,this.aes.encrypt(ni(this,jr,"f")),"f"),i.set(ni(this,jr,"f"),o)}return i}decrypt(n){if(n.length%16)throw new TypeError("invalid ciphertext size (must be multiple of 16 bytes)");const i=new Uint8Array(n.length);for(let o=0;o<n.length;o+=16){const s=this.aes.decrypt(n.subarray(o,o+16));for(let u=0;u<16;u++)i[o+u]=s[u]^ni(this,jr,"f")[u],ni(this,jr,"f")[u]=n[o+u]}return i}}Fa=new WeakMap,jr=new WeakMap;var ii=function(t,n,i,o,s){if(o==="m")throw new TypeError("Private method is not writable");if(o==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof n=="function"?t!==n||!s:!n.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return o==="a"?s.call(t,i):s?s.value=i:n.set(t,i),i},Vt=function(t,n,i,o){if(i==="a"&&!o)throw new TypeError("Private accessor was defined without a getter");if(typeof n=="function"?t!==n||!o:!n.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return i==="m"?o:i==="a"?o.call(t):o?o.value:n.get(t)},ka,s0,Kt;class _4 extends R4{constructor(n,i){super("CTR",n,_4),ka.set(this,void 0),s0.set(this,void 0),Kt.set(this,void 0),ii(this,Kt,new Uint8Array(16),"f"),Vt(this,Kt,"f").fill(0),ii(this,ka,Vt(this,Kt,"f"),"f"),ii(this,s0,16,"f"),i==null&&(i=1),typeof i=="number"?this.setCounterValue(i):this.setCounterBytes(i)}get counter(){return new Uint8Array(Vt(this,Kt,"f"))}setCounterValue(n){if(!Number.isInteger(n)||n<0||n>Number.MAX_SAFE_INTEGER)throw new TypeError("invalid counter initial integer value");for(let i=15;i>=0;--i)Vt(this,Kt,"f")[i]=n%256,n=Math.floor(n/256)}setCounterBytes(n){if(n.length!==16)throw new TypeError("invalid counter initial Uint8Array value length");Vt(this,Kt,"f").set(n)}increment(){for(let n=15;n>=0;n--)if(Vt(this,Kt,"f")[n]===255)Vt(this,Kt,"f")[n]=0;else{Vt(this,Kt,"f")[n]++;break}}encrypt(n){var i,o;const s=new Uint8Array(n);for(let u=0;u<s.length;u++)Vt(this,s0,"f")===16&&(ii(this,ka,this.aes.encrypt(Vt(this,Kt,"f")),"f"),ii(this,s0,0,"f"),this.increment()),s[u]^=Vt(this,ka,"f")[ii(this,s0,(o=Vt(this,s0,"f"),i=o++,o),"f"),i];return s}decrypt(n){return this.encrypt(n)}}ka=new WeakMap,s0=new WeakMap,Kt=new WeakMap;function xA(t){if(t.length<16)throw new TypeError("PKCS#7 invalid length");const n=t[t.length-1];if(n>16)throw new TypeError("PKCS#7 padding byte out of range");const i=t.length-n;for(let o=0;o<n;o++)if(t[i+o]!==n)throw new TypeError("PKCS#7 invalid padding byte");return new Uint8Array(t.subarray(0,i))}let Ig={data:""},Og=t=>typeof window=="object"?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||Ig,Mg=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Lg=/\/\*[^]*?\*\/|  +/g,Lh=/\n+/g,kn=(t,n)=>{let i="",o="",s="";for(let u in t){let x=t[u];u[0]=="@"?u[1]=="i"?i=u+" "+x+";":o+=u[1]=="f"?kn(x,u):u+"{"+kn(x,u[1]=="k"?"":n)+"}":typeof x=="object"?o+=kn(x,n?n.replace(/([^,])+/g,c=>u.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,h=>/&/.test(h)?h.replace(/&/g,c):c?c+" "+h:h)):u):x!=null&&(u=/^--/.test(u)?u:u.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=kn.p?kn.p(u,x):u+":"+x+";")}return i+(n&&s?n+"{"+s+"}":s)+o},Ur={},I4=t=>{if(typeof t=="object"){let n="";for(let i in t)n+=i+I4(t[i]);return n}return t},Ng=(t,n,i,o,s)=>{let u=I4(t),x=Ur[u]||(Ur[u]=(h=>{let f=0,p=11;for(;f<h.length;)p=101*p+h.charCodeAt(f++)>>>0;return"go"+p})(u));if(!Ur[x]){let h=u!==t?t:(f=>{let p,m,y=[{}];for(;p=Mg.exec(f.replace(Lg,""));)p[4]?y.shift():p[3]?(m=p[3].replace(Lh," ").trim(),y.unshift(y[0][m]=y[0][m]||{})):y[0][p[1]]=p[2].replace(Lh," ").trim();return y[0]})(t);Ur[x]=kn(s?{["@keyframes "+x]:h}:h,i?"":"."+x)}let c=i&&Ur.g?Ur.g:null;return i&&(Ur.g=Ur[x]),((h,f,p,m)=>{m?f.data=f.data.replace(m,h):f.data.indexOf(h)===-1&&(f.data=p?h+f.data:f.data+h)})(Ur[x],n,o,c),x},Tg=(t,n,i)=>t.reduce((o,s,u)=>{let x=n[u];if(x&&x.call){let c=x(i),h=c&&c.props&&c.props.className||/^go/.test(c)&&c;x=h?"."+h:c&&typeof c=="object"?c.props?"":kn(c,""):c===!1?"":c}return o+s+(x??"")},"");function Sl(t){let n=this||{},i=t.call?t(n.p):t;return Ng(i.unshift?i.raw?Tg(i,[].slice.call(arguments,1),n.p):i.reduce((o,s)=>Object.assign(o,s&&s.call?s(n.p):s),{}):i,Og(n.target),n.g,n.o,n.k)}let O4,pf,vf;Sl.bind({g:1});let Zr=Sl.bind({k:1});function zg(t,n,i,o){kn.p=n,O4=t,pf=i,vf=o}function Un(t,n){let i=this||{};return function(){let o=arguments;function s(u,x){let c=Object.assign({},u),h=c.className||s.className;i.p=Object.assign({theme:pf&&pf()},c),i.o=/ *go\d+/.test(h),c.className=Sl.apply(i,o)+(h?" "+h:"");let f=t;return t[0]&&(f=c.as||t,delete c.as),vf&&f[0]&&vf(c),O4(f,c)}return s}}var Hg=t=>typeof t=="function",za=(t,n)=>Hg(t)?t(n):t,Ug=(()=>{let t=0;return()=>(++t).toString()})(),M4=(()=>{let t;return()=>{if(t===void 0&&typeof window<"u"){let n=matchMedia("(prefers-reduced-motion: reduce)");t=!n||n.matches}return t}})(),Qg=20,L4=(t,n)=>{switch(n.type){case 0:return{...t,toasts:[n.toast,...t.toasts].slice(0,Qg)};case 1:return{...t,toasts:t.toasts.map(u=>u.id===n.toast.id?{...u,...n.toast}:u)};case 2:let{toast:i}=n;return L4(t,{type:t.toasts.find(u=>u.id===i.id)?1:0,toast:i});case 3:let{toastId:o}=n;return{...t,toasts:t.toasts.map(u=>u.id===o||o===void 0?{...u,dismissed:!0,visible:!1}:u)};case 4:return n.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(u=>u.id!==n.toastId)};case 5:return{...t,pausedAt:n.time};case 6:let s=n.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(u=>({...u,pauseDuration:u.pauseDuration+s}))}}},Ss=[],f0={toasts:[],pausedAt:void 0},D0=t=>{f0=L4(f0,t),Ss.forEach(n=>{n(f0)})},jg={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},N4=(t={})=>{let[n,i]=U.useState(f0),o=U.useRef(f0);U.useEffect(()=>(o.current!==f0&&i(f0),Ss.push(i),()=>{let u=Ss.indexOf(i);u>-1&&Ss.splice(u,1)}),[]);let s=n.toasts.map(u=>{var x,c,h;return{...t,...t[u.type],...u,removeDelay:u.removeDelay||((x=t[u.type])==null?void 0:x.removeDelay)||t?.removeDelay,duration:u.duration||((c=t[u.type])==null?void 0:c.duration)||t?.duration||jg[u.type],style:{...t.style,...(h=t[u.type])==null?void 0:h.style,...u.style}}});return{...n,toasts:s}},Wg=(t,n="blank",i)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:n,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...i,id:i?.id||Ug()}),$a=t=>(n,i)=>{let o=Wg(n,t,i);return D0({type:2,toast:o}),o.id},ht=(t,n)=>$a("blank")(t,n);ht.error=$a("error");ht.success=$a("success");ht.loading=$a("loading");ht.custom=$a("custom");ht.dismiss=t=>{D0({type:3,toastId:t})};ht.remove=t=>D0({type:4,toastId:t});ht.promise=(t,n,i)=>{let o=ht.loading(n.loading,{...i,...i?.loading});return typeof t=="function"&&(t=t()),t.then(s=>{let u=n.success?za(n.success,s):void 0;return u?ht.success(u,{id:o,...i,...i?.success}):ht.dismiss(o),s}).catch(s=>{let u=n.error?za(n.error,s):void 0;u?ht.error(u,{id:o,...i,...i?.error}):ht.dismiss(o)}),t};var Vg=(t,n)=>{D0({type:1,toast:{id:t,height:n}})},qg=()=>{D0({type:5,time:Date.now()})},Pa=new Map,Kg=1e3,$g=(t,n=Kg)=>{if(Pa.has(t))return;let i=setTimeout(()=>{Pa.delete(t),D0({type:4,toastId:t})},n);Pa.set(t,i)},T4=t=>{let{toasts:n,pausedAt:i}=N4(t);U.useEffect(()=>{if(i)return;let u=Date.now(),x=n.map(c=>{if(c.duration===1/0)return;let h=(c.duration||0)+c.pauseDuration-(u-c.createdAt);if(h<0){c.visible&&ht.dismiss(c.id);return}return setTimeout(()=>ht.dismiss(c.id),h)});return()=>{x.forEach(c=>c&&clearTimeout(c))}},[n,i]);let o=U.useCallback(()=>{i&&D0({type:6,time:Date.now()})},[i]),s=U.useCallback((u,x)=>{let{reverseOrder:c=!1,gutter:h=8,defaultPosition:f}=x||{},p=n.filter(A=>(A.position||f)===(u.position||f)&&A.height),m=p.findIndex(A=>A.id===u.id),y=p.filter((A,w)=>w<m&&A.visible).length;return p.filter(A=>A.visible).slice(...c?[y+1]:[0,y]).reduce((A,w)=>A+(w.height||0)+h,0)},[n]);return U.useEffect(()=>{n.forEach(u=>{if(u.dismissed)$g(u.id,u.removeDelay);else{let x=Pa.get(u.id);x&&(clearTimeout(x),Pa.delete(u.id))}})},[n]),{toasts:n,handlers:{updateHeight:Vg,startPause:qg,endPause:o,calculateOffset:s}}},Gg=Zr`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Zg=Zr`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Yg=Zr`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,z4=Un("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Gg} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Zg} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Yg} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Xg=Zr`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,H4=Un("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${Xg} 1s linear infinite;
`,Jg=Zr`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,eb=Zr`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,U4=Un("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Jg} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${eb} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,tb=Un("div")`
  position: absolute;
`,rb=Un("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,nb=Zr`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ib=Un("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${nb} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Q4=({toast:t})=>{let{icon:n,type:i,iconTheme:o}=t;return n!==void 0?typeof n=="string"?U.createElement(ib,null,n):n:i==="blank"?null:U.createElement(rb,null,U.createElement(H4,{...o}),i!=="loading"&&U.createElement(tb,null,i==="error"?U.createElement(z4,{...o}):U.createElement(U4,{...o})))},ab=t=>`
0% {transform: translate3d(0,${t*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ob=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${t*-150}%,-1px) scale(.6); opacity:0;}
`,sb="0%{opacity:0;} 100%{opacity:1;}",lb="0%{opacity:1;} 100%{opacity:0;}",ub=Un("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,cb=Un("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,fb=(t,n)=>{let i=t.includes("top")?1:-1,[o,s]=M4()?[sb,lb]:[ab(i),ob(i)];return{animation:n?`${Zr(o)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Zr(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},j4=U.memo(({toast:t,position:n,style:i,children:o})=>{let s=t.height?fb(t.position||n||"top-center",t.visible):{opacity:0},u=U.createElement(Q4,{toast:t}),x=U.createElement(cb,{...t.ariaProps},za(t.message,t));return U.createElement(ub,{className:t.className,style:{...s,...i,...t.style}},typeof o=="function"?o({icon:u,message:x}):U.createElement(U.Fragment,null,u,x))});zg(U.createElement);var xb=({id:t,className:n,style:i,onHeightUpdate:o,children:s})=>{let u=U.useCallback(x=>{if(x){let c=()=>{let h=x.getBoundingClientRect().height;o(t,h)};c(),new MutationObserver(c).observe(x,{subtree:!0,childList:!0,characterData:!0})}},[t,o]);return U.createElement("div",{ref:u,className:n,style:i},s)},db=(t,n)=>{let i=t.includes("top"),o=i?{top:0}:{bottom:0},s=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:M4()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${n*(i?1:-1)}px)`,...o,...s}},hb=Sl`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ys=16,pb=({reverseOrder:t,position:n="top-center",toastOptions:i,gutter:o,children:s,containerStyle:u,containerClassName:x})=>{let{toasts:c,handlers:h}=T4(i);return U.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:ys,left:ys,right:ys,bottom:ys,pointerEvents:"none",...u},className:x,onMouseEnter:h.startPause,onMouseLeave:h.endPause},c.map(f=>{let p=f.position||n,m=h.calculateOffset(f,{reverseOrder:t,gutter:o,defaultPosition:n}),y=db(p,m);return U.createElement(xb,{id:f.id,key:f.id,onHeightUpdate:h.updateHeight,className:f.visible?hb:"",style:y},f.type==="custom"?za(f.message,f):s?s(f):U.createElement(j4,{toast:f,position:p}))}))},vb=ht;const dA=Object.freeze(Object.defineProperty({__proto__:null,CheckmarkIcon:U4,ErrorIcon:z4,LoaderIcon:H4,ToastBar:j4,ToastIcon:Q4,Toaster:pb,default:vb,resolveValue:za,toast:ht,useToaster:T4,useToasterStore:N4},Symbol.toStringTag,{value:"Module"}));var Ds={exports:{}};function mb(t){throw new Error('Could not dynamically require "'+t+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Fs={exports:{}};const yb={},gb=Object.freeze(Object.defineProperty({__proto__:null,default:yb},Symbol.toStringTag,{value:"Module"})),bb=U3(gb);var Ab=Fs.exports,Nh;function Be(){return Nh||(Nh=1,function(t,n){(function(i,o){t.exports=o()})(Ab,function(){var i=i||function(o,s){var u;if(typeof window<"u"&&window.crypto&&(u=window.crypto),typeof self<"u"&&self.crypto&&(u=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(u=globalThis.crypto),!u&&typeof window<"u"&&window.msCrypto&&(u=window.msCrypto),!u&&typeof window<"u"&&window.crypto&&(u=window.crypto),!u&&typeof mb=="function")try{u=bb}catch{}var x=function(){if(u){if(typeof u.getRandomValues=="function")try{return u.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof u.randomBytes=="function")try{return u.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},c=Object.create||function(){function B(){}return function(E){var D;return B.prototype=E,D=new B,B.prototype=null,D}}(),h={},f=h.lib={},p=f.Base=function(){return{extend:function(B){var E=c(this);return B&&E.mixIn(B),(!E.hasOwnProperty("init")||this.init===E.init)&&(E.init=function(){E.$super.init.apply(this,arguments)}),E.init.prototype=E,E.$super=this,E},create:function(){var B=this.extend();return B.init.apply(B,arguments),B},init:function(){},mixIn:function(B){for(var E in B)B.hasOwnProperty(E)&&(this[E]=B[E]);B.hasOwnProperty("toString")&&(this.toString=B.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),m=f.WordArray=p.extend({init:function(B,E){B=this.words=B||[],E!=s?this.sigBytes=E:this.sigBytes=B.length*4},toString:function(B){return(B||A).stringify(this)},concat:function(B){var E=this.words,D=B.words,k=this.sigBytes,R=B.sigBytes;if(this.clamp(),k%4)for(var I=0;I<R;I++){var O=D[I>>>2]>>>24-I%4*8&255;E[k+I>>>2]|=O<<24-(k+I)%4*8}else for(var Q=0;Q<R;Q+=4)E[k+Q>>>2]=D[Q>>>2];return this.sigBytes+=R,this},clamp:function(){var B=this.words,E=this.sigBytes;B[E>>>2]&=4294967295<<32-E%4*8,B.length=o.ceil(E/4)},clone:function(){var B=p.clone.call(this);return B.words=this.words.slice(0),B},random:function(B){for(var E=[],D=0;D<B;D+=4)E.push(x());return new m.init(E,B)}}),y=h.enc={},A=y.Hex={stringify:function(B){for(var E=B.words,D=B.sigBytes,k=[],R=0;R<D;R++){var I=E[R>>>2]>>>24-R%4*8&255;k.push((I>>>4).toString(16)),k.push((I&15).toString(16))}return k.join("")},parse:function(B){for(var E=B.length,D=[],k=0;k<E;k+=2)D[k>>>3]|=parseInt(B.substr(k,2),16)<<24-k%8*4;return new m.init(D,E/2)}},w=y.Latin1={stringify:function(B){for(var E=B.words,D=B.sigBytes,k=[],R=0;R<D;R++){var I=E[R>>>2]>>>24-R%4*8&255;k.push(String.fromCharCode(I))}return k.join("")},parse:function(B){for(var E=B.length,D=[],k=0;k<E;k++)D[k>>>2]|=(B.charCodeAt(k)&255)<<24-k%4*8;return new m.init(D,E)}},g=y.Utf8={stringify:function(B){try{return decodeURIComponent(escape(w.stringify(B)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(B){return w.parse(unescape(encodeURIComponent(B)))}},b=f.BufferedBlockAlgorithm=p.extend({reset:function(){this._data=new m.init,this._nDataBytes=0},_append:function(B){typeof B=="string"&&(B=g.parse(B)),this._data.concat(B),this._nDataBytes+=B.sigBytes},_process:function(B){var E,D=this._data,k=D.words,R=D.sigBytes,I=this.blockSize,O=I*4,Q=R/O;B?Q=o.ceil(Q):Q=o.max((Q|0)-this._minBufferSize,0);var _=Q*I,T=o.min(_*4,R);if(_){for(var q=0;q<_;q+=I)this._doProcessBlock(k,q);E=k.splice(0,_),D.sigBytes-=T}return new m.init(E,T)},clone:function(){var B=p.clone.call(this);return B._data=this._data.clone(),B},_minBufferSize:0});f.Hasher=b.extend({cfg:p.extend(),init:function(B){this.cfg=this.cfg.extend(B),this.reset()},reset:function(){b.reset.call(this),this._doReset()},update:function(B){return this._append(B),this._process(),this},finalize:function(B){B&&this._append(B);var E=this._doFinalize();return E},blockSize:16,_createHelper:function(B){return function(E,D){return new B.init(D).finalize(E)}},_createHmacHelper:function(B){return function(E,D){return new S.HMAC.init(B,D).finalize(E)}}});var S=h.algo={};return h}(Math);return i})}(Fs)),Fs.exports}var ks={exports:{}},wb=ks.exports,Th;function Dl(){return Th||(Th=1,function(t,n){(function(i,o){t.exports=o(Be())})(wb,function(i){return function(o){var s=i,u=s.lib,x=u.Base,c=u.WordArray,h=s.x64={};h.Word=x.extend({init:function(f,p){this.high=f,this.low=p}}),h.WordArray=x.extend({init:function(f,p){f=this.words=f||[],p!=o?this.sigBytes=p:this.sigBytes=f.length*8},toX32:function(){for(var f=this.words,p=f.length,m=[],y=0;y<p;y++){var A=f[y];m.push(A.high),m.push(A.low)}return c.create(m,this.sigBytes)},clone:function(){for(var f=x.clone.call(this),p=f.words=this.words.slice(0),m=p.length,y=0;y<m;y++)p[y]=p[y].clone();return f}})}(),i})}(ks)),ks.exports}var Rs={exports:{}},Eb=Rs.exports,zh;function Cb(){return zh||(zh=1,function(t,n){(function(i,o){t.exports=o(Be())})(Eb,function(i){return function(){if(typeof ArrayBuffer=="function"){var o=i,s=o.lib,u=s.WordArray,x=u.init,c=u.init=function(h){if(h instanceof ArrayBuffer&&(h=new Uint8Array(h)),(h instanceof Int8Array||typeof Uint8ClampedArray<"u"&&h instanceof Uint8ClampedArray||h instanceof Int16Array||h instanceof Uint16Array||h instanceof Int32Array||h instanceof Uint32Array||h instanceof Float32Array||h instanceof Float64Array)&&(h=new Uint8Array(h.buffer,h.byteOffset,h.byteLength)),h instanceof Uint8Array){for(var f=h.byteLength,p=[],m=0;m<f;m++)p[m>>>2]|=h[m]<<24-m%4*8;x.call(this,p,f)}else x.apply(this,arguments)};c.prototype=u}}(),i.lib.WordArray})}(Rs)),Rs.exports}var Ps={exports:{}},Bb=Ps.exports,Hh;function Sb(){return Hh||(Hh=1,function(t,n){(function(i,o){t.exports=o(Be())})(Bb,function(i){return function(){var o=i,s=o.lib,u=s.WordArray,x=o.enc;x.Utf16=x.Utf16BE={stringify:function(h){for(var f=h.words,p=h.sigBytes,m=[],y=0;y<p;y+=2){var A=f[y>>>2]>>>16-y%4*8&65535;m.push(String.fromCharCode(A))}return m.join("")},parse:function(h){for(var f=h.length,p=[],m=0;m<f;m++)p[m>>>1]|=h.charCodeAt(m)<<16-m%2*16;return u.create(p,f*2)}},x.Utf16LE={stringify:function(h){for(var f=h.words,p=h.sigBytes,m=[],y=0;y<p;y+=2){var A=c(f[y>>>2]>>>16-y%4*8&65535);m.push(String.fromCharCode(A))}return m.join("")},parse:function(h){for(var f=h.length,p=[],m=0;m<f;m++)p[m>>>1]|=c(h.charCodeAt(m)<<16-m%2*16);return u.create(p,f*2)}};function c(h){return h<<8&4278255360|h>>>8&16711935}}(),i.enc.Utf16})}(Ps)),Ps.exports}var _s={exports:{}},Db=_s.exports,Uh;function F0(){return Uh||(Uh=1,function(t,n){(function(i,o){t.exports=o(Be())})(Db,function(i){return function(){var o=i,s=o.lib,u=s.WordArray,x=o.enc;x.Base64={stringify:function(h){var f=h.words,p=h.sigBytes,m=this._map;h.clamp();for(var y=[],A=0;A<p;A+=3)for(var w=f[A>>>2]>>>24-A%4*8&255,g=f[A+1>>>2]>>>24-(A+1)%4*8&255,b=f[A+2>>>2]>>>24-(A+2)%4*8&255,S=w<<16|g<<8|b,B=0;B<4&&A+B*.75<p;B++)y.push(m.charAt(S>>>6*(3-B)&63));var E=m.charAt(64);if(E)for(;y.length%4;)y.push(E);return y.join("")},parse:function(h){var f=h.length,p=this._map,m=this._reverseMap;if(!m){m=this._reverseMap=[];for(var y=0;y<p.length;y++)m[p.charCodeAt(y)]=y}var A=p.charAt(64);if(A){var w=h.indexOf(A);w!==-1&&(f=w)}return c(h,f,m)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function c(h,f,p){for(var m=[],y=0,A=0;A<f;A++)if(A%4){var w=p[h.charCodeAt(A-1)]<<A%4*2,g=p[h.charCodeAt(A)]>>>6-A%4*2,b=w|g;m[y>>>2]|=b<<24-y%4*8,y++}return u.create(m,y)}}(),i.enc.Base64})}(_s)),_s.exports}var Is={exports:{}},Fb=Is.exports,Qh;function kb(){return Qh||(Qh=1,function(t,n){(function(i,o){t.exports=o(Be())})(Fb,function(i){return function(){var o=i,s=o.lib,u=s.WordArray,x=o.enc;x.Base64url={stringify:function(h,f){f===void 0&&(f=!0);var p=h.words,m=h.sigBytes,y=f?this._safe_map:this._map;h.clamp();for(var A=[],w=0;w<m;w+=3)for(var g=p[w>>>2]>>>24-w%4*8&255,b=p[w+1>>>2]>>>24-(w+1)%4*8&255,S=p[w+2>>>2]>>>24-(w+2)%4*8&255,B=g<<16|b<<8|S,E=0;E<4&&w+E*.75<m;E++)A.push(y.charAt(B>>>6*(3-E)&63));var D=y.charAt(64);if(D)for(;A.length%4;)A.push(D);return A.join("")},parse:function(h,f){f===void 0&&(f=!0);var p=h.length,m=f?this._safe_map:this._map,y=this._reverseMap;if(!y){y=this._reverseMap=[];for(var A=0;A<m.length;A++)y[m.charCodeAt(A)]=A}var w=m.charAt(64);if(w){var g=h.indexOf(w);g!==-1&&(p=g)}return c(h,p,y)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"};function c(h,f,p){for(var m=[],y=0,A=0;A<f;A++)if(A%4){var w=p[h.charCodeAt(A-1)]<<A%4*2,g=p[h.charCodeAt(A)]>>>6-A%4*2,b=w|g;m[y>>>2]|=b<<24-y%4*8,y++}return u.create(m,y)}}(),i.enc.Base64url})}(Is)),Is.exports}var Os={exports:{}},Rb=Os.exports,jh;function k0(){return jh||(jh=1,function(t,n){(function(i,o){t.exports=o(Be())})(Rb,function(i){return function(o){var s=i,u=s.lib,x=u.WordArray,c=u.Hasher,h=s.algo,f=[];(function(){for(var g=0;g<64;g++)f[g]=o.abs(o.sin(g+1))*4294967296|0})();var p=h.MD5=c.extend({_doReset:function(){this._hash=new x.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(g,b){for(var S=0;S<16;S++){var B=b+S,E=g[B];g[B]=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360}var D=this._hash.words,k=g[b+0],R=g[b+1],I=g[b+2],O=g[b+3],Q=g[b+4],_=g[b+5],T=g[b+6],q=g[b+7],$=g[b+8],ae=g[b+9],K=g[b+10],Y=g[b+11],ne=g[b+12],Z=g[b+13],J=g[b+14],X=g[b+15],F=D[0],M=D[1],G=D[2],W=D[3];F=m(F,M,G,W,k,7,f[0]),W=m(W,F,M,G,R,12,f[1]),G=m(G,W,F,M,I,17,f[2]),M=m(M,G,W,F,O,22,f[3]),F=m(F,M,G,W,Q,7,f[4]),W=m(W,F,M,G,_,12,f[5]),G=m(G,W,F,M,T,17,f[6]),M=m(M,G,W,F,q,22,f[7]),F=m(F,M,G,W,$,7,f[8]),W=m(W,F,M,G,ae,12,f[9]),G=m(G,W,F,M,K,17,f[10]),M=m(M,G,W,F,Y,22,f[11]),F=m(F,M,G,W,ne,7,f[12]),W=m(W,F,M,G,Z,12,f[13]),G=m(G,W,F,M,J,17,f[14]),M=m(M,G,W,F,X,22,f[15]),F=y(F,M,G,W,R,5,f[16]),W=y(W,F,M,G,T,9,f[17]),G=y(G,W,F,M,Y,14,f[18]),M=y(M,G,W,F,k,20,f[19]),F=y(F,M,G,W,_,5,f[20]),W=y(W,F,M,G,K,9,f[21]),G=y(G,W,F,M,X,14,f[22]),M=y(M,G,W,F,Q,20,f[23]),F=y(F,M,G,W,ae,5,f[24]),W=y(W,F,M,G,J,9,f[25]),G=y(G,W,F,M,O,14,f[26]),M=y(M,G,W,F,$,20,f[27]),F=y(F,M,G,W,Z,5,f[28]),W=y(W,F,M,G,I,9,f[29]),G=y(G,W,F,M,q,14,f[30]),M=y(M,G,W,F,ne,20,f[31]),F=A(F,M,G,W,_,4,f[32]),W=A(W,F,M,G,$,11,f[33]),G=A(G,W,F,M,Y,16,f[34]),M=A(M,G,W,F,J,23,f[35]),F=A(F,M,G,W,R,4,f[36]),W=A(W,F,M,G,Q,11,f[37]),G=A(G,W,F,M,q,16,f[38]),M=A(M,G,W,F,K,23,f[39]),F=A(F,M,G,W,Z,4,f[40]),W=A(W,F,M,G,k,11,f[41]),G=A(G,W,F,M,O,16,f[42]),M=A(M,G,W,F,T,23,f[43]),F=A(F,M,G,W,ae,4,f[44]),W=A(W,F,M,G,ne,11,f[45]),G=A(G,W,F,M,X,16,f[46]),M=A(M,G,W,F,I,23,f[47]),F=w(F,M,G,W,k,6,f[48]),W=w(W,F,M,G,q,10,f[49]),G=w(G,W,F,M,J,15,f[50]),M=w(M,G,W,F,_,21,f[51]),F=w(F,M,G,W,ne,6,f[52]),W=w(W,F,M,G,O,10,f[53]),G=w(G,W,F,M,K,15,f[54]),M=w(M,G,W,F,R,21,f[55]),F=w(F,M,G,W,$,6,f[56]),W=w(W,F,M,G,X,10,f[57]),G=w(G,W,F,M,T,15,f[58]),M=w(M,G,W,F,Z,21,f[59]),F=w(F,M,G,W,Q,6,f[60]),W=w(W,F,M,G,Y,10,f[61]),G=w(G,W,F,M,I,15,f[62]),M=w(M,G,W,F,ae,21,f[63]),D[0]=D[0]+F|0,D[1]=D[1]+M|0,D[2]=D[2]+G|0,D[3]=D[3]+W|0},_doFinalize:function(){var g=this._data,b=g.words,S=this._nDataBytes*8,B=g.sigBytes*8;b[B>>>5]|=128<<24-B%32;var E=o.floor(S/4294967296),D=S;b[(B+64>>>9<<4)+15]=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360,b[(B+64>>>9<<4)+14]=(D<<8|D>>>24)&16711935|(D<<24|D>>>8)&4278255360,g.sigBytes=(b.length+1)*4,this._process();for(var k=this._hash,R=k.words,I=0;I<4;I++){var O=R[I];R[I]=(O<<8|O>>>24)&16711935|(O<<24|O>>>8)&4278255360}return k},clone:function(){var g=c.clone.call(this);return g._hash=this._hash.clone(),g}});function m(g,b,S,B,E,D,k){var R=g+(b&S|~b&B)+E+k;return(R<<D|R>>>32-D)+b}function y(g,b,S,B,E,D,k){var R=g+(b&B|S&~B)+E+k;return(R<<D|R>>>32-D)+b}function A(g,b,S,B,E,D,k){var R=g+(b^S^B)+E+k;return(R<<D|R>>>32-D)+b}function w(g,b,S,B,E,D,k){var R=g+(S^(b|~B))+E+k;return(R<<D|R>>>32-D)+b}s.MD5=c._createHelper(p),s.HmacMD5=c._createHmacHelper(p)}(Math),i.MD5})}(Os)),Os.exports}var Ms={exports:{}},Pb=Ms.exports,Wh;function W4(){return Wh||(Wh=1,function(t,n){(function(i,o){t.exports=o(Be())})(Pb,function(i){return function(){var o=i,s=o.lib,u=s.WordArray,x=s.Hasher,c=o.algo,h=[],f=c.SHA1=x.extend({_doReset:function(){this._hash=new u.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(p,m){for(var y=this._hash.words,A=y[0],w=y[1],g=y[2],b=y[3],S=y[4],B=0;B<80;B++){if(B<16)h[B]=p[m+B]|0;else{var E=h[B-3]^h[B-8]^h[B-14]^h[B-16];h[B]=E<<1|E>>>31}var D=(A<<5|A>>>27)+S+h[B];B<20?D+=(w&g|~w&b)+1518500249:B<40?D+=(w^g^b)+1859775393:B<60?D+=(w&g|w&b|g&b)-1894007588:D+=(w^g^b)-899497514,S=b,b=g,g=w<<30|w>>>2,w=A,A=D}y[0]=y[0]+A|0,y[1]=y[1]+w|0,y[2]=y[2]+g|0,y[3]=y[3]+b|0,y[4]=y[4]+S|0},_doFinalize:function(){var p=this._data,m=p.words,y=this._nDataBytes*8,A=p.sigBytes*8;return m[A>>>5]|=128<<24-A%32,m[(A+64>>>9<<4)+14]=Math.floor(y/4294967296),m[(A+64>>>9<<4)+15]=y,p.sigBytes=m.length*4,this._process(),this._hash},clone:function(){var p=x.clone.call(this);return p._hash=this._hash.clone(),p}});o.SHA1=x._createHelper(f),o.HmacSHA1=x._createHmacHelper(f)}(),i.SHA1})}(Ms)),Ms.exports}var Ls={exports:{}},_b=Ls.exports,Vh;function Yf(){return Vh||(Vh=1,function(t,n){(function(i,o){t.exports=o(Be())})(_b,function(i){return function(o){var s=i,u=s.lib,x=u.WordArray,c=u.Hasher,h=s.algo,f=[],p=[];(function(){function A(S){for(var B=o.sqrt(S),E=2;E<=B;E++)if(!(S%E))return!1;return!0}function w(S){return(S-(S|0))*4294967296|0}for(var g=2,b=0;b<64;)A(g)&&(b<8&&(f[b]=w(o.pow(g,1/2))),p[b]=w(o.pow(g,1/3)),b++),g++})();var m=[],y=h.SHA256=c.extend({_doReset:function(){this._hash=new x.init(f.slice(0))},_doProcessBlock:function(A,w){for(var g=this._hash.words,b=g[0],S=g[1],B=g[2],E=g[3],D=g[4],k=g[5],R=g[6],I=g[7],O=0;O<64;O++){if(O<16)m[O]=A[w+O]|0;else{var Q=m[O-15],_=(Q<<25|Q>>>7)^(Q<<14|Q>>>18)^Q>>>3,T=m[O-2],q=(T<<15|T>>>17)^(T<<13|T>>>19)^T>>>10;m[O]=_+m[O-7]+q+m[O-16]}var $=D&k^~D&R,ae=b&S^b&B^S&B,K=(b<<30|b>>>2)^(b<<19|b>>>13)^(b<<10|b>>>22),Y=(D<<26|D>>>6)^(D<<21|D>>>11)^(D<<7|D>>>25),ne=I+Y+$+p[O]+m[O],Z=K+ae;I=R,R=k,k=D,D=E+ne|0,E=B,B=S,S=b,b=ne+Z|0}g[0]=g[0]+b|0,g[1]=g[1]+S|0,g[2]=g[2]+B|0,g[3]=g[3]+E|0,g[4]=g[4]+D|0,g[5]=g[5]+k|0,g[6]=g[6]+R|0,g[7]=g[7]+I|0},_doFinalize:function(){var A=this._data,w=A.words,g=this._nDataBytes*8,b=A.sigBytes*8;return w[b>>>5]|=128<<24-b%32,w[(b+64>>>9<<4)+14]=o.floor(g/4294967296),w[(b+64>>>9<<4)+15]=g,A.sigBytes=w.length*4,this._process(),this._hash},clone:function(){var A=c.clone.call(this);return A._hash=this._hash.clone(),A}});s.SHA256=c._createHelper(y),s.HmacSHA256=c._createHmacHelper(y)}(Math),i.SHA256})}(Ls)),Ls.exports}var Ns={exports:{}},Ib=Ns.exports,qh;function Ob(){return qh||(qh=1,function(t,n){(function(i,o,s){t.exports=o(Be(),Yf())})(Ib,function(i){return function(){var o=i,s=o.lib,u=s.WordArray,x=o.algo,c=x.SHA256,h=x.SHA224=c.extend({_doReset:function(){this._hash=new u.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var f=c._doFinalize.call(this);return f.sigBytes-=4,f}});o.SHA224=c._createHelper(h),o.HmacSHA224=c._createHmacHelper(h)}(),i.SHA224})}(Ns)),Ns.exports}var Ts={exports:{}},Mb=Ts.exports,Kh;function V4(){return Kh||(Kh=1,function(t,n){(function(i,o,s){t.exports=o(Be(),Dl())})(Mb,function(i){return function(){var o=i,s=o.lib,u=s.Hasher,x=o.x64,c=x.Word,h=x.WordArray,f=o.algo;function p(){return c.create.apply(c,arguments)}var m=[p(1116352408,3609767458),p(1899447441,602891725),p(3049323471,3964484399),p(3921009573,2173295548),p(961987163,4081628472),p(1508970993,3053834265),p(2453635748,2937671579),p(2870763221,3664609560),p(3624381080,2734883394),p(310598401,1164996542),p(607225278,1323610764),p(1426881987,3590304994),p(1925078388,4068182383),p(2162078206,991336113),p(2614888103,633803317),p(3248222580,3479774868),p(3835390401,2666613458),p(4022224774,944711139),p(264347078,2341262773),p(604807628,2007800933),p(770255983,1495990901),p(1249150122,1856431235),p(1555081692,3175218132),p(1996064986,2198950837),p(2554220882,3999719339),p(2821834349,766784016),p(2952996808,2566594879),p(3210313671,3203337956),p(3336571891,1034457026),p(3584528711,2466948901),p(113926993,3758326383),p(338241895,168717936),p(666307205,1188179964),p(773529912,1546045734),p(1294757372,1522805485),p(1396182291,2643833823),p(1695183700,2343527390),p(1986661051,1014477480),p(2177026350,1206759142),p(2456956037,344077627),p(2730485921,1290863460),p(2820302411,3158454273),p(3259730800,3505952657),p(3345764771,106217008),p(3516065817,3606008344),p(3600352804,1432725776),p(4094571909,1467031594),p(275423344,851169720),p(430227734,3100823752),p(506948616,1363258195),p(659060556,3750685593),p(883997877,3785050280),p(958139571,3318307427),p(1322822218,3812723403),p(1537002063,2003034995),p(1747873779,3602036899),p(1955562222,1575990012),p(2024104815,1125592928),p(2227730452,2716904306),p(2361852424,442776044),p(2428436474,593698344),p(2756734187,3733110249),p(3204031479,2999351573),p(3329325298,3815920427),p(3391569614,3928383900),p(3515267271,566280711),p(3940187606,3454069534),p(4118630271,4000239992),p(116418474,1914138554),p(174292421,2731055270),p(289380356,3203993006),p(460393269,320620315),p(685471733,587496836),p(852142971,1086792851),p(1017036298,365543100),p(1126000580,2618297676),p(1288033470,3409855158),p(1501505948,4234509866),p(1607167915,987167468),p(1816402316,1246189591)],y=[];(function(){for(var w=0;w<80;w++)y[w]=p()})();var A=f.SHA512=u.extend({_doReset:function(){this._hash=new h.init([new c.init(1779033703,4089235720),new c.init(3144134277,2227873595),new c.init(1013904242,4271175723),new c.init(2773480762,1595750129),new c.init(1359893119,2917565137),new c.init(2600822924,725511199),new c.init(528734635,4215389547),new c.init(1541459225,327033209)])},_doProcessBlock:function(w,g){for(var b=this._hash.words,S=b[0],B=b[1],E=b[2],D=b[3],k=b[4],R=b[5],I=b[6],O=b[7],Q=S.high,_=S.low,T=B.high,q=B.low,$=E.high,ae=E.low,K=D.high,Y=D.low,ne=k.high,Z=k.low,J=R.high,X=R.low,F=I.high,M=I.low,G=O.high,W=O.low,fe=Q,ce=_,ye=T,oe=q,we=$,Pe=ae,Jt=K,$e=Y,Te=ne,Ue=Z,vr=J,er=X,Jr=F,en=M,jn=G,tr=W,Je=0;Je<80;Je++){var lt,rr,Wn=y[Je];if(Je<16)rr=Wn.high=w[g+Je*2]|0,lt=Wn.low=w[g+Je*2+1]|0;else{var Ii=y[Je-15],kr=Ii.high,Rr=Ii.low,R0=(kr>>>1|Rr<<31)^(kr>>>8|Rr<<24)^kr>>>7,Oi=(Rr>>>1|kr<<31)^(Rr>>>8|kr<<24)^(Rr>>>7|kr<<25),tn=y[Je-2],Lt=tn.high,Vn=tn.low,Ga=(Lt>>>19|Vn<<13)^(Lt<<3|Vn>>>29)^Lt>>>6,Mi=(Vn>>>19|Lt<<13)^(Vn<<3|Lt>>>29)^(Vn>>>6|Lt<<26),Za=y[Je-7],Li=Za.high,Ni=Za.low,P0=y[Je-16],Ti=P0.high,_0=P0.low;lt=Oi+Ni,rr=R0+Li+(lt>>>0<Oi>>>0?1:0),lt=lt+Mi,rr=rr+Ga+(lt>>>0<Mi>>>0?1:0),lt=lt+_0,rr=rr+Ti+(lt>>>0<_0>>>0?1:0),Wn.high=rr,Wn.low=lt}var rn=Te&vr^~Te&Jr,Pr=Ue&er^~Ue&en,Ya=fe&ye^fe&we^ye&we,Xa=ce&oe^ce&Pe^oe&Pe,Ja=(fe>>>28|ce<<4)^(fe<<30|ce>>>2)^(fe<<25|ce>>>7),zi=(ce>>>28|fe<<4)^(ce<<30|fe>>>2)^(ce<<25|fe>>>7),eo=(Te>>>14|Ue<<18)^(Te>>>18|Ue<<14)^(Te<<23|Ue>>>9),Hi=(Ue>>>14|Te<<18)^(Ue>>>18|Te<<14)^(Ue<<23|Te>>>9),Ui=m[Je],qn=Ui.high,I0=Ui.low,Ge=tr+Hi,mr=jn+eo+(Ge>>>0<tr>>>0?1:0),Ge=Ge+Pr,mr=mr+rn+(Ge>>>0<Pr>>>0?1:0),Ge=Ge+I0,mr=mr+qn+(Ge>>>0<I0>>>0?1:0),Ge=Ge+lt,mr=mr+rr+(Ge>>>0<lt>>>0?1:0),nn=zi+Xa,O0=Ja+Ya+(nn>>>0<zi>>>0?1:0);jn=Jr,tr=en,Jr=vr,en=er,vr=Te,er=Ue,Ue=$e+Ge|0,Te=Jt+mr+(Ue>>>0<$e>>>0?1:0)|0,Jt=we,$e=Pe,we=ye,Pe=oe,ye=fe,oe=ce,ce=Ge+nn|0,fe=mr+O0+(ce>>>0<Ge>>>0?1:0)|0}_=S.low=_+ce,S.high=Q+fe+(_>>>0<ce>>>0?1:0),q=B.low=q+oe,B.high=T+ye+(q>>>0<oe>>>0?1:0),ae=E.low=ae+Pe,E.high=$+we+(ae>>>0<Pe>>>0?1:0),Y=D.low=Y+$e,D.high=K+Jt+(Y>>>0<$e>>>0?1:0),Z=k.low=Z+Ue,k.high=ne+Te+(Z>>>0<Ue>>>0?1:0),X=R.low=X+er,R.high=J+vr+(X>>>0<er>>>0?1:0),M=I.low=M+en,I.high=F+Jr+(M>>>0<en>>>0?1:0),W=O.low=W+tr,O.high=G+jn+(W>>>0<tr>>>0?1:0)},_doFinalize:function(){var w=this._data,g=w.words,b=this._nDataBytes*8,S=w.sigBytes*8;g[S>>>5]|=128<<24-S%32,g[(S+128>>>10<<5)+30]=Math.floor(b/4294967296),g[(S+128>>>10<<5)+31]=b,w.sigBytes=g.length*4,this._process();var B=this._hash.toX32();return B},clone:function(){var w=u.clone.call(this);return w._hash=this._hash.clone(),w},blockSize:1024/32});o.SHA512=u._createHelper(A),o.HmacSHA512=u._createHmacHelper(A)}(),i.SHA512})}(Ts)),Ts.exports}var zs={exports:{}},Lb=zs.exports,$h;function Nb(){return $h||($h=1,function(t,n){(function(i,o,s){t.exports=o(Be(),Dl(),V4())})(Lb,function(i){return function(){var o=i,s=o.x64,u=s.Word,x=s.WordArray,c=o.algo,h=c.SHA512,f=c.SHA384=h.extend({_doReset:function(){this._hash=new x.init([new u.init(3418070365,3238371032),new u.init(1654270250,914150663),new u.init(2438529370,812702999),new u.init(355462360,4144912697),new u.init(1731405415,4290775857),new u.init(2394180231,1750603025),new u.init(3675008525,1694076839),new u.init(1203062813,3204075428)])},_doFinalize:function(){var p=h._doFinalize.call(this);return p.sigBytes-=16,p}});o.SHA384=h._createHelper(f),o.HmacSHA384=h._createHmacHelper(f)}(),i.SHA384})}(zs)),zs.exports}var Hs={exports:{}},Tb=Hs.exports,Gh;function zb(){return Gh||(Gh=1,function(t,n){(function(i,o,s){t.exports=o(Be(),Dl())})(Tb,function(i){return function(o){var s=i,u=s.lib,x=u.WordArray,c=u.Hasher,h=s.x64,f=h.Word,p=s.algo,m=[],y=[],A=[];(function(){for(var b=1,S=0,B=0;B<24;B++){m[b+5*S]=(B+1)*(B+2)/2%64;var E=S%5,D=(2*b+3*S)%5;b=E,S=D}for(var b=0;b<5;b++)for(var S=0;S<5;S++)y[b+5*S]=S+(2*b+3*S)%5*5;for(var k=1,R=0;R<24;R++){for(var I=0,O=0,Q=0;Q<7;Q++){if(k&1){var _=(1<<Q)-1;_<32?O^=1<<_:I^=1<<_-32}k&128?k=k<<1^113:k<<=1}A[R]=f.create(I,O)}})();var w=[];(function(){for(var b=0;b<25;b++)w[b]=f.create()})();var g=p.SHA3=c.extend({cfg:c.cfg.extend({outputLength:512}),_doReset:function(){for(var b=this._state=[],S=0;S<25;S++)b[S]=new f.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(b,S){for(var B=this._state,E=this.blockSize/2,D=0;D<E;D++){var k=b[S+2*D],R=b[S+2*D+1];k=(k<<8|k>>>24)&16711935|(k<<24|k>>>8)&4278255360,R=(R<<8|R>>>24)&16711935|(R<<24|R>>>8)&4278255360;var I=B[D];I.high^=R,I.low^=k}for(var O=0;O<24;O++){for(var Q=0;Q<5;Q++){for(var _=0,T=0,q=0;q<5;q++){var I=B[Q+5*q];_^=I.high,T^=I.low}var $=w[Q];$.high=_,$.low=T}for(var Q=0;Q<5;Q++)for(var ae=w[(Q+4)%5],K=w[(Q+1)%5],Y=K.high,ne=K.low,_=ae.high^(Y<<1|ne>>>31),T=ae.low^(ne<<1|Y>>>31),q=0;q<5;q++){var I=B[Q+5*q];I.high^=_,I.low^=T}for(var Z=1;Z<25;Z++){var _,T,I=B[Z],J=I.high,X=I.low,F=m[Z];F<32?(_=J<<F|X>>>32-F,T=X<<F|J>>>32-F):(_=X<<F-32|J>>>64-F,T=J<<F-32|X>>>64-F);var M=w[y[Z]];M.high=_,M.low=T}var G=w[0],W=B[0];G.high=W.high,G.low=W.low;for(var Q=0;Q<5;Q++)for(var q=0;q<5;q++){var Z=Q+5*q,I=B[Z],fe=w[Z],ce=w[(Q+1)%5+5*q],ye=w[(Q+2)%5+5*q];I.high=fe.high^~ce.high&ye.high,I.low=fe.low^~ce.low&ye.low}var I=B[0],oe=A[O];I.high^=oe.high,I.low^=oe.low}},_doFinalize:function(){var b=this._data,S=b.words;this._nDataBytes*8;var B=b.sigBytes*8,E=this.blockSize*32;S[B>>>5]|=1<<24-B%32,S[(o.ceil((B+1)/E)*E>>>5)-1]|=128,b.sigBytes=S.length*4,this._process();for(var D=this._state,k=this.cfg.outputLength/8,R=k/8,I=[],O=0;O<R;O++){var Q=D[O],_=Q.high,T=Q.low;_=(_<<8|_>>>24)&16711935|(_<<24|_>>>8)&4278255360,T=(T<<8|T>>>24)&16711935|(T<<24|T>>>8)&4278255360,I.push(T),I.push(_)}return new x.init(I,k)},clone:function(){for(var b=c.clone.call(this),S=b._state=this._state.slice(0),B=0;B<25;B++)S[B]=S[B].clone();return b}});s.SHA3=c._createHelper(g),s.HmacSHA3=c._createHmacHelper(g)}(Math),i.SHA3})}(Hs)),Hs.exports}var Us={exports:{}},Hb=Us.exports,Zh;function Ub(){return Zh||(Zh=1,function(t,n){(function(i,o){t.exports=o(Be())})(Hb,function(i){/** @preserve
			(c) 2012 by Cédric Mesnil. All rights reserved.

			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
			*/return function(o){var s=i,u=s.lib,x=u.WordArray,c=u.Hasher,h=s.algo,f=x.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),p=x.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),m=x.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),y=x.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),A=x.create([0,1518500249,1859775393,2400959708,2840853838]),w=x.create([1352829926,1548603684,1836072691,2053994217,0]),g=h.RIPEMD160=c.extend({_doReset:function(){this._hash=x.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(R,I){for(var O=0;O<16;O++){var Q=I+O,_=R[Q];R[Q]=(_<<8|_>>>24)&16711935|(_<<24|_>>>8)&4278255360}var T=this._hash.words,q=A.words,$=w.words,ae=f.words,K=p.words,Y=m.words,ne=y.words,Z,J,X,F,M,G,W,fe,ce,ye;G=Z=T[0],W=J=T[1],fe=X=T[2],ce=F=T[3],ye=M=T[4];for(var oe,O=0;O<80;O+=1)oe=Z+R[I+ae[O]]|0,O<16?oe+=b(J,X,F)+q[0]:O<32?oe+=S(J,X,F)+q[1]:O<48?oe+=B(J,X,F)+q[2]:O<64?oe+=E(J,X,F)+q[3]:oe+=D(J,X,F)+q[4],oe=oe|0,oe=k(oe,Y[O]),oe=oe+M|0,Z=M,M=F,F=k(X,10),X=J,J=oe,oe=G+R[I+K[O]]|0,O<16?oe+=D(W,fe,ce)+$[0]:O<32?oe+=E(W,fe,ce)+$[1]:O<48?oe+=B(W,fe,ce)+$[2]:O<64?oe+=S(W,fe,ce)+$[3]:oe+=b(W,fe,ce)+$[4],oe=oe|0,oe=k(oe,ne[O]),oe=oe+ye|0,G=ye,ye=ce,ce=k(fe,10),fe=W,W=oe;oe=T[1]+X+ce|0,T[1]=T[2]+F+ye|0,T[2]=T[3]+M+G|0,T[3]=T[4]+Z+W|0,T[4]=T[0]+J+fe|0,T[0]=oe},_doFinalize:function(){var R=this._data,I=R.words,O=this._nDataBytes*8,Q=R.sigBytes*8;I[Q>>>5]|=128<<24-Q%32,I[(Q+64>>>9<<4)+14]=(O<<8|O>>>24)&16711935|(O<<24|O>>>8)&4278255360,R.sigBytes=(I.length+1)*4,this._process();for(var _=this._hash,T=_.words,q=0;q<5;q++){var $=T[q];T[q]=($<<8|$>>>24)&16711935|($<<24|$>>>8)&4278255360}return _},clone:function(){var R=c.clone.call(this);return R._hash=this._hash.clone(),R}});function b(R,I,O){return R^I^O}function S(R,I,O){return R&I|~R&O}function B(R,I,O){return(R|~I)^O}function E(R,I,O){return R&O|I&~O}function D(R,I,O){return R^(I|~O)}function k(R,I){return R<<I|R>>>32-I}s.RIPEMD160=c._createHelper(g),s.HmacRIPEMD160=c._createHmacHelper(g)}(),i.RIPEMD160})}(Us)),Us.exports}var Qs={exports:{}},Qb=Qs.exports,Yh;function Xf(){return Yh||(Yh=1,function(t,n){(function(i,o){t.exports=o(Be())})(Qb,function(i){(function(){var o=i,s=o.lib,u=s.Base,x=o.enc,c=x.Utf8,h=o.algo;h.HMAC=u.extend({init:function(f,p){f=this._hasher=new f.init,typeof p=="string"&&(p=c.parse(p));var m=f.blockSize,y=m*4;p.sigBytes>y&&(p=f.finalize(p)),p.clamp();for(var A=this._oKey=p.clone(),w=this._iKey=p.clone(),g=A.words,b=w.words,S=0;S<m;S++)g[S]^=1549556828,b[S]^=909522486;A.sigBytes=w.sigBytes=y,this.reset()},reset:function(){var f=this._hasher;f.reset(),f.update(this._iKey)},update:function(f){return this._hasher.update(f),this},finalize:function(f){var p=this._hasher,m=p.finalize(f);p.reset();var y=p.finalize(this._oKey.clone().concat(m));return y}})})()})}(Qs)),Qs.exports}var js={exports:{}},jb=js.exports,Xh;function Wb(){return Xh||(Xh=1,function(t,n){(function(i,o,s){t.exports=o(Be(),Yf(),Xf())})(jb,function(i){return function(){var o=i,s=o.lib,u=s.Base,x=s.WordArray,c=o.algo,h=c.SHA256,f=c.HMAC,p=c.PBKDF2=u.extend({cfg:u.extend({keySize:128/32,hasher:h,iterations:25e4}),init:function(m){this.cfg=this.cfg.extend(m)},compute:function(m,y){for(var A=this.cfg,w=f.create(A.hasher,m),g=x.create(),b=x.create([1]),S=g.words,B=b.words,E=A.keySize,D=A.iterations;S.length<E;){var k=w.update(y).finalize(b);w.reset();for(var R=k.words,I=R.length,O=k,Q=1;Q<D;Q++){O=w.finalize(O),w.reset();for(var _=O.words,T=0;T<I;T++)R[T]^=_[T]}g.concat(k),B[0]++}return g.sigBytes=E*4,g}});o.PBKDF2=function(m,y,A){return p.create(A).compute(m,y)}}(),i.PBKDF2})}(js)),js.exports}var Ws={exports:{}},Vb=Ws.exports,Jh;function Qn(){return Jh||(Jh=1,function(t,n){(function(i,o,s){t.exports=o(Be(),W4(),Xf())})(Vb,function(i){return function(){var o=i,s=o.lib,u=s.Base,x=s.WordArray,c=o.algo,h=c.MD5,f=c.EvpKDF=u.extend({cfg:u.extend({keySize:128/32,hasher:h,iterations:1}),init:function(p){this.cfg=this.cfg.extend(p)},compute:function(p,m){for(var y,A=this.cfg,w=A.hasher.create(),g=x.create(),b=g.words,S=A.keySize,B=A.iterations;b.length<S;){y&&w.update(y),y=w.update(p).finalize(m),w.reset();for(var E=1;E<B;E++)y=w.finalize(y),w.reset();g.concat(y)}return g.sigBytes=S*4,g}});o.EvpKDF=function(p,m,y){return f.create(y).compute(p,m)}}(),i.EvpKDF})}(Ws)),Ws.exports}var Vs={exports:{}},qb=Vs.exports,ep;function it(){return ep||(ep=1,function(t,n){(function(i,o,s){t.exports=o(Be(),Qn())})(qb,function(i){i.lib.Cipher||function(o){var s=i,u=s.lib,x=u.Base,c=u.WordArray,h=u.BufferedBlockAlgorithm,f=s.enc;f.Utf8;var p=f.Base64,m=s.algo,y=m.EvpKDF,A=u.Cipher=h.extend({cfg:x.extend(),createEncryptor:function(_,T){return this.create(this._ENC_XFORM_MODE,_,T)},createDecryptor:function(_,T){return this.create(this._DEC_XFORM_MODE,_,T)},init:function(_,T,q){this.cfg=this.cfg.extend(q),this._xformMode=_,this._key=T,this.reset()},reset:function(){h.reset.call(this),this._doReset()},process:function(_){return this._append(_),this._process()},finalize:function(_){_&&this._append(_);var T=this._doFinalize();return T},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function _(T){return typeof T=="string"?Q:R}return function(T){return{encrypt:function(q,$,ae){return _($).encrypt(T,q,$,ae)},decrypt:function(q,$,ae){return _($).decrypt(T,q,$,ae)}}}}()});u.StreamCipher=A.extend({_doFinalize:function(){var _=this._process(!0);return _},blockSize:1});var w=s.mode={},g=u.BlockCipherMode=x.extend({createEncryptor:function(_,T){return this.Encryptor.create(_,T)},createDecryptor:function(_,T){return this.Decryptor.create(_,T)},init:function(_,T){this._cipher=_,this._iv=T}}),b=w.CBC=function(){var _=g.extend();_.Encryptor=_.extend({processBlock:function(q,$){var ae=this._cipher,K=ae.blockSize;T.call(this,q,$,K),ae.encryptBlock(q,$),this._prevBlock=q.slice($,$+K)}}),_.Decryptor=_.extend({processBlock:function(q,$){var ae=this._cipher,K=ae.blockSize,Y=q.slice($,$+K);ae.decryptBlock(q,$),T.call(this,q,$,K),this._prevBlock=Y}});function T(q,$,ae){var K,Y=this._iv;Y?(K=Y,this._iv=o):K=this._prevBlock;for(var ne=0;ne<ae;ne++)q[$+ne]^=K[ne]}return _}(),S=s.pad={},B=S.Pkcs7={pad:function(_,T){for(var q=T*4,$=q-_.sigBytes%q,ae=$<<24|$<<16|$<<8|$,K=[],Y=0;Y<$;Y+=4)K.push(ae);var ne=c.create(K,$);_.concat(ne)},unpad:function(_){var T=_.words[_.sigBytes-1>>>2]&255;_.sigBytes-=T}};u.BlockCipher=A.extend({cfg:A.cfg.extend({mode:b,padding:B}),reset:function(){var _;A.reset.call(this);var T=this.cfg,q=T.iv,$=T.mode;this._xformMode==this._ENC_XFORM_MODE?_=$.createEncryptor:(_=$.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==_?this._mode.init(this,q&&q.words):(this._mode=_.call($,this,q&&q.words),this._mode.__creator=_)},_doProcessBlock:function(_,T){this._mode.processBlock(_,T)},_doFinalize:function(){var _,T=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(T.pad(this._data,this.blockSize),_=this._process(!0)):(_=this._process(!0),T.unpad(_)),_},blockSize:128/32});var E=u.CipherParams=x.extend({init:function(_){this.mixIn(_)},toString:function(_){return(_||this.formatter).stringify(this)}}),D=s.format={},k=D.OpenSSL={stringify:function(_){var T,q=_.ciphertext,$=_.salt;return $?T=c.create([1398893684,1701076831]).concat($).concat(q):T=q,T.toString(p)},parse:function(_){var T,q=p.parse(_),$=q.words;return $[0]==1398893684&&$[1]==1701076831&&(T=c.create($.slice(2,4)),$.splice(0,4),q.sigBytes-=16),E.create({ciphertext:q,salt:T})}},R=u.SerializableCipher=x.extend({cfg:x.extend({format:k}),encrypt:function(_,T,q,$){$=this.cfg.extend($);var ae=_.createEncryptor(q,$),K=ae.finalize(T),Y=ae.cfg;return E.create({ciphertext:K,key:q,iv:Y.iv,algorithm:_,mode:Y.mode,padding:Y.padding,blockSize:_.blockSize,formatter:$.format})},decrypt:function(_,T,q,$){$=this.cfg.extend($),T=this._parse(T,$.format);var ae=_.createDecryptor(q,$).finalize(T.ciphertext);return ae},_parse:function(_,T){return typeof _=="string"?T.parse(_,this):_}}),I=s.kdf={},O=I.OpenSSL={execute:function(_,T,q,$,ae){if($||($=c.random(64/8)),ae)var K=y.create({keySize:T+q,hasher:ae}).compute(_,$);else var K=y.create({keySize:T+q}).compute(_,$);var Y=c.create(K.words.slice(T),q*4);return K.sigBytes=T*4,E.create({key:K,iv:Y,salt:$})}},Q=u.PasswordBasedCipher=R.extend({cfg:R.cfg.extend({kdf:O}),encrypt:function(_,T,q,$){$=this.cfg.extend($);var ae=$.kdf.execute(q,_.keySize,_.ivSize,$.salt,$.hasher);$.iv=ae.iv;var K=R.encrypt.call(this,_,T,ae.key,$);return K.mixIn(ae),K},decrypt:function(_,T,q,$){$=this.cfg.extend($),T=this._parse(T,$.format);var ae=$.kdf.execute(q,_.keySize,_.ivSize,T.salt,$.hasher);$.iv=ae.iv;var K=R.decrypt.call(this,_,T,ae.key,$);return K}})}()})}(Vs)),Vs.exports}var qs={exports:{}},Kb=qs.exports,tp;function $b(){return tp||(tp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(Kb,function(i){return i.mode.CFB=function(){var o=i.lib.BlockCipherMode.extend();o.Encryptor=o.extend({processBlock:function(u,x){var c=this._cipher,h=c.blockSize;s.call(this,u,x,h,c),this._prevBlock=u.slice(x,x+h)}}),o.Decryptor=o.extend({processBlock:function(u,x){var c=this._cipher,h=c.blockSize,f=u.slice(x,x+h);s.call(this,u,x,h,c),this._prevBlock=f}});function s(u,x,c,h){var f,p=this._iv;p?(f=p.slice(0),this._iv=void 0):f=this._prevBlock,h.encryptBlock(f,0);for(var m=0;m<c;m++)u[x+m]^=f[m]}return o}(),i.mode.CFB})}(qs)),qs.exports}var Ks={exports:{}},Gb=Ks.exports,rp;function Zb(){return rp||(rp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(Gb,function(i){return i.mode.CTR=function(){var o=i.lib.BlockCipherMode.extend(),s=o.Encryptor=o.extend({processBlock:function(u,x){var c=this._cipher,h=c.blockSize,f=this._iv,p=this._counter;f&&(p=this._counter=f.slice(0),this._iv=void 0);var m=p.slice(0);c.encryptBlock(m,0),p[h-1]=p[h-1]+1|0;for(var y=0;y<h;y++)u[x+y]^=m[y]}});return o.Decryptor=s,o}(),i.mode.CTR})}(Ks)),Ks.exports}var $s={exports:{}},Yb=$s.exports,np;function Xb(){return np||(np=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(Yb,function(i){/** @preserve
 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
 * derived from CryptoJS.mode.CTR
 * <NAME_EMAIL>
 */return i.mode.CTRGladman=function(){var o=i.lib.BlockCipherMode.extend();function s(c){if((c>>24&255)===255){var h=c>>16&255,f=c>>8&255,p=c&255;h===255?(h=0,f===255?(f=0,p===255?p=0:++p):++f):++h,c=0,c+=h<<16,c+=f<<8,c+=p}else c+=1<<24;return c}function u(c){return(c[0]=s(c[0]))===0&&(c[1]=s(c[1])),c}var x=o.Encryptor=o.extend({processBlock:function(c,h){var f=this._cipher,p=f.blockSize,m=this._iv,y=this._counter;m&&(y=this._counter=m.slice(0),this._iv=void 0),u(y);var A=y.slice(0);f.encryptBlock(A,0);for(var w=0;w<p;w++)c[h+w]^=A[w]}});return o.Decryptor=x,o}(),i.mode.CTRGladman})}($s)),$s.exports}var Gs={exports:{}},Jb=Gs.exports,ip;function e7(){return ip||(ip=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(Jb,function(i){return i.mode.OFB=function(){var o=i.lib.BlockCipherMode.extend(),s=o.Encryptor=o.extend({processBlock:function(u,x){var c=this._cipher,h=c.blockSize,f=this._iv,p=this._keystream;f&&(p=this._keystream=f.slice(0),this._iv=void 0),c.encryptBlock(p,0);for(var m=0;m<h;m++)u[x+m]^=p[m]}});return o.Decryptor=s,o}(),i.mode.OFB})}(Gs)),Gs.exports}var Zs={exports:{}},t7=Zs.exports,ap;function r7(){return ap||(ap=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(t7,function(i){return i.mode.ECB=function(){var o=i.lib.BlockCipherMode.extend();return o.Encryptor=o.extend({processBlock:function(s,u){this._cipher.encryptBlock(s,u)}}),o.Decryptor=o.extend({processBlock:function(s,u){this._cipher.decryptBlock(s,u)}}),o}(),i.mode.ECB})}(Zs)),Zs.exports}var Ys={exports:{}},n7=Ys.exports,op;function i7(){return op||(op=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(n7,function(i){return i.pad.AnsiX923={pad:function(o,s){var u=o.sigBytes,x=s*4,c=x-u%x,h=u+c-1;o.clamp(),o.words[h>>>2]|=c<<24-h%4*8,o.sigBytes+=c},unpad:function(o){var s=o.words[o.sigBytes-1>>>2]&255;o.sigBytes-=s}},i.pad.Ansix923})}(Ys)),Ys.exports}var Xs={exports:{}},a7=Xs.exports,sp;function o7(){return sp||(sp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(a7,function(i){return i.pad.Iso10126={pad:function(o,s){var u=s*4,x=u-o.sigBytes%u;o.concat(i.lib.WordArray.random(x-1)).concat(i.lib.WordArray.create([x<<24],1))},unpad:function(o){var s=o.words[o.sigBytes-1>>>2]&255;o.sigBytes-=s}},i.pad.Iso10126})}(Xs)),Xs.exports}var Js={exports:{}},s7=Js.exports,lp;function l7(){return lp||(lp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(s7,function(i){return i.pad.Iso97971={pad:function(o,s){o.concat(i.lib.WordArray.create([2147483648],1)),i.pad.ZeroPadding.pad(o,s)},unpad:function(o){i.pad.ZeroPadding.unpad(o),o.sigBytes--}},i.pad.Iso97971})}(Js)),Js.exports}var el={exports:{}},u7=el.exports,up;function c7(){return up||(up=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(u7,function(i){return i.pad.ZeroPadding={pad:function(o,s){var u=s*4;o.clamp(),o.sigBytes+=u-(o.sigBytes%u||u)},unpad:function(o){for(var s=o.words,u=o.sigBytes-1,u=o.sigBytes-1;u>=0;u--)if(s[u>>>2]>>>24-u%4*8&255){o.sigBytes=u+1;break}}},i.pad.ZeroPadding})}(el)),el.exports}var tl={exports:{}},f7=tl.exports,cp;function x7(){return cp||(cp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(f7,function(i){return i.pad.NoPadding={pad:function(){},unpad:function(){}},i.pad.NoPadding})}(tl)),tl.exports}var rl={exports:{}},d7=rl.exports,fp;function h7(){return fp||(fp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),it())})(d7,function(i){return function(o){var s=i,u=s.lib,x=u.CipherParams,c=s.enc,h=c.Hex,f=s.format;f.Hex={stringify:function(p){return p.ciphertext.toString(h)},parse:function(p){var m=h.parse(p);return x.create({ciphertext:m})}}}(),i.format.Hex})}(rl)),rl.exports}var nl={exports:{}},p7=nl.exports,xp;function v7(){return xp||(xp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),F0(),k0(),Qn(),it())})(p7,function(i){return function(){var o=i,s=o.lib,u=s.BlockCipher,x=o.algo,c=[],h=[],f=[],p=[],m=[],y=[],A=[],w=[],g=[],b=[];(function(){for(var E=[],D=0;D<256;D++)D<128?E[D]=D<<1:E[D]=D<<1^283;for(var k=0,R=0,D=0;D<256;D++){var I=R^R<<1^R<<2^R<<3^R<<4;I=I>>>8^I&255^99,c[k]=I,h[I]=k;var O=E[k],Q=E[O],_=E[Q],T=E[I]*257^I*16843008;f[k]=T<<24|T>>>8,p[k]=T<<16|T>>>16,m[k]=T<<8|T>>>24,y[k]=T;var T=_*16843009^Q*65537^O*257^k*16843008;A[I]=T<<24|T>>>8,w[I]=T<<16|T>>>16,g[I]=T<<8|T>>>24,b[I]=T,k?(k=O^E[E[E[_^O]]],R^=E[E[R]]):k=R=1}})();var S=[0,1,2,4,8,16,32,64,128,27,54],B=x.AES=u.extend({_doReset:function(){var E;if(!(this._nRounds&&this._keyPriorReset===this._key)){for(var D=this._keyPriorReset=this._key,k=D.words,R=D.sigBytes/4,I=this._nRounds=R+6,O=(I+1)*4,Q=this._keySchedule=[],_=0;_<O;_++)_<R?Q[_]=k[_]:(E=Q[_-1],_%R?R>6&&_%R==4&&(E=c[E>>>24]<<24|c[E>>>16&255]<<16|c[E>>>8&255]<<8|c[E&255]):(E=E<<8|E>>>24,E=c[E>>>24]<<24|c[E>>>16&255]<<16|c[E>>>8&255]<<8|c[E&255],E^=S[_/R|0]<<24),Q[_]=Q[_-R]^E);for(var T=this._invKeySchedule=[],q=0;q<O;q++){var _=O-q;if(q%4)var E=Q[_];else var E=Q[_-4];q<4||_<=4?T[q]=E:T[q]=A[c[E>>>24]]^w[c[E>>>16&255]]^g[c[E>>>8&255]]^b[c[E&255]]}}},encryptBlock:function(E,D){this._doCryptBlock(E,D,this._keySchedule,f,p,m,y,c)},decryptBlock:function(E,D){var k=E[D+1];E[D+1]=E[D+3],E[D+3]=k,this._doCryptBlock(E,D,this._invKeySchedule,A,w,g,b,h);var k=E[D+1];E[D+1]=E[D+3],E[D+3]=k},_doCryptBlock:function(E,D,k,R,I,O,Q,_){for(var T=this._nRounds,q=E[D]^k[0],$=E[D+1]^k[1],ae=E[D+2]^k[2],K=E[D+3]^k[3],Y=4,ne=1;ne<T;ne++){var Z=R[q>>>24]^I[$>>>16&255]^O[ae>>>8&255]^Q[K&255]^k[Y++],J=R[$>>>24]^I[ae>>>16&255]^O[K>>>8&255]^Q[q&255]^k[Y++],X=R[ae>>>24]^I[K>>>16&255]^O[q>>>8&255]^Q[$&255]^k[Y++],F=R[K>>>24]^I[q>>>16&255]^O[$>>>8&255]^Q[ae&255]^k[Y++];q=Z,$=J,ae=X,K=F}var Z=(_[q>>>24]<<24|_[$>>>16&255]<<16|_[ae>>>8&255]<<8|_[K&255])^k[Y++],J=(_[$>>>24]<<24|_[ae>>>16&255]<<16|_[K>>>8&255]<<8|_[q&255])^k[Y++],X=(_[ae>>>24]<<24|_[K>>>16&255]<<16|_[q>>>8&255]<<8|_[$&255])^k[Y++],F=(_[K>>>24]<<24|_[q>>>16&255]<<16|_[$>>>8&255]<<8|_[ae&255])^k[Y++];E[D]=Z,E[D+1]=J,E[D+2]=X,E[D+3]=F},keySize:256/32});o.AES=u._createHelper(B)}(),i.AES})}(nl)),nl.exports}var il={exports:{}},m7=il.exports,dp;function y7(){return dp||(dp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),F0(),k0(),Qn(),it())})(m7,function(i){return function(){var o=i,s=o.lib,u=s.WordArray,x=s.BlockCipher,c=o.algo,h=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],f=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],p=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],m=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],y=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],A=c.DES=x.extend({_doReset:function(){for(var S=this._key,B=S.words,E=[],D=0;D<56;D++){var k=h[D]-1;E[D]=B[k>>>5]>>>31-k%32&1}for(var R=this._subKeys=[],I=0;I<16;I++){for(var O=R[I]=[],Q=p[I],D=0;D<24;D++)O[D/6|0]|=E[(f[D]-1+Q)%28]<<31-D%6,O[4+(D/6|0)]|=E[28+(f[D+24]-1+Q)%28]<<31-D%6;O[0]=O[0]<<1|O[0]>>>31;for(var D=1;D<7;D++)O[D]=O[D]>>>(D-1)*4+3;O[7]=O[7]<<5|O[7]>>>27}for(var _=this._invSubKeys=[],D=0;D<16;D++)_[D]=R[15-D]},encryptBlock:function(S,B){this._doCryptBlock(S,B,this._subKeys)},decryptBlock:function(S,B){this._doCryptBlock(S,B,this._invSubKeys)},_doCryptBlock:function(S,B,E){this._lBlock=S[B],this._rBlock=S[B+1],w.call(this,4,252645135),w.call(this,16,65535),g.call(this,2,858993459),g.call(this,8,16711935),w.call(this,1,1431655765);for(var D=0;D<16;D++){for(var k=E[D],R=this._lBlock,I=this._rBlock,O=0,Q=0;Q<8;Q++)O|=m[Q][((I^k[Q])&y[Q])>>>0];this._lBlock=I,this._rBlock=R^O}var _=this._lBlock;this._lBlock=this._rBlock,this._rBlock=_,w.call(this,1,1431655765),g.call(this,8,16711935),g.call(this,2,858993459),w.call(this,16,65535),w.call(this,4,252645135),S[B]=this._lBlock,S[B+1]=this._rBlock},keySize:64/32,ivSize:64/32,blockSize:64/32});function w(S,B){var E=(this._lBlock>>>S^this._rBlock)&B;this._rBlock^=E,this._lBlock^=E<<S}function g(S,B){var E=(this._rBlock>>>S^this._lBlock)&B;this._lBlock^=E,this._rBlock^=E<<S}o.DES=x._createHelper(A);var b=c.TripleDES=x.extend({_doReset:function(){var S=this._key,B=S.words;if(B.length!==2&&B.length!==4&&B.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var E=B.slice(0,2),D=B.length<4?B.slice(0,2):B.slice(2,4),k=B.length<6?B.slice(0,2):B.slice(4,6);this._des1=A.createEncryptor(u.create(E)),this._des2=A.createEncryptor(u.create(D)),this._des3=A.createEncryptor(u.create(k))},encryptBlock:function(S,B){this._des1.encryptBlock(S,B),this._des2.decryptBlock(S,B),this._des3.encryptBlock(S,B)},decryptBlock:function(S,B){this._des3.decryptBlock(S,B),this._des2.encryptBlock(S,B),this._des1.decryptBlock(S,B)},keySize:192/32,ivSize:64/32,blockSize:64/32});o.TripleDES=x._createHelper(b)}(),i.TripleDES})}(il)),il.exports}var al={exports:{}},g7=al.exports,hp;function b7(){return hp||(hp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),F0(),k0(),Qn(),it())})(g7,function(i){return function(){var o=i,s=o.lib,u=s.StreamCipher,x=o.algo,c=x.RC4=u.extend({_doReset:function(){for(var p=this._key,m=p.words,y=p.sigBytes,A=this._S=[],w=0;w<256;w++)A[w]=w;for(var w=0,g=0;w<256;w++){var b=w%y,S=m[b>>>2]>>>24-b%4*8&255;g=(g+A[w]+S)%256;var B=A[w];A[w]=A[g],A[g]=B}this._i=this._j=0},_doProcessBlock:function(p,m){p[m]^=h.call(this)},keySize:256/32,ivSize:0});function h(){for(var p=this._S,m=this._i,y=this._j,A=0,w=0;w<4;w++){m=(m+1)%256,y=(y+p[m])%256;var g=p[m];p[m]=p[y],p[y]=g,A|=p[(p[m]+p[y])%256]<<24-w*8}return this._i=m,this._j=y,A}o.RC4=u._createHelper(c);var f=x.RC4Drop=c.extend({cfg:c.cfg.extend({drop:192}),_doReset:function(){c._doReset.call(this);for(var p=this.cfg.drop;p>0;p--)h.call(this)}});o.RC4Drop=u._createHelper(f)}(),i.RC4})}(al)),al.exports}var ol={exports:{}},A7=ol.exports,pp;function w7(){return pp||(pp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),F0(),k0(),Qn(),it())})(A7,function(i){return function(){var o=i,s=o.lib,u=s.StreamCipher,x=o.algo,c=[],h=[],f=[],p=x.Rabbit=u.extend({_doReset:function(){for(var y=this._key.words,A=this.cfg.iv,w=0;w<4;w++)y[w]=(y[w]<<8|y[w]>>>24)&16711935|(y[w]<<24|y[w]>>>8)&4278255360;var g=this._X=[y[0],y[3]<<16|y[2]>>>16,y[1],y[0]<<16|y[3]>>>16,y[2],y[1]<<16|y[0]>>>16,y[3],y[2]<<16|y[1]>>>16],b=this._C=[y[2]<<16|y[2]>>>16,y[0]&4294901760|y[1]&65535,y[3]<<16|y[3]>>>16,y[1]&4294901760|y[2]&65535,y[0]<<16|y[0]>>>16,y[2]&4294901760|y[3]&65535,y[1]<<16|y[1]>>>16,y[3]&4294901760|y[0]&65535];this._b=0;for(var w=0;w<4;w++)m.call(this);for(var w=0;w<8;w++)b[w]^=g[w+4&7];if(A){var S=A.words,B=S[0],E=S[1],D=(B<<8|B>>>24)&16711935|(B<<24|B>>>8)&4278255360,k=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360,R=D>>>16|k&4294901760,I=k<<16|D&65535;b[0]^=D,b[1]^=R,b[2]^=k,b[3]^=I,b[4]^=D,b[5]^=R,b[6]^=k,b[7]^=I;for(var w=0;w<4;w++)m.call(this)}},_doProcessBlock:function(y,A){var w=this._X;m.call(this),c[0]=w[0]^w[5]>>>16^w[3]<<16,c[1]=w[2]^w[7]>>>16^w[5]<<16,c[2]=w[4]^w[1]>>>16^w[7]<<16,c[3]=w[6]^w[3]>>>16^w[1]<<16;for(var g=0;g<4;g++)c[g]=(c[g]<<8|c[g]>>>24)&16711935|(c[g]<<24|c[g]>>>8)&4278255360,y[A+g]^=c[g]},blockSize:128/32,ivSize:64/32});function m(){for(var y=this._X,A=this._C,w=0;w<8;w++)h[w]=A[w];A[0]=A[0]+1295307597+this._b|0,A[1]=A[1]+3545052371+(A[0]>>>0<h[0]>>>0?1:0)|0,A[2]=A[2]+886263092+(A[1]>>>0<h[1]>>>0?1:0)|0,A[3]=A[3]+1295307597+(A[2]>>>0<h[2]>>>0?1:0)|0,A[4]=A[4]+3545052371+(A[3]>>>0<h[3]>>>0?1:0)|0,A[5]=A[5]+886263092+(A[4]>>>0<h[4]>>>0?1:0)|0,A[6]=A[6]+1295307597+(A[5]>>>0<h[5]>>>0?1:0)|0,A[7]=A[7]+3545052371+(A[6]>>>0<h[6]>>>0?1:0)|0,this._b=A[7]>>>0<h[7]>>>0?1:0;for(var w=0;w<8;w++){var g=y[w]+A[w],b=g&65535,S=g>>>16,B=((b*b>>>17)+b*S>>>15)+S*S,E=((g&4294901760)*g|0)+((g&65535)*g|0);f[w]=B^E}y[0]=f[0]+(f[7]<<16|f[7]>>>16)+(f[6]<<16|f[6]>>>16)|0,y[1]=f[1]+(f[0]<<8|f[0]>>>24)+f[7]|0,y[2]=f[2]+(f[1]<<16|f[1]>>>16)+(f[0]<<16|f[0]>>>16)|0,y[3]=f[3]+(f[2]<<8|f[2]>>>24)+f[1]|0,y[4]=f[4]+(f[3]<<16|f[3]>>>16)+(f[2]<<16|f[2]>>>16)|0,y[5]=f[5]+(f[4]<<8|f[4]>>>24)+f[3]|0,y[6]=f[6]+(f[5]<<16|f[5]>>>16)+(f[4]<<16|f[4]>>>16)|0,y[7]=f[7]+(f[6]<<8|f[6]>>>24)+f[5]|0}o.Rabbit=u._createHelper(p)}(),i.Rabbit})}(ol)),ol.exports}var sl={exports:{}},E7=sl.exports,vp;function C7(){return vp||(vp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),F0(),k0(),Qn(),it())})(E7,function(i){return function(){var o=i,s=o.lib,u=s.StreamCipher,x=o.algo,c=[],h=[],f=[],p=x.RabbitLegacy=u.extend({_doReset:function(){var y=this._key.words,A=this.cfg.iv,w=this._X=[y[0],y[3]<<16|y[2]>>>16,y[1],y[0]<<16|y[3]>>>16,y[2],y[1]<<16|y[0]>>>16,y[3],y[2]<<16|y[1]>>>16],g=this._C=[y[2]<<16|y[2]>>>16,y[0]&4294901760|y[1]&65535,y[3]<<16|y[3]>>>16,y[1]&4294901760|y[2]&65535,y[0]<<16|y[0]>>>16,y[2]&4294901760|y[3]&65535,y[1]<<16|y[1]>>>16,y[3]&4294901760|y[0]&65535];this._b=0;for(var b=0;b<4;b++)m.call(this);for(var b=0;b<8;b++)g[b]^=w[b+4&7];if(A){var S=A.words,B=S[0],E=S[1],D=(B<<8|B>>>24)&16711935|(B<<24|B>>>8)&4278255360,k=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360,R=D>>>16|k&4294901760,I=k<<16|D&65535;g[0]^=D,g[1]^=R,g[2]^=k,g[3]^=I,g[4]^=D,g[5]^=R,g[6]^=k,g[7]^=I;for(var b=0;b<4;b++)m.call(this)}},_doProcessBlock:function(y,A){var w=this._X;m.call(this),c[0]=w[0]^w[5]>>>16^w[3]<<16,c[1]=w[2]^w[7]>>>16^w[5]<<16,c[2]=w[4]^w[1]>>>16^w[7]<<16,c[3]=w[6]^w[3]>>>16^w[1]<<16;for(var g=0;g<4;g++)c[g]=(c[g]<<8|c[g]>>>24)&16711935|(c[g]<<24|c[g]>>>8)&4278255360,y[A+g]^=c[g]},blockSize:128/32,ivSize:64/32});function m(){for(var y=this._X,A=this._C,w=0;w<8;w++)h[w]=A[w];A[0]=A[0]+1295307597+this._b|0,A[1]=A[1]+3545052371+(A[0]>>>0<h[0]>>>0?1:0)|0,A[2]=A[2]+886263092+(A[1]>>>0<h[1]>>>0?1:0)|0,A[3]=A[3]+1295307597+(A[2]>>>0<h[2]>>>0?1:0)|0,A[4]=A[4]+3545052371+(A[3]>>>0<h[3]>>>0?1:0)|0,A[5]=A[5]+886263092+(A[4]>>>0<h[4]>>>0?1:0)|0,A[6]=A[6]+1295307597+(A[5]>>>0<h[5]>>>0?1:0)|0,A[7]=A[7]+3545052371+(A[6]>>>0<h[6]>>>0?1:0)|0,this._b=A[7]>>>0<h[7]>>>0?1:0;for(var w=0;w<8;w++){var g=y[w]+A[w],b=g&65535,S=g>>>16,B=((b*b>>>17)+b*S>>>15)+S*S,E=((g&4294901760)*g|0)+((g&65535)*g|0);f[w]=B^E}y[0]=f[0]+(f[7]<<16|f[7]>>>16)+(f[6]<<16|f[6]>>>16)|0,y[1]=f[1]+(f[0]<<8|f[0]>>>24)+f[7]|0,y[2]=f[2]+(f[1]<<16|f[1]>>>16)+(f[0]<<16|f[0]>>>16)|0,y[3]=f[3]+(f[2]<<8|f[2]>>>24)+f[1]|0,y[4]=f[4]+(f[3]<<16|f[3]>>>16)+(f[2]<<16|f[2]>>>16)|0,y[5]=f[5]+(f[4]<<8|f[4]>>>24)+f[3]|0,y[6]=f[6]+(f[5]<<16|f[5]>>>16)+(f[4]<<16|f[4]>>>16)|0,y[7]=f[7]+(f[6]<<8|f[6]>>>24)+f[5]|0}o.RabbitLegacy=u._createHelper(p)}(),i.RabbitLegacy})}(sl)),sl.exports}var ll={exports:{}},B7=ll.exports,mp;function S7(){return mp||(mp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),F0(),k0(),Qn(),it())})(B7,function(i){return function(){var o=i,s=o.lib,u=s.BlockCipher,x=o.algo;const c=16,h=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],f=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var p={pbox:[],sbox:[]};function m(b,S){let B=S>>24&255,E=S>>16&255,D=S>>8&255,k=S&255,R=b.sbox[0][B]+b.sbox[1][E];return R=R^b.sbox[2][D],R=R+b.sbox[3][k],R}function y(b,S,B){let E=S,D=B,k;for(let R=0;R<c;++R)E=E^b.pbox[R],D=m(b,E)^D,k=E,E=D,D=k;return k=E,E=D,D=k,D=D^b.pbox[c],E=E^b.pbox[c+1],{left:E,right:D}}function A(b,S,B){let E=S,D=B,k;for(let R=c+1;R>1;--R)E=E^b.pbox[R],D=m(b,E)^D,k=E,E=D,D=k;return k=E,E=D,D=k,D=D^b.pbox[1],E=E^b.pbox[0],{left:E,right:D}}function w(b,S,B){for(let I=0;I<4;I++){b.sbox[I]=[];for(let O=0;O<256;O++)b.sbox[I][O]=f[I][O]}let E=0;for(let I=0;I<c+2;I++)b.pbox[I]=h[I]^S[E],E++,E>=B&&(E=0);let D=0,k=0,R=0;for(let I=0;I<c+2;I+=2)R=y(b,D,k),D=R.left,k=R.right,b.pbox[I]=D,b.pbox[I+1]=k;for(let I=0;I<4;I++)for(let O=0;O<256;O+=2)R=y(b,D,k),D=R.left,k=R.right,b.sbox[I][O]=D,b.sbox[I][O+1]=k;return!0}var g=x.Blowfish=u.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var b=this._keyPriorReset=this._key,S=b.words,B=b.sigBytes/4;w(p,S,B)}},encryptBlock:function(b,S){var B=y(p,b[S],b[S+1]);b[S]=B.left,b[S+1]=B.right},decryptBlock:function(b,S){var B=A(p,b[S],b[S+1]);b[S]=B.left,b[S+1]=B.right},blockSize:64/32,keySize:128/32,ivSize:64/32});o.Blowfish=u._createHelper(g)}(),i.Blowfish})}(ll)),ll.exports}var D7=Ds.exports,yp;function F7(){return yp||(yp=1,function(t,n){(function(i,o,s){t.exports=o(Be(),Dl(),Cb(),Sb(),F0(),kb(),k0(),W4(),Yf(),Ob(),V4(),Nb(),zb(),Ub(),Xf(),Wb(),Qn(),it(),$b(),Zb(),Xb(),e7(),r7(),i7(),o7(),l7(),c7(),x7(),h7(),v7(),y7(),b7(),w7(),C7(),S7())})(D7,function(i){return i})}(Ds)),Ds.exports}var k7=F7();const hA=yl(k7);export{Pm as $,zf as A,Nf as B,J6 as C,j2 as D,X7 as E,oA as F,sA as G,uA as H,j6 as I,lA as J,N9 as K,fA as L,_4 as M,P4 as N,xA as O,ht as P,V3 as Q,Rf as R,K6 as S,Xr as T,W6 as U,ry as V,vb as W,_7 as X,eA as Y,rA as Z,P7 as _,q7 as a,tA as a0,L2 as a1,hA as a2,iA as a3,pb as a4,O7 as a5,I7 as a6,L7 as a7,nA as a8,dA as a9,K7 as b,V7 as c,Z6 as d,W7 as e,qc as f,G6 as g,$6 as h,q6 as i,e5 as j,ki as k,X6 as l,Y6 as m,Y1 as n,$7 as o,z7 as p,Y7 as q,Z7 as r,G7 as s,j7 as t,U as u,N7 as v,J7 as w,T7 as x,Up as y,aA as z};
