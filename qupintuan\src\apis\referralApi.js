// src/apis/referralApi.js
// 推荐关系相关的 API
import { readContract } from '@wagmi/core';

/**
 * 获取合约地址和 ABI
 */
async function getAgentSystemContract() {
  const { getContractAddress } = await import('@/contracts/addresses');
  const { ABIS } = await import('@/contracts/index');

  const contractAddress = getContractAddress(97, 'AgentSystem'); // BSC 测试网
  const AgentSystemABI = ABIS.AgentSystem;

  return { contractAddress, AgentSystemABI };
}

/**
 * 获取 wagmi 配置
 */
async function getWagmiConfig() {
  const { config } = await import('@/wagmi.config');
  return config;
}

/**
 * 获取用户的推荐人
 * @param {string} userAddress - 用户地址
 * @returns {Promise<string>} 推荐人地址
 */
export async function getUserReferrer(userAddress) {
  try {
    // 获取合约信息
    const { contractAddress, AgentSystemABI } = await getAgentSystemContract();
    const config = await getWagmiConfig();

    // 优先尝试使用新的 getUserInfo 函数
    try {
      const userInfo = await readContract(config, {
        address: contractAddress,
        abi: AgentSystemABI,
        functionName: 'getUserInfo',
        args: [userAddress],
      });

      const referrer = userInfo[0]; // inviter 是第一个字段

      // 如果返回零地址，说明没有推荐人
      if (referrer === '0x0000000000000000000000000000000000000000') {
        return null;
      }

      return referrer;
    } catch (getUserInfoError) {
      console.warn('⚠️ [getUserReferrer] getUserInfo 调用失败，回退到 users mapping:', getUserInfoError.message);

      // 回退到 users mapping，但要处理可能的存储结构问题
      try {
        // 先检查用户是否注册
        const isRegistered = await readContract(config, {
          address: contractAddress,
          abi: AgentSystemABI,
          functionName: 'isAgent',
          args: [userAddress],
        });

        if (!isRegistered) {
          console.log('ℹ️ [getUserReferrer] 用户未注册，没有推荐人');
          return null;
        }

        // 尝试使用 users mapping，但要捕获存储结构错误
        const userInfo = await readContract(config, {
          address: contractAddress,
          abi: AgentSystemABI,
          functionName: 'users',
          args: [userAddress],
        });

        const referrer = userInfo[0]; // inviter 是第一个字段

        // 如果返回零地址，说明没有推荐人
        if (referrer === '0x0000000000000000000000000000000000000000') {
          return null;
        }

        return referrer;
      } catch (usersError) {
        console.warn('⚠️ [getUserReferrer] users mapping 调用失败，可能是合约存储结构变化:', usersError.message);

        // 如果是存储结构问题，返回 null 而不是抛出错误
        if (usersError.message.includes('out of bounds') || usersError.message.includes('Position')) {
          console.log('ℹ️ [getUserReferrer] 检测到存储结构变化，跳过推荐人查询');
          return null;
        }

        return null;
      }
    }
  } catch (error) {
    console.error('获取推荐人失败:', error);
    return null;
  }
}

/**
 * 检查用户是否为指定地址的推荐人
 * @param {string} userAddress - 用户地址
 * @param {string} referredAddress - 被推荐人地址
 * @returns {Promise<boolean>} 是否为推荐人
 */
export async function isUserReferrer(userAddress, referredAddress) {
  try {
    const referrer = await getUserReferrer(referredAddress);
    return referrer && referrer.toLowerCase() === userAddress.toLowerCase();
  } catch (error) {
    console.error('检查推荐关系失败:', error);
    return false;
  }
}

/**
 * 获取用户的直推列表
 * @param {string} userAddress - 用户地址
 * @returns {Promise<string[]>} 直推用户地址列表
 */
export async function getUserDirectReferrals(userAddress) {
  try {
    // 获取合约信息
    const { contractAddress, AgentSystemABI } = await getAgentSystemContract();
    const config = await getWagmiConfig();

    // 尝试调用 getUserReferrals 函数
    try {
      const referralAddresses = await readContract(config, {
        address: contractAddress,
        abi: AgentSystemABI,
        functionName: 'getUserReferrals',
        args: [userAddress],
      });

      return referralAddresses || [];
    } catch (error) {
      console.warn('getUserReferrals 函数不存在，返回空数组:', error.message);
      return [];
    }
  } catch (error) {
    console.error('获取直推列表失败:', error);
    return [];
  }
}

/**
 * 获取用户的团队总数
 * @param {string} userAddress - 用户地址
 * @returns {Promise<number>} 团队总人数
 */
export async function getUserTeamCount(userAddress) {
  try {
    // 获取合约信息
    const { contractAddress, AgentSystemABI } = await getAgentSystemContract();
    const config = await getWagmiConfig();

    // 尝试调用 getTeamStats 函数
    try {
      const teamStats = await readContract(config, {
        address: contractAddress,
        abi: AgentSystemABI,
        functionName: 'getTeamStats',
        args: [userAddress],
      });

      // 修复：返回 teamStats[1] (totalCount) 而不是 teamStats[0] (directCount)
      // getTeamStats 返回: (directCount, totalCount, teamPerf, personalPerf)
      return Number(teamStats[1]) || 0; // 返回团队总人数（20层以内）
    } catch (error) {
      console.warn('getTeamStats 函数调用失败:', error.message);
      return 0;
    }
  } catch (error) {
    console.error('获取团队总数失败:', error);
    return 0;
  }
}

/**
 * 获取推荐关系链
 * @param {string} userAddress - 用户地址
 * @param {number} levels - 查询层级数（默认3级）
 * @returns {Promise<string[]>} 推荐关系链（从直接推荐人到顶级）
 */
export async function getReferralChain(userAddress, levels = 3) {
  try {
    const chain = [];
    let currentAddress = userAddress;

    for (let i = 0; i < levels; i++) {
      const referrer = await getUserReferrer(currentAddress);
      if (!referrer) {
        break;
      }

      chain.push(referrer);
      currentAddress = referrer;
    }

    return chain;
  } catch (error) {
    console.error('获取推荐关系链失败:', error);
    return [];
  }
}

/**
 * 格式化地址显示
 * @param {string} address - 地址
 * @returns {string} 格式化后的地址
 */
export function formatAddress(address) {
  if (!address) return '';
  if (address.length <= 10) return address;
  return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
}

/**
 * 获取用户显示名称（优先使用昵称，否则使用格式化地址）
 * @param {string} address - 用户地址
 * @returns {Promise<string>} 显示名称
 */
export async function getUserDisplayName(address) {
  try {
    // TODO: 如果有用户系统，可以查询昵称
    // const { getUserProfile } = await import('@/apis/userApi');
    // const profile = await getUserProfile({ address });
    // return profile.nickname || formatAddress(address);

    // 临时使用格式化地址
    return formatAddress(address);
  } catch (error) {
    return formatAddress(address);
  }
}

/**
 * 检查并通知推荐人奖励
 * @param {string} winnerAddress - 赢家地址
 * @param {Object} room - 房间信息
 * @returns {Promise<Object|null>} 推荐人信息（如果存在）
 */
export async function checkAndNotifyReferrerReward(winnerAddress, room) {
  try {
    const referrer = await getUserReferrer(winnerAddress);

    if (!referrer) {
      return null;
    }

    const referrerName = await getUserDisplayName(referrer);
    const winnerName = await getUserDisplayName(winnerAddress);

    return {
      referrerAddress: referrer,
      referrerName,
      winnerAddress,
      winnerName,
      room,
      qptReward: getQPTReward(parseFloat(room.tierDisplay || room.tier || 0))
    };
  } catch (error) {
    console.error('检查推荐人奖励失败:', error);
    return null;
  }
}

/**
 * 获取QPT奖励数量（从奖励配置中导入）
 */
function getQPTReward(tierAmount) {
  const QPT_REWARDS = {
    30: 1.5,
    50: 2.5,
    100: 5,
    200: 10,
    500: 25,
    1000: 50
  };

  return QPT_REWARDS[tierAmount] || 0;
}
