import React, { useEffect, useState } from "react";
import { useAccount } from "wagmi";
import {
  checkUserRegisteredOnChain,
  getUserInfoOnChain,
} from "@/apis/adminApi";
import { getSystemAdmin, getUserReferralsByContract, getTeamStats } from "@/apis/agentSystemApi";
import UserRegistration from "@/components/UserRegistration";
import "./index.css";

// 格式化地址
function formatAddress(address) {
  if (!address) return "-";
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

// 格式化USDT金额（USDT是6位小数）
function formatUSDTAmount(amount) {
  if (!amount || amount === 0 || amount === '0') return '0';

  let numAmount;

  // 处理不同类型的输入
  if (typeof amount === 'bigint') {
    numAmount = Number(amount.toString());
  } else if (typeof amount === 'string') {
    numAmount = parseFloat(amount);
  } else if (typeof amount === 'object' && amount.toString) {
    numAmount = Number(amount.toString());
  } else {
    numAmount = Number(amount);
  }

  // 验证转换结果
  if (isNaN(numAmount) || numAmount === 0) return '0';

  // USDT是6位小数，所以需要除以10^6
  const formattedAmount = numAmount / 1000000;

  // 格式化显示，保留合适的小数位
  if (formattedAmount >= 1000) {
    return formattedAmount.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  } else if (formattedAmount >= 1) {
    return formattedAmount.toFixed(2);
  } else {
    return formattedAmount.toFixed(6);
  }
}

export default function UserInfo() {
  const { address, isConnected } = useAccount();
  const [isChecking, setIsChecking] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [systemAdmin, setSystemAdmin] = useState("");
  const [referralsList, setReferralsList] = useState([]);
  const [isLoadingReferrals, setIsLoadingReferrals] = useState(false);
  const [teamStats, setTeamStats] = useState(null);

  // 获取直推用户列表（优化版本）
  const loadReferralsList = async (userAddress) => {
    if (!userAddress) return;
    setIsLoadingReferrals(true);
    try {
      // 使用缓存优化性能
      const referrals = await getUserReferralsByContract({
        userAddress,
        useCache: true
      });
      setReferralsList(referrals || []);

    } catch (error) {
      console.error('获取直推用户列表失败:', error);
      setReferralsList([]);
      // 可以添加toast提示用户
      if (error.message && !error.message.includes('网络')) {
        console.warn('直推用户列表加载失败，请稍后重试');
      }
    } finally {
      setIsLoadingReferrals(false);
    }
  };

  // 获取团队统计信息（使用合约的getTeamStats函数，统计20层以内）
  const loadTeamStats = async (userAddress) => {
    if (!userAddress) return;
    try {
      const stats = await getTeamStats({ userAddress });
      setTeamStats(stats);
    } catch (error) {
      console.error('获取团队统计失败:', error);
      setTeamStats(null);
    }
  };

  useEffect(() => {
    async function fetchUserInfo() {
      if (!address || !isConnected) {
        setIsRegistered(false);
        setUserInfo(null);
        setSystemAdmin("");
        return;
      }
      setIsChecking(true);
      try {
        const [registered, info, admin] = await Promise.all([
          checkUserRegisteredOnChain(address),
          getUserInfoOnChain(address),
          getSystemAdmin().catch(() => ""),
        ]);
        setIsRegistered(!!registered);
        setUserInfo(info);
        setSystemAdmin(admin);

        // 如果用户已注册，加载直推用户列表和团队统计
        if (registered && info) {
          // 并行加载直推用户列表和团队统计，提高性能
          const loadPromises = [];

          if (info.referralsCount > 0) {
            loadPromises.push(loadReferralsList(address));
          } else {
            setReferralsList([]);
          }

          // 加载团队统计（20层以内）
          loadPromises.push(loadTeamStats(address));

          // 并行执行，不等待完成
          Promise.allSettled(loadPromises).then(results => {
            const failedCount = results.filter(r => r.status === 'rejected').length;
            if (failedCount > 0) {
  
            }
          });
        } else {
          setReferralsList([]);
          setTeamStats(null);
        }
      } catch {
        setIsRegistered(false);
        setUserInfo(null);
        setSystemAdmin("");
        setReferralsList([]);
        setTeamStats(null);
      } finally {
        setIsChecking(false);
      }
    }
    fetchUserInfo();
  }, [address, isConnected]);

  // 未连接钱包
  if (!isConnected) {
    return (
      <div className="user-info-block">
        <div className="info-row">
          <span className="label">💳 钱包连接状态</span>
          <span className="value not-connected">未连接</span>
        </div>
      </div>
    );
  }

  // 加载中
  if (isChecking) {
    return (
      <div className="user-info-block">
        <div className="info-row">
          <span className="label">🔄 正在查询注册状态...</span>
        </div>
      </div>
    );
  }

  // 未注册
  if (!isRegistered) {
    return (
      <div className="user-info-block">
        <div className="info-row">
          <span className="label">💳 钱包地址</span>
          <span className="value">{formatAddress(address)}</span>
        </div>
        <div className="info-row">
          <span className="label">❌ 注册状态</span>
          <span className="value not-registered">未注册</span>
        </div>
        <div className="info-row">
          <span className="label">🎭 当前身份</span>
          <span className="value">普通用户</span>
        </div>
        <div className="info-row info-tip">
          请前往下方“用户注册”区块完成代理系统注册
        </div>
      </div>
    );
  }

  // 已注册，展示详细信息
  return (
    <div className="user-info-block">
      <div className="info-row">
        <span className="label">💳 钱包地址</span>
        <span className="value">{formatAddress(address)}</span>
      </div>
      <div className="info-row">
        <span className="label">✅ 注册状态</span>
        <span className="value registered">已注册</span>
      </div>
      <div className="info-row">
        <span className="label">👥 推荐人</span>
        <span className="value">
          {userInfo && userInfo.inviter === systemAdmin
            ? `${formatAddress(userInfo.inviter)} (系统管理员)`
            : formatAddress(userInfo?.inviter)}
        </span>
      </div>
      <div className="info-row">
        <span className="label">🏆 代理级别</span>
        <span className="value level-badge">Level {userInfo?.level}</span>
      </div>
      <div className="info-row">
        <span className="label">💰 个人业绩</span>
        <span className="value performance-value">
          {formatUSDTAmount(userInfo?.personalPerformance)} USDT
        </span>
      </div>
      <div className="info-row">
        <span className="label">👨‍👩‍👧‍👦 直推人数</span>
        <span className="value team-count">
          {teamStats?.directCount || referralsList.length || 0} 人
        </span>
      </div>
      <div className="info-row">
        <span className="label">🌐 团队总人数</span>
        <span className="value team-total-count">
          {teamStats?.totalMembers || 0} 人
          <span className="team-stats-note">(20层以内)</span>
        </span>
      </div>
      <div className="info-row">
        <span className="label">📈 团队业绩</span>
        <span className="value performance-value">
          {formatUSDTAmount(teamStats?.teamPerformance || userInfo?.totalPerformance || 0)} USDT
        </span>
      </div>
      <div className="info-row">
        <span className="label">🎭 当前身份</span>
        <span className="value">
          {userInfo?.level >= 10
            ? "管理员"
            : userInfo?.level > 0
              ? "代理"
              : "普通用户"}
        </span>
      </div>

      {/* 直推用户列表 */}
      {(referralsList.length > 0 || isLoadingReferrals) && (
        <div className="referrals-section">
          <div className="section-header">
            <span className="label">👥 直推用户列表</span>
            <span className="referrals-count">
              {isLoadingReferrals ? '加载中...' : `${referralsList.length} 人`}
            </span>
          </div>

          {isLoadingReferrals ? (
            <div className="loading-message">正在加载直推用户列表...</div>
          ) : referralsList.length > 0 ? (
            <div className="referrals-list">
              {referralsList.map((referral, index) => (
                <div key={referral.address} className="referral-item">
                  <div className="referral-header">
                    <span className="referral-index">#{index + 1}</span>
                    <span className="referral-address">{formatAddress(referral.address)}</span>
                    <span className={`referral-status ${referral.isRegistered ? 'active' : 'inactive'}`}>
                      {referral.isRegistered ? '✅ 已激活' : '⏳ 未激活'}
                    </span>
                  </div>
                  {referral.isRegistered && (
                    <div className="referral-details">
                      <div className="detail-item">
                        <span className="detail-label">等级:</span>
                        <span className="detail-value">Level {referral.level}</span>
                      </div>
                      <div className="detail-item">
                        <span className="detail-label">团队业绩:</span>
                        <span className="detail-value">{formatUSDTAmount(BigInt(referral.personalPerformance || 0) + BigInt(referral.totalPerformance || 0))} USDT</span>
                      </div>
                      <div className="detail-item">
                        <span className="detail-label">团队人数:</span>
                        <span className="detail-value">{referral.teamMemberCount || 0} 人</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="empty-message">暂无直推用户</div>
          )}
        </div>
      )}
    </div>
  );
}
